# -*- coding: utf-8 -*-
"""
운동 기능 테스트를 위한 샘플 데이터 추가
일일소모량을 넘기는 고칼로리 음식들을 오늘 날짜로 추가
"""

import json
from datetime import datetime, timedelta
from pathlib import Path
import logging

# 로깅 설정
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def add_high_calorie_sample_data():
    """일일소모량을 넘기는 고칼로리 샘플 데이터 추가"""
    
    # 고칼로리 음식 데이터 (TDEE 2000을 넘기기 위해 총 2500칼로리 정도)
    sample_foods = [
        {
            'restaurant_name': '맥도날드',
            'food_name': '빅맥 세트',
            'food_name_ko': '빅맥 세트',
            'food_name_en': 'Big Mac Set',
            'calories': 1100,  # 빅맥 + 후렌치후라이 + 콜라
            'protein': 27,
            'carbs': 70,
            'fat': 43,
            'weight_grams': 400,
            'nutrition_source': 'sample_data',
            'confidence': 1.0,
            'timestamp': datetime.now().replace(hour=12, minute=30).isoformat(),  # 오늘 점심
            'image_path': None,
            'method': 'sample_data'
        },
        {
            'restaurant_name': '피자헛',
            'food_name': '콤비네이션 피자 (라지)',
            'food_name_ko': '콤비네이션 피자',
            'food_name_en': 'Combination Pizza Large',
            'calories': 800,
            'protein': 35,
            'carbs': 85,
            'fat': 35,
            'weight_grams': 300,
            'nutrition_source': 'sample_data',
            'confidence': 1.0,
            'timestamp': datetime.now().replace(hour=18, minute=0).isoformat(),  # 오늘 저녁
            'image_path': None,
            'method': 'sample_data'
        },
        {
            'restaurant_name': '스타벅스',
            'food_name': '화이트 초콜릿 모카 + 스콘',
            'food_name_ko': '화이트 초콜릿 모카 + 스콘',
            'food_name_en': 'White Chocolate Mocha + Scone',
            'calories': 650,  # 모카 400 + 스콘 250
            'protein': 12,
            'carbs': 78,
            'fat': 28,
            'weight_grams': 250,
            'nutrition_source': 'sample_data',
            'confidence': 1.0,
            'timestamp': datetime.now().replace(hour=15, minute=30).isoformat(),  # 오늘 오후
            'image_path': None,
            'method': 'sample_data'
        }
    ]
    
    try:
        # 기존 음식 기록 로드
        records_file = Path('food_records.json')
        if records_file.exists():
            with open(records_file, 'r', encoding='utf-8') as f:
                records = json.load(f)
        else:
            records = []
        
        # 샘플 데이터 추가
        start_id = len(records) + 1
        total_calories = 0
        
        for i, food in enumerate(sample_foods):
            food['id'] = start_id + i
            records.append(food)
            total_calories += food['calories']
            logger.info(f"✅ 샘플 음식 추가: {food['food_name']} ({food['calories']} kcal)")
        
        # 파일 저장
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        
        logger.info(f"🎉 샘플 데이터 추가 완료!")
        logger.info(f"📊 오늘 총 칼로리: {total_calories} kcal")
        logger.info(f"💪 TDEE 2000을 {total_calories - 2000} kcal 초과 → 운동 추천 활성화!")
        
        return {
            'success': True,
            'added_count': len(sample_foods),
            'total_calories': total_calories,
            'excess_calories': total_calories - 2000
        }
        
    except Exception as e:
        logger.error(f"❌ 샘플 데이터 추가 실패: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def add_sample_profile():
    """테스트용 샘플 프로필 추가"""
    
    sample_profile = {
        'current_weight': 75,
        'height': 175,
        'age': 28,
        'gender': 'male',
        'activity_level': 'moderate',
        'target_weight': 70,
        'target_date': (datetime.now() + timedelta(days=60)).strftime('%Y-%m-%d'),
        'bmr': 1681.0,  # 계산된 BMR
        'tdee': 2605.5,  # 계산된 TDEE  
        'daily_calorie_goal': 2105.5,  # 주당 0.5kg 감량 목표
        'days_remaining': 60,
        'weight_change_needed': -5.0,
        'created_at': datetime.now().isoformat(),
        'updated_at': datetime.now().isoformat()
    }
    
    try:
        profile_file = Path('user_profile.json')
        
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(sample_profile, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 샘플 프로필 생성 완료")
        logger.info(f"👤 체중: {sample_profile['current_weight']}kg → {sample_profile['target_weight']}kg")
        logger.info(f"🔥 일일 목표: {sample_profile['daily_calorie_goal']} kcal")
        logger.info(f"📅 목표일: {sample_profile['target_date']}")
        
        return {
            'success': True,
            'profile': sample_profile
        }
        
    except Exception as e:
        logger.error(f"❌ 샘플 프로필 생성 실패: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def clear_existing_data():
    """기존 데이터 삭제 (옵션)"""
    try:
        # 음식 기록 삭제
        records_file = Path('food_records.json')
        if records_file.exists():
            records_file.unlink()
            logger.info("🗑️ 기존 음식 기록 삭제됨")
        
        # 프로필 삭제
        profile_file = Path('user_profile.json')
        if profile_file.exists():
            profile_file.unlink()
            logger.info("🗑️ 기존 프로필 삭제됨")
        
        return True
    except Exception as e:
        logger.error(f"❌ 데이터 삭제 실패: {e}")
        return False

if __name__ == "__main__":
    print("🚀 운동 기능 테스트를 위한 샘플 데이터 생성")
    print("=" * 50)
    
    # 옵션: 기존 데이터 삭제 (원하는 경우 주석 해제)
    # clear_existing_data()
    
    # 샘플 프로필 추가
    profile_result = add_sample_profile()
    if profile_result['success']:
        print("✅ 샘플 프로필 생성 완료")
    else:
        print(f"❌ 프로필 생성 실패: {profile_result['error']}")
    
    # 고칼로리 샘플 데이터 추가
    food_result = add_high_calorie_sample_data()
    if food_result['success']:
        print(f"✅ 고칼로리 샘플 음식 {food_result['added_count']}개 추가")
        print(f"📊 오늘 총 섭취: {food_result['total_calories']} kcal")
        print(f"💪 초과 칼로리: {food_result['excess_calories']} kcal")
        print("🏃‍♂️ 이제 운동 추천 기능을 테스트할 수 있습니다!")
    else:
        print(f"❌ 음식 데이터 추가 실패: {food_result['error']}")
    
    print("\n🌐 테스트 방법:")
    print("1. 브라우저에서 http://localhost:5000/dashboard 접속")
    print("2. 오늘 칼로리가 2500+ kcal로 표시되는지 확인")
    print("3. http://localhost:5000/exercise 에서 운동 추천 확인")
    print("4. 초과 칼로리에 따른 운동 시간 계산 확인")

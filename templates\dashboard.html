<!DOCTYPE html>
<html>
<head>
    <title>칼로리 대시보드 - AI 칼로리 관리</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: #1d1d1f;
            min-height: 100vh;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px 0;
        }
        
        .header h1 {
            color: #1d1d1f;
            margin: 0 0 10px 0;
            font-size: 48px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }
        
        .header p {
            color: #86868b;
            margin: 0;
            font-size: 18px;
            font-weight: 400;
        }
        
        .api-status-banner {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #d2d2d7;
            border-radius: 18px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: center;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
        }
        
        .api-status-banner.warning {
            background: rgba(255, 204, 0, 0.1);
            border-color: #ff9500;
            color: #1d1d1f;
        }
        
        .api-status-banner.error {
            background: rgba(255, 59, 48, 0.1);
            border-color: #ff3b30;
            color: #1d1d1f;
        }
        
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .card h2 {
            margin: 0 0 24px 0;
            color: #1d1d1f;
            font-size: 22px;
            font-weight: 600;
            border-bottom: 1px solid #f2f2f7;
            padding-bottom: 12px;
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .metric-item {
            text-align: center;
            padding: 24px;
            background: #f2f2f7;
            border-radius: 16px;
            border-left: 4px solid #007aff;
            transition: all 0.3s ease;
        }
        
        .metric-item:hover {
            background: #e8e8ed;
            transform: scale(1.02);
        }
        
        .metric-value {
            font-size: 32px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 8px;
        }
        
        .metric-label {
            color: #86868b;
            font-size: 14px;
            font-weight: 500;
        }
        
        .weekly-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 12px;
            margin-top: 20px;
        }
        
        .day-item {
            text-align: center;
            padding: 16px 8px;
            background: #f2f2f7;
            border-radius: 12px;
            border: 1px solid transparent;
            transition: all 0.3s ease;
        }
        
        .day-item:hover {
            background: #e8e8ed;
            transform: translateY(-1px);
        }
        
        .day-item.today {
            background: rgba(0, 122, 255, 0.1);
            border-color: #007aff;
            color: #007aff;
        }
        
        .day-name {
            font-size: 12px;
            color: #86868b;
            margin-bottom: 6px;
            font-weight: 500;
        }
        
        .day-calories {
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .day-item.today .day-calories {
            color: #007aff;
        }
        
        .food-list {
            max-height: 320px;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .food-list::-webkit-scrollbar {
            display: none;
        }
        
        .food-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            margin-bottom: 12px;
            background: #f2f2f7;
            border-radius: 16px;
            border-left: 3px solid #34c759;
            transition: all 0.3s ease;
        }
        
        .food-item:hover {
            background: #e8e8ed;
            transform: translateX(4px);
        }
        
        .food-info {
            flex: 1;
        }
        
        .food-name {
            font-weight: 600;
            margin-bottom: 6px;
            color: #1d1d1f;
            font-size: 16px;
        }
        
        .food-details {
            font-size: 13px;
            color: #86868b;
            line-height: 1.4;
        }
        
        .food-calories {
            font-size: 18px;
            font-weight: 700;
            color: #ff3b30;
            text-align: right;
        }
        
        .nutrition-source-badge {
            display: inline-block;
            font-size: 10px;
            padding: 4px 8px;
            border-radius: 8px;
            margin-left: 8px;
            color: white;
            font-weight: 500;
        }
        
        .nutrition-source-badge.mfds_api {
            background: #34c759;
        }
        
        .nutrition-source-badge.default_db {
            background: #ff9500;
        }
        
        .nutrition-source-badge.api {
            background: #007aff;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin-top: 24px;
        }
        
        .navigation {
            text-align: center;
            margin: 40px 0;
        }
        
        .nav-link {
            color: #007aff;
            text-decoration: none;
            font-weight: 600;
            margin: 0 12px;
            padding: 14px 28px;
            border: 2px solid #007aff;
            border-radius: 12px;
            transition: all 0.3s ease;
            display: inline-block;
            background: rgba(0, 122, 255, 0.05);
        }
        
        .nav-link:hover {
            background: #007aff;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #86868b;
        }
        
        .empty-state h3 {
            color: #1d1d1f;
            margin-bottom: 12px;
            font-weight: 600;
        }
        
        .empty-state p {
            font-size: 16px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-ok { background: #34c759; }
        .status-warning { background: #ff9500; }
        .status-error { background: #ff3b30; }
        
        .refresh-btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            margin-left: 12px;
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #0056cc;
            transform: translateY(-1px);
        }
        
        .status-details {
            margin-top: 12px;
            font-size: 12px;
            color: #86868b;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 36px;
            }
            
            .card {
                padding: 20px;
            }
            
            .metric-grid {
                grid-template-columns: 1fr;
            }
            
            .weekly-grid {
                grid-template-columns: repeat(7, 1fr);
                gap: 8px;
            }
            
            .day-item {
                padding: 12px 4px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>칼로리 대시보드</h1>
        <p>AI 기반 음식 인식과 영양데이터로 관리하는 나의 건강</p>
    </div>

    <!-- API 상태 배너 -->
    <div class="api-status-banner" id="api-status-banner">
        <span id="api-status-text">영양정보 API 상태를 확인 중...</span>
    </div>

    <div class="dashboard-grid">
        <!-- 주간 통계 -->
        <div class="card">
            <h2>주간 통계 (최근 7일)</h2>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value">{{ "%.0f"|format(weekly.avg_daily_calories) }}</div>
                    <div class="metric-label">일평균 칼로리</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{{ weekly.total_meals }}</div>
                    <div class="metric-label">총 식사 횟수</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{{ "%.0f"|format(weekly.total_calories) }}</div>
                    <div class="metric-label">총 칼로리</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{{ "%.1f"|format(weekly.total_protein) }}</div>
                    <div class="metric-label">총 단백질 (g)</div>
                </div>
            </div>
            
            <div class="weekly-grid">
                {% for day in weekly.weekly_data %}
                <div class="day-item {% if day.date == today.date %}today{% endif %}">
                    <div class="day-name">
                        {% set day_names = ['월', '화', '수', '목', '금', '토', '일'] %}
                        {{ day_names[loop.index0] }}
                    </div>
                    <div class="day-calories">{{ "%.0f"|format(day.total_calories) }}</div>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- 칼로리 추이 차트 -->
        <div class="card">
            <h2>칼로리 추이</h2>
            <div class="chart-container">
                <canvas id="calorie-chart"></canvas>
            </div>
        </div>

        <!-- 영양소 비율 차트 -->
        <div class="card">
            <h2>오늘 영양소 비율</h2>
            <div class="chart-container">
                <canvas id="nutrition-chart"></canvas>
            </div>
        </div>

        <!-- 최근 음식 기록 -->
        <div class="card" style="grid-column: 1 / -1;">
            <h2>최근 음식 기록 (7일간)</h2>
            {% if recent_foods %}
            <div class="food-list">
                {% for food in recent_foods %}
                <div class="food-item">
                    <div class="food-info">
                        <div class="food-name">
                            {{ food.food_name_ko or food.food_name_en or food.food_name or '알 수 없는 음식' }}
                            {% if food.method %}
                                {% if 'api' in food.method %}
                                    <span class="nutrition-source-badge mfds_api">식약처API</span>
                                {% elif 'default' in food.method %}
                                    <span class="nutrition-source-badge default_db">기본DB</span>
                                {% else %}
                                    <span class="nutrition-source-badge api">AI분석</span>
                                {% endif %}
                            {% endif %}
                        </div>
                        <div class="food-details">
                            {{ food.restaurant_name }} • 
                            {{ food.timestamp.split('T')[0] }} {{ food.timestamp.split('T')[1].split('.')[0] }} •
                            {% if food.confidence %}
                            정확도 {{ "%.1f"|format(food.confidence * 100) }}% •
                            {% endif %}
                            중량 {{ "%.0f"|format(food.weight_grams or 0) }}g
                            {% if food.protein %}
                            • 단백질 {{ "%.1f"|format(food.protein) }}g 탄수화물 {{ "%.1f"|format(food.carbs or 0) }}g 지방 {{ "%.1f"|format(food.fat or 0) }}g
                            {% endif %}
                        </div>
                    </div>
                    <div class="food-calories">
                        {{ "%.0f"|format(food.calories) }} kcal
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="empty-state">
                <h3>아직 등록된 음식이 없습니다</h3>
                <p>음식 사진을 등록하면 여기에 기록이 표시됩니다</p>
            </div>
            {% endif %}
        </div>

        <!-- 목표 달성도 (강조된 카드) -->
        {% if progress %}
        <div class="card" style="grid-column: 1 / -1; background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(52, 199, 89, 0.1)); border: 2px solid #007aff; box-shadow: 0 12px 48px rgba(0, 122, 255, 0.2);">
            <h2 style="font-size: 28px; color: #007aff; text-align: center; margin-bottom: 30px;">🏆 목표 달성도</h2>
            <div class="metric-grid">
                <div class="metric-item">
                    <div class="metric-value">{{ "%.1f"|format(progress.recent_avg_calories or 0) }}</div>
                    <div class="metric-label">최근 평균 칼로리</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{{ "%.0f"|format(progress.daily_calorie_goal or 0) }}</div>
                    <div class="metric-label">목표 칼로리</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{{ "%.1f"|format(progress.current_weight or 0) }}</div>
                    <div class="metric-label">현재 체중 (kg)</div>
                </div>
                <div class="metric-item">
                    <div class="metric-value">{{ "%.1f"|format(progress.target_weight or 0) }}</div>
                    <div class="metric-label">목표 체중 (kg)</div>
                </div>
            </div>
            <div style="margin-top: 20px; padding: 16px; border-radius: 12px; 
                        {% if progress.on_track %}
                        background: rgba(52, 199, 89, 0.1); color: #1b5e20;
                        {% else %}
                        background: rgba(255, 149, 0, 0.1); color: #e65100;
                        {% endif %}">
                {% if progress.on_track %}
                    목표를 잘 달성하고 있습니다!
                {% else %}
                    목표 달성을 위해 칼로리 조절이 필요합니다.
                {% endif %}
                <br><small>D-{{ progress.days_remaining or 0 }}일 남음</small>
            </div>
        </div>
        {% else %}
        <div class="card" style="grid-column: 1 / -1; background: rgba(255, 149, 0, 0.1); border: 2px solid #ff9500;">
            <h2 style="font-size: 24px; color: #ff9500; text-align: center; margin-bottom: 20px;">⚠️ 프로필 설정 필요</h2>
            <div style="text-align: center; padding: 20px;">
                <p style="font-size: 16px; color: #1d1d1f; margin-bottom: 20px;">
                    목표 달성도를 확인하려면 먼저 프로필을 설정해주세요.
                </p>
                <a href="/profile" style="background: #ff9500; color: white; padding: 12px 24px; border-radius: 12px; text-decoration: none; font-weight: 600;">
                    프로필 설정하기
                </a>
            </div>
        </div>
        {% endif %}


    </div>

    <div class="navigation">
        <a href="/" class="nav-link">지도로 돌아가기</a>
        <a href="/upload_food" class="nav-link">음식 등록하기</a>
        <a href="/receipt" class="nav-link">영수증 분석</a>
        <a href="/exercise" class="nav-link">운동 추천</a>
        <a href="/profile" class="nav-link">프로필 설정</a>
    </div>

    <script>
        // 차트 데이터 준비
        const weeklyData = {{ weekly.weekly_data | tojson }};
        const todayNutrition = {{ today | tojson }};

        // 안전한 숫자 변환 함수
        function safeNumber(value) {
            const num = parseFloat(value);
            return isNaN(num) ? 0 : num;
        }

        // 칼로리 추이 차트
        const calorieCtx = document.getElementById('calorie-chart').getContext('2d');
        new Chart(calorieCtx, {
            type: 'line',
            data: {
                labels: ['월', '화', '수', '목', '금', '토', '일'],
                datasets: [{
                    label: '일일 칼로리',
                    data: weeklyData.map(day => safeNumber(day.total_calories)),
                    borderColor: '#007aff',
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    fill: true,
                    tension: 0.4,
                    borderWidth: 3,
                    pointBackgroundColor: '#007aff',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '칼로리 (kcal)',
                            color: '#86868b',
                            font: {
                                size: 14,
                                weight: '600'
                            }
                        },
                        grid: {
                            color: '#f2f2f7'
                        },
                        ticks: {
                            color: '#86868b'
                        }
                    },
                    x: {
                        grid: {
                            color: '#f2f2f7'
                        },
                        ticks: {
                            color: '#86868b'
                        }
                    }
                }
            }
        });

        // 영양소 비율 차트
        const nutritionCtx = document.getElementById('nutrition-chart').getContext('2d');
        const proteinCal = safeNumber(todayNutrition.total_protein) * 4;
        const carbsCal = safeNumber(todayNutrition.total_carbs) * 4;
        const fatCal = safeNumber(todayNutrition.total_fat) * 9;

        new Chart(nutritionCtx, {
            type: 'doughnut',
            data: {
                labels: ['단백질', '탄수화물', '지방'],
                datasets: [{
                    data: [proteinCal, carbsCal, fatCal],
                    backgroundColor: [
                        '#ff3b30',
                        '#007aff', 
                        '#ff9500'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            color: '#1d1d1f',
                            font: {
                                size: 14,
                                weight: '600'
                            },
                            padding: 20
                        }
                    }
                }
            }
        });

        // API 상태 확인
        async function checkApiStatus() {
            try {
                const response = await fetch('/api/nutrition_api_status');
                const data = await response.json();
                
                const banner = document.getElementById('api-status-banner');
                const statusText = document.getElementById('api-status-text');
                
                if (data.success && data.data.api_available) {
                    banner.className = 'api-status-banner';
                    statusText.textContent = '식품의약품안전처 영양데이터 API 정상 연결됨';
                } else {
                    banner.className = 'api-status-banner warning';
                    statusText.textContent = '영양정보 API 연결 불가 - 기본 데이터베이스 사용 중';
                }
            } catch (error) {
                const banner = document.getElementById('api-status-banner');
                const statusText = document.getElementById('api-status-text');
                banner.className = 'api-status-banner error';
                statusText.textContent = 'API 상태 확인 실패';
            }
        }



        // 페이지 로드 시 상태 확인
        checkApiStatus();

        // 30초마다 자동 새로고침
        setInterval(() => {
            checkApiStatus();
        }, 30000);
    </script>
</body>
</html>
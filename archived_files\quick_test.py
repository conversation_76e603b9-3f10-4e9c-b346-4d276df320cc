#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
위치 추적 및 알림 기능 빠른 테스트 스크립트
Quick Location Tracking Test Script
"""

import time
import json
import requests
from restaurant_visitor import RestaurantVisitor
from geopy.distance import geodesic
import logging

# 간단한 로깅 설정
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
logger = logging.getLogger(__name__)

class QuickLocationTest:
    """빠른 위치 추적 테스트"""
    
    def __init__(self):
        self.visitor = RestaurantVisitor()
        
        # 음식점 데이터 로드
        try:
            with open('restaurants.json', 'r', encoding='utf-8') as f:
                restaurant_list = json.load(f)
            self.visitor.restaurants = {r['name']: (r['lat'], r['lon']) for r in restaurant_list}
            logger.info(f"✅ {len(self.visitor.restaurants)}개 음식점 로드 완료")
        except Exception as e:
            logger.error(f"❌ 음식점 데이터 로드 실패: {e}")
            exit(1)

    def test_specific_restaurant(self, restaurant_name):
        """특정 음식점에서 알림 테스트"""
        if restaurant_name not in self.visitor.restaurants:
            logger.error(f"❌ '{restaurant_name}' 음식점을 찾을 수 없습니다")
            return False
        
        restaurant_pos = self.visitor.restaurants[restaurant_name]
        logger.info(f"🍽️ 테스트 대상: {restaurant_name} {restaurant_pos}")
        
        # 음식점 근처 위치 (5m 거리)
        test_lat = restaurant_pos[0] + 0.00003
        test_lng = restaurant_pos[1] + 0.00003
        
        distance = geodesic((test_lat, test_lng), restaurant_pos).meters
        logger.info(f"📏 테스트 위치에서 음식점까지 거리: {distance:.1f}m")
        
        # 상태 초기화
        self.visitor.visit_times.clear()
        self.visitor.notification_sent.clear()
        
        # 위치 추적 시뮬레이션
        logger.info("🎯 위치 추적 시뮬레이션 시작...")
        
        for second in range(6):  # 6초간 테스트
            current_time = time.time() + second
            
            # restaurant_visitor의 check_restaurant_visit 메서드 사용
            status_changes = self.visitor.check_restaurant_visit((test_lat, test_lng), current_time)
            
            logger.info(f"⏱️ {second}초: ", end="")
            
            if status_changes['entered']:
                entry = status_changes['entered'][0]
                logger.info(f"🚶‍♂️ 입장 감지: {entry['restaurant']} (거리: {entry['distance']}m)")
            elif status_changes['notifications']:
                notification = status_changes['notifications'][0]
                logger.info(f"🔔 알림 발송! {notification['restaurant']} (체류: {notification['stay_duration']}초)")
                return True
            else:
                stay_time = current_time - self.visitor.visit_times.get(restaurant_name, current_time)
                logger.info(f"⏳ 대기 중... (체류: {stay_time:.1f}초)")
            
            time.sleep(0.1)  # 시뮬레이션 지연
        
        logger.warning("⚠️ 6초 동안 알림이 발생하지 않았습니다")
        return False

    def test_all_restaurants(self):
        """모든 음식점에서 거리 테스트"""
        logger.info("📍 모든 음식점 거리 테스트")
        
        # IT관 위치 (기본 위치)
        test_position = (35.830569788, 128.75399385)
        
        results = []
        for name, pos in self.visitor.restaurants.items():
            distance = geodesic(test_position, pos).meters
            results.append({
                'name': name,
                'distance': distance,
                'detectable': distance <= 15.0  # 15m 반경
            })
        
        # 거리순 정렬
        results.sort(key=lambda x: x['distance'])
        
        logger.info(f"📊 IT관에서 가장 가까운 음식점들 (상위 10개):")
        for i, result in enumerate(results[:10]):
            status = "🎯 감지 가능" if result['detectable'] else "📍 감지 불가"
            logger.info(f"   {i+1}. {result['name']}: {result['distance']:.1f}m {status}")
        
        # 감지 가능한 음식점 수
        detectable_count = sum(1 for r in results if r['detectable'])
        logger.info(f"✅ 총 {detectable_count}개 음식점이 IT관에서 감지 가능")
        
        return results

    def test_server_connection(self):
        """서버 연결 상태 확인"""
        logger.info("🌐 서버 연결 테스트")
        
        try:
            # 메인 서버 확인
            response = requests.get('http://localhost:5000', timeout=3)
            logger.info(f"✅ 메인 서버 (Flask): 연결 성공 ({response.status_code})")
        except Exception as e:
            logger.error(f"❌ 메인 서버 연결 실패: {e}")
            return False
        
        try:
            # Analyzer 서버 확인
            response = requests.get('http://localhost:5001/health', timeout=3)
            logger.info(f"✅ Analyzer 서버: 연결 성공 ({response.status_code})")
        except Exception as e:
            logger.warning(f"⚠️ Analyzer 서버 연결 실패: {e}")
        
        return True

def main():
    """메인 테스트 실행"""
    print("🚀 위치 추적 및 알림 기능 빠른 테스트")
    print("=" * 50)
    
    tester = QuickLocationTest()
    
    # 1. 서버 연결 확인
    if not tester.test_server_connection():
        print("\n❌ 서버가 실행되지 않았습니다!")
        print("먼저 서버를 시작하세요: python app.py")
        return
    
    print()
    
    # 2. 모든 음식점 거리 테스트
    results = tester.test_all_restaurants()
    
    print()
    
    # 3. 가장 가까운 음식점에서 알림 테스트
    closest_restaurants = [r for r in results if r['detectable']][:3]
    
    if closest_restaurants:
        print("🧪 가장 가까운 음식점들에서 알림 테스트:")
        for restaurant in closest_restaurants:
            print(f"\n📍 {restaurant['name']} 테스트 중...")
            success = tester.test_specific_restaurant(restaurant['name'])
            if success:
                print(f"✅ {restaurant['name']}: 알림 테스트 성공!")
                break
            else:
                print(f"❌ {restaurant['name']}: 알림 테스트 실패")
    else:
        print("⚠️ IT관 근처에 감지 가능한 음식점이 없습니다")
        print("다른 위치에서 테스트하거나 감지 반경을 늘려보세요")
    
    print("\n🎯 실제 테스트 방법:")
    print("1. 브라우저에서 http://localhost:5000 접속")
    print("2. '위치 설정' 버튼 클릭")
    print("3. 음식점 마커 근처로 위치 이동")
    print("4. 3초 대기 후 알림 확인")
    print("5. '🧪 알림 테스트' 버튼으로도 테스트 가능")

if __name__ == "__main__":
    main()

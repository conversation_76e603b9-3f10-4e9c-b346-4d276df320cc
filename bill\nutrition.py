import requests

API_URL = "https://trackapi.nutritionix.com/v2/natural/nutrients"
APP_ID = "93fe502b"
API_KEY = "2b134d101c75fd773f662af0ffe9d8ef"


FOOD_TRANSLATION = {
    "빅맥": "big mac",
    "후렌치후라이": "french fries",
    "치즈버거": "cheeseburger",
    "치킨너겟": "chicken nuggets",
    "콜라": "coca cola",
    "사이다": "sprite",
    "감자튀김": "french fries",
    "맥너겟": "mcnuggets",
    "불고기버거": "bulgogi burger",
    "햄버거": "hamburger"
}

def get_nutrition(food_name: str) -> dict:
    query = FOOD_TRANSLATION.get(food_name, food_name)

    headers = {
        "x-app-id": APP_ID,
        "x-app-key": API_KEY,
        "Content-Type": "application/json",
    }

    data = {
        "query": query,
        "timezone": "Asia/Seoul"
    }

    try:
        response = requests.post(API_URL, headers=headers, json=data)

        print(f"\n🔍 요청된 음식: {query}")
        print(f"📦 응답 상태 코드: {response.status_code}")

        if response.status_code != 200:
            print(f"❌ 오류 발생! 응답: {response.text[:300]}")
            return {}

        json_data = response.json()
        foods = json_data.get("foods", [])

        if not foods:
            print("⚠️ 'foods' 항목이 비어있습니다.")
            return {}

        food = foods[0]  

        result = {
            "food": food_name,
            "calories": int(food.get("nf_calories", 0)),
            "carbs": int(food.get("nf_total_carbohydrate", 0)),
            "protein": int(food.get("nf_protein", 0)),
            "fat": int(food.get("nf_total_fat", 0))
        }

        print(f"✅ 영양소 정보: {result}")
        return result

    except Exception as e:
        print(f"❌ 예외 발생: {str(e)}")
        return {}

# -*- coding: utf-8 -*-
"""
Calorie and Nutrition Database Model
SQLite-based local data storage with enhanced nutrition API integration
"""

import sqlite3
import json
from datetime import datetime, date, timedelta
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class FoodDatabase:
    """Food and calorie data management with nutrition API integration"""
    
    def __init__(self, db_path="food_data.db"):
        self.db_path = Path(db_path)
        self.init_database()
    
    def get_connection(self):
        """Database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Return results as dict
        return conn
    
    def init_database(self):
        """Create database tables with enhanced nutrition fields"""
        with self.get_connection() as conn:
            # Food records table with enhanced nutrition tracking
            conn.execute("""
                CREATE TABLE IF NOT EXISTS food_records (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    date DATE NOT NULL,
                    restaurant_name TEXT,
                    food_name_ko TEXT,
                    food_name_en TEXT,
                    confidence REAL,
                    method TEXT,
                    calories REAL NOT NULL,
                    protein REAL,
                    carbs REAL,
                    fat REAL,
                    fiber REAL DEFAULT 0,
                    sodium REAL DEFAULT 0,
                    weight_grams REAL DEFAULT 0,
                    image_path TEXT,
                    analysis_data TEXT,  -- JSON format original analysis data
                    
                    -- Enhanced nutrition API fields
                    nutrition_source TEXT DEFAULT 'unknown',  -- 'mfds_api', 'default_db', 'user_input', etc.
                    api_food_code TEXT,  -- 식약처 API 음식 코드
                    api_retrieved_at DATETIME,  -- API 조회 시간
                    estimated_weight REAL,  -- AI가 추정한 무게
                    user_adjusted_weight REAL,  -- 사용자가 조정한 무게
                    
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Daily summary table with enhanced nutrition tracking
            conn.execute("""
                CREATE TABLE IF NOT EXISTS daily_summary (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date DATE UNIQUE NOT NULL,
                    total_calories REAL DEFAULT 0,
                    total_protein REAL DEFAULT 0,
                    total_carbs REAL DEFAULT 0,
                    total_fat REAL DEFAULT 0,
                    total_fiber REAL DEFAULT 0,
                    total_sodium REAL DEFAULT 0,
                    meal_count INTEGER DEFAULT 0,
                    api_nutrition_count INTEGER DEFAULT 0,  -- API로 조회된 음식 개수
                    default_nutrition_count INTEGER DEFAULT 0,  -- 기본값 사용 음식 개수
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # User profile table (enhanced for nutrition goals)
            conn.execute("""
                CREATE TABLE IF NOT EXISTS user_profile (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    current_weight REAL,
                    target_weight REAL,
                    height REAL,
                    age INTEGER,
                    gender TEXT,
                    activity_level TEXT,
                    target_date DATE,
                    daily_calorie_goal REAL DEFAULT 2000,
                    daily_protein_goal REAL DEFAULT 120,
                    daily_carbs_goal REAL DEFAULT 250,
                    daily_fat_goal REAL DEFAULT 65,
                    daily_fiber_goal REAL DEFAULT 25,
                    daily_sodium_limit REAL DEFAULT 2300,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Nutrition API cache table
            conn.execute("""
                CREATE TABLE IF NOT EXISTS nutrition_cache (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    food_name TEXT NOT NULL,
                    food_code TEXT,
                    weight_grams REAL NOT NULL,
                    calories REAL,
                    protein REAL,
                    carbs REAL,
                    fat REAL,
                    fiber REAL,
                    sodium REAL,
                    source TEXT NOT NULL,  -- 'mfds_api', 'default_db'
                    api_response TEXT,  -- JSON of original API response
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME,
                    UNIQUE(food_name, weight_grams, source)
                )
            """)
            
            # Food search history for auto-completion
            conn.execute("""
                CREATE TABLE IF NOT EXISTS food_search_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    search_term TEXT NOT NULL,
                    selected_food_name TEXT,
                    search_count INTEGER DEFAULT 1,
                    last_searched DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(search_term)
                )
            """)
            
            conn.commit()
            logger.info("✅ Enhanced database table initialization completed")
    
    def add_food_record(self, restaurant_name, analysis_result, image_path=None):
        """Add food record with enhanced nutrition tracking"""
        try:
            with self.get_connection() as conn:
                today = date.today()
                
                # Extract data from analysis result
                data = analysis_result.get('data', {})
                nutrition = data.get('nutrition', {})
                
                # Determine nutrition source and API details
                nutrition_source = data.get('nutrition_source', 'unknown')
                api_food_code = data.get('api_food_code')
                api_retrieved_at = data.get('api_retrieved_at')
                estimated_weight = data.get('estimated_weight', nutrition.get('weight_grams', 0))
                
                # Add food record with enhanced fields
                cursor = conn.execute("""
                    INSERT INTO food_records (
                        date, restaurant_name, food_name_ko, food_name_en,
                        confidence, method, calories, protein, carbs, fat,
                        fiber, sodium, weight_grams, image_path, analysis_data,
                        nutrition_source, api_food_code, api_retrieved_at,
                        estimated_weight
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    today,
                    restaurant_name,
                    data.get('food_name_ko'),
                    data.get('food_name_en'),
                    data.get('confidence', 0.0),
                    data.get('method'),
                    nutrition.get('calories', 0.0),
                    nutrition.get('protein', 0.0),
                    nutrition.get('carbs', 0.0),
                    nutrition.get('fat', 0.0),
                    nutrition.get('fiber', 0.0),
                    nutrition.get('sodium', 0.0),
                    nutrition.get('weight_grams', 0.0),
                    image_path,
                    json.dumps(analysis_result),
                    nutrition_source,
                    api_food_code,
                    api_retrieved_at,
                    estimated_weight
                ))
                
                record_id = cursor.lastrowid
                
                # Update daily summary with enhanced tracking
                self._update_daily_summary(today, nutrition, nutrition_source)
                
                logger.info(f"✅ Food record added: {data.get('food_name_ko', 'Unknown')} ({nutrition.get('calories', 0):.0f} kcal) from {nutrition_source}")
                return record_id
                
        except Exception as e:
            logger.error(f"❌ Failed to add food record: {e}")
            return None
    
    def _update_daily_summary(self, target_date, nutrition, nutrition_source='unknown'):
        """Update daily summary with enhanced nutrition tracking"""
        with self.get_connection() as conn:
            # Check existing summary
            existing = conn.execute(
                "SELECT * FROM daily_summary WHERE date = ?", 
                (target_date,)
            ).fetchone()
            
            # Determine nutrition source counts
            api_count_delta = 1 if nutrition_source in ['mfds_api', 'api'] else 0
            default_count_delta = 1 if nutrition_source in ['default_db', 'default'] else 0
            
            if existing:
                # Update existing data
                conn.execute("""
                    UPDATE daily_summary SET
                        total_calories = total_calories + ?,
                        total_protein = total_protein + ?,
                        total_carbs = total_carbs + ?,
                        total_fat = total_fat + ?,
                        total_fiber = total_fiber + ?,
                        total_sodium = total_sodium + ?,
                        meal_count = meal_count + 1,
                        api_nutrition_count = api_nutrition_count + ?,
                        default_nutrition_count = default_nutrition_count + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE date = ?
                """, (
                    nutrition.get('calories', 0.0),
                    nutrition.get('protein', 0.0),
                    nutrition.get('carbs', 0.0),
                    nutrition.get('fat', 0.0),
                    nutrition.get('fiber', 0.0),
                    nutrition.get('sodium', 0.0),
                    api_count_delta,
                    default_count_delta,
                    target_date
                ))
            else:
                # Create new summary
                conn.execute("""
                    INSERT INTO daily_summary (
                        date, total_calories, total_protein, total_carbs, 
                        total_fat, total_fiber, total_sodium, meal_count,
                        api_nutrition_count, default_nutrition_count
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, 1, ?, ?)
                """, (
                    target_date,
                    nutrition.get('calories', 0.0),
                    nutrition.get('protein', 0.0),
                    nutrition.get('carbs', 0.0),
                    nutrition.get('fat', 0.0),
                    nutrition.get('fiber', 0.0),
                    nutrition.get('sodium', 0.0),
                    api_count_delta,
                    default_count_delta
                ))
    
    def cache_nutrition_data(self, food_name, weight_grams, nutrition_data, source='mfds_api', expires_hours=24):
        """Cache nutrition data for faster retrieval"""
        try:
            with self.get_connection() as conn:
                expires_at = datetime.now() + timedelta(hours=expires_hours)
                
                conn.execute("""
                    INSERT OR REPLACE INTO nutrition_cache (
                        food_name, weight_grams, calories, protein, carbs, fat,
                        fiber, sodium, source, api_response, expires_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    food_name, weight_grams,
                    nutrition_data.get('calories', 0),
                    nutrition_data.get('protein', 0),
                    nutrition_data.get('carbs', 0),
                    nutrition_data.get('fat', 0),
                    nutrition_data.get('fiber', 0),
                    nutrition_data.get('sodium', 0),
                    source,
                    json.dumps(nutrition_data),
                    expires_at
                ))
                
                logger.debug(f"💾 Cached nutrition data for {food_name} ({weight_grams}g)")
                
        except Exception as e:
            logger.error(f"❌ Failed to cache nutrition data: {e}")
    
    def get_cached_nutrition(self, food_name, weight_grams, source='mfds_api'):
        """Retrieve cached nutrition data if not expired"""
        try:
            with self.get_connection() as conn:
                result = conn.execute("""
                    SELECT * FROM nutrition_cache 
                    WHERE food_name = ? AND weight_grams = ? AND source = ?
                    AND expires_at > CURRENT_TIMESTAMP
                    ORDER BY created_at DESC LIMIT 1
                """, (food_name, weight_grams, source)).fetchone()
                
                if result:
                    logger.debug(f"💾 Cache hit for {food_name} ({weight_grams}g)")
                    return dict(result)
                
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to retrieve cached nutrition: {e}")
            return None
    
    def add_search_history(self, search_term, selected_food_name=None):
        """Add or update food search history for auto-completion"""
        try:
            with self.get_connection() as conn:
                conn.execute("""
                    INSERT OR REPLACE INTO food_search_history (
                        search_term, selected_food_name, search_count, last_searched
                    ) VALUES (
                        ?, ?, 
                        COALESCE((SELECT search_count FROM food_search_history WHERE search_term = ?) + 1, 1),
                        CURRENT_TIMESTAMP
                    )
                """, (search_term, selected_food_name, search_term))
                
        except Exception as e:
            logger.error(f"❌ Failed to add search history: {e}")
    
    def get_popular_searches(self, limit=10):
        """Get popular search terms for auto-completion"""
        try:
            with self.get_connection() as conn:
                results = conn.execute("""
                    SELECT search_term, selected_food_name, search_count
                    FROM food_search_history
                    ORDER BY search_count DESC, last_searched DESC
                    LIMIT ?
                """, (limit,)).fetchall()
                
                return [dict(result) for result in results]
                
        except Exception as e:
            logger.error(f"❌ Failed to get popular searches: {e}")
            return []
    
    def get_daily_summary(self, target_date=None):
        """Get daily summary with enhanced nutrition info"""
        if target_date is None:
            target_date = date.today()
        
        with self.get_connection() as conn:
            summary = conn.execute(
                "SELECT * FROM daily_summary WHERE date = ?",
                (target_date,)
            ).fetchone()
            
            if summary:
                return dict(summary)
            else:
                return {
                    'date': target_date.isoformat(),
                    'total_calories': 0.0,
                    'total_protein': 0.0,
                    'total_carbs': 0.0,
                    'total_fat': 0.0,
                    'total_fiber': 0.0,
                    'total_sodium': 0.0,
                    'meal_count': 0,
                    'api_nutrition_count': 0,
                    'default_nutrition_count': 0
                }
    
    def get_weekly_summary(self, start_date=None):
        """Get weekly summary with enhanced nutrition tracking"""
        if start_date is None:
            start_date = date.today() - timedelta(days=6)  # Recent 7 days
        
        end_date = start_date + timedelta(days=6)
        
        with self.get_connection() as conn:
            summaries = conn.execute("""
                SELECT * FROM daily_summary 
                WHERE date BETWEEN ? AND ?
                ORDER BY date
            """, (start_date, end_date)).fetchall()
            
            # Generate 7-day data (including empty dates)
            weekly_data = []
            current_date = start_date
            
            for i in range(7):
                date_str = current_date.isoformat()
                found = next((dict(s) for s in summaries if s['date'] == date_str), None)
                
                if found:
                    weekly_data.append(found)
                else:
                    weekly_data.append({
                        'date': date_str,
                        'total_calories': 0.0,
                        'total_protein': 0.0,
                        'total_carbs': 0.0,
                        'total_fat': 0.0,
                        'total_fiber': 0.0,
                        'total_sodium': 0.0,
                        'meal_count': 0,
                        'api_nutrition_count': 0,
                        'default_nutrition_count': 0
                    })
                
                current_date += timedelta(days=1)
            
            return weekly_data
    
    def get_recent_food_records(self, days=7):
        """Get recent food records with enhanced nutrition info"""
        since_date = date.today() - timedelta(days=days-1)
        
        with self.get_connection() as conn:
            records = conn.execute("""
                SELECT * FROM food_records 
                WHERE date >= ?
                ORDER BY timestamp DESC
            """, (since_date,)).fetchall()
            
            return [dict(record) for record in records]
    
    def get_nutrition_stats(self, days=7):
        """Enhanced nutrition statistics with API usage info"""
        weekly_data = self.get_weekly_summary()
        
        total_calories = sum(day['total_calories'] for day in weekly_data)
        total_protein = sum(day['total_protein'] for day in weekly_data)
        total_carbs = sum(day['total_carbs'] for day in weekly_data)
        total_fat = sum(day['total_fat'] for day in weekly_data)
        total_fiber = sum(day['total_fiber'] for day in weekly_data)
        total_sodium = sum(day['total_sodium'] for day in weekly_data)
        total_meals = sum(day['meal_count'] for day in weekly_data)
        total_api_nutrition = sum(day['api_nutrition_count'] for day in weekly_data)
        total_default_nutrition = sum(day['default_nutrition_count'] for day in weekly_data)
        
        avg_calories = total_calories / 7 if total_calories > 0 else 0
        api_usage_rate = (total_api_nutrition / total_meals * 100) if total_meals > 0 else 0
        
        return {
            'period': f"{days} days",
            'total_calories': round(total_calories, 1),
            'avg_daily_calories': round(avg_calories, 1),
            'total_protein': round(total_protein, 1),
            'total_carbs': round(total_carbs, 1),
            'total_fat': round(total_fat, 1),
            'total_fiber': round(total_fiber, 1),
            'total_sodium': round(total_sodium, 1),
            'total_meals': total_meals,
            'api_nutrition_count': total_api_nutrition,
            'default_nutrition_count': total_default_nutrition,
            'api_usage_rate': round(api_usage_rate, 1),
            'weekly_data': weekly_data
        }
    
    def cleanup_expired_cache(self):
        """Clean up expired nutrition cache entries"""
        try:
            with self.get_connection() as conn:
                result = conn.execute("""
                    DELETE FROM nutrition_cache 
                    WHERE expires_at < CURRENT_TIMESTAMP
                """)
                
                deleted_count = result.rowcount
                if deleted_count > 0:
                    logger.info(f"🧹 Cleaned up {deleted_count} expired cache entries")
                
        except Exception as e:
            logger.error(f"❌ Failed to cleanup cache: {e}")

# Global database instance
db = FoodDatabase()

<!DOCTYPE html>
<html lang="ko">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>📸 영수증 칼로리 분석 - AI 칼로리 관리</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #333;
      }

      .container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        padding: 30px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .header h1 {
        color: #2c3e50;
        margin: 0 0 10px 0;
        font-size: 28px;
      }

      .header p {
        color: #7f8c8d;
        margin: 0;
        font-size: 16px;
      }

      .api-status {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 25px;
        text-align: center;
      }

      .api-status.success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
      }

      .api-status.warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
      }

      .upload-section {
        border: 3px dashed #e0e0e0;
        border-radius: 12px;
        padding: 40px;
        text-align: center;
        margin-bottom: 25px;
        transition: all 0.3s ease;
      }

      .upload-section:hover {
        border-color: #4caf50;
        background: #f8fdf8;
      }

      .upload-section.dragover {
        border-color: #4caf50;
        background: #e8f5e8;
      }

      .file-input {
        display: none;
      }

      .upload-btn {
        background: linear-gradient(45deg, #4caf50, #45a049);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        margin: 10px;
        transition: all 0.3s ease;
      }

      .upload-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(76, 175, 80, 0.3);
      }

      .upload-btn:disabled {
        background: #cccccc;
        cursor: not-allowed;
        transform: none;
      }

      .restaurant-input {
        width: 100%;
        padding: 12px;
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        font-size: 16px;
        margin-bottom: 20px;
        transition: border-color 0.3s ease;
      }

      .restaurant-input:focus {
        outline: none;
        border-color: #4caf50;
      }

      .preview-section {
        margin: 25px 0;
        text-align: center;
      }

      .preview-image {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      }

      .result-section {
        margin-top: 25px;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #4caf50;
      }

      .food-table {
        width: 100%;
        border-collapse: collapse;
        margin: 15px 0;
      }

      .food-table th,
      .food-table td {
        padding: 12px;
        text-align: center;
        border: 1px solid #ddd;
      }

      .food-table th {
        background: #4caf50;
        color: white;
      }

      .food-table tr:nth-child(even) {
        background: #f2f2f2;
      }

      .total-calories {
        font-size: 20px;
        font-weight: bold;
        color: #e91e63;
        text-align: center;
        margin: 15px 0;
        padding: 15px;
        background: #fff;
        border-radius: 8px;
        border: 2px solid #e91e63;
      }

      .ocr-text {
        background: #f0f0f0;
        padding: 15px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 12px;
        white-space: pre-wrap;
        max-height: 200px;
        overflow-y: auto;
        margin: 15px 0;
      }

      .supported-foods {
        background: #e3f2fd;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
      }

      .supported-foods h3 {
        margin-top: 0;
        color: #1976d2;
      }

      .food-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 10px;
      }

      .food-tag {
        background: #2196f3;
        color: white;
        padding: 4px 8px;
        border-radius: 15px;
        font-size: 12px;
      }

      .navigation {
        text-align: center;
        margin: 30px 0;
      }

      .nav-link {
        color: #4caf50;
        text-decoration: none;
        font-weight: 500;
        margin: 0 15px;
        padding: 12px 24px;
        border: 2px solid #4caf50;
        border-radius: 6px;
        transition: all 0.3s ease;
        display: inline-block;
      }

      .nav-link:hover {
        background: #4caf50;
        color: white;
      }

      .save-btn {
        background: linear-gradient(45deg, #e91e63, #c2185b);
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 8px;
        font-size: 16px;
        cursor: pointer;
        margin: 15px auto;
        display: block;
        transition: all 0.3s ease;
      }

      .save-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(233, 30, 99, 0.3);
      }

      .loading {
        text-align: center;
        padding: 20px;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4caf50;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 20px auto;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .error {
        background: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
      }

      .success {
        background: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 15px;
        border-radius: 8px;
        margin: 15px 0;
      }

      @media (max-width: 600px) {
        .container {
          padding: 20px;
          margin: 10px;
        }

        .header h1 {
          font-size: 24px;
        }

        .upload-section {
          padding: 20px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>📸 영수증 칼로리 분석</h1>
        <p>
          영수증 사진을 업로드하면 AI가 음식을 인식하고 칼로리를 분석해드립니다
        </p>
      </div>

      <!-- API 상태 표시 -->
      {% if google_vision_available %}
      <div class="api-status success">
        ✅ Google Cloud Vision API가 연결되어 있습니다. 영수증 OCR 기능을 사용할
        수 있습니다.
      </div>
      {% else %}
      <div class="api-status warning">
        ⚠️ Google Cloud Vision API가 설정되지 않았습니다. OCR 기능이 제한될 수
        있습니다.
        <br /><small>설치: pip install google-cloud-vision</small>
      </div>
      {% endif %}

      <!-- 영수증 업로드 폼 -->
      <form id="receiptForm" enctype="multipart/form-data">
        <div class="upload-section" id="uploadSection">
          <input
            type="file"
            name="receipt_image"
            id="receiptImage"
            class="file-input"
            accept="image/*"
            required
          />
          <div id="uploadContent">
            <h3>📷 영수증 사진 업로드</h3>
            <p>클릭하거나 파일을 드래그해서 업로드하세요</p>
            <button
              type="button"
              class="upload-btn"
              onclick="document.getElementById('receiptImage').click()"
            >
              파일 선택
            </button>
            <p><small>지원 형식: JPG, PNG, GIF, WebP</small></p>
          </div>
        </div>
      </form>

      <!-- 이미지 미리보기 -->
      <div class="preview-section" id="previewSection" style="display: none">
        <h3>📷 업로드된 영수증</h3>
        <img id="previewImage" class="preview-image" alt="영수증 미리보기" />
        <br />
        <button type="button" class="upload-btn" id="analyzeBtn">
          🔍 영수증 분석하기
        </button>
        <button
          type="button"
          class="upload-btn"
          id="testBtn"
          style="background: #ff9800; margin-left: 10px"
        >
          🧪 간단 테스트
        </button>
      </div>

      <!-- 로딩 -->
      <div class="loading" id="loadingSection" style="display: none">
        <div class="spinner"></div>
        <p>🤖 AI가 영수증을 분석하고 있습니다...</p>
        <p><small>OCR 텍스트 인식 및 음식명 추출 중</small></p>
      </div>

      <!-- 결과 표시 -->
      <div class="result-section" id="resultSection" style="display: none">
        <h3>🔍 분석 결과</h3>

        <!-- 에러 메시지 -->
        <div id="errorMessage" class="error" style="display: none"></div>

        <!-- 성공 결과 -->
        <div id="successResult" style="display: none">
          <div id="totalCalories" class="total-calories"></div>

          <h4>🍽️ 인식된 음식 및 영양정보</h4>
          <table class="food-table" id="foodTable">
            <thead>
              <tr>
                <th>음식명</th>
                <th>칼로리 (kcal)</th>
                <th>탄수화물 (g)</th>
                <th>단백질 (g)</th>
                <th>지방 (g)</th>
              </tr>
            </thead>
            <tbody id="foodTableBody"></tbody>
          </table>

          <button type="button" class="save-btn" id="saveFoodsBtn">
            💾 음식 기록 저장하기
          </button>

          <!-- OCR 텍스트 표시 (토글) -->
          <details style="margin-top: 20px">
            <summary style="cursor: pointer; font-weight: bold">
              🔍 OCR 인식된 전체 텍스트 보기
            </summary>
            <div class="ocr-text" id="ocrText"></div>
          </details>
        </div>
      </div>

      <!-- 지원되는 음식 목록 -->
      {% if supported_foods %}
      <div class="supported-foods">
        <h3>🍜 현재 인식 가능한 음식 (일부)</h3>
        <p>영수증에서 다음과 같은 음식들을 자동으로 인식할 수 있습니다:</p>
        <div class="food-tags">
          {% for food in supported_foods %}
          <span class="food-tag">{{ food }}</span>
          {% endfor %}
          <span class="food-tag">...외 {{ supported_foods|length * 3 }}개</span>
        </div>
      </div>
      {% endif %}

      <div class="navigation">
        <a href="/" class="nav-link">🗺️ 지도로 돌아가기</a>
        <a href="/upload_food" class="nav-link">📸 직접 음식 등록</a>
        <a href="/dashboard" class="nav-link">📊 대시보드 보기</a>
      </div>
    </div>

    <script>
      // 전역 변수
      let currentAnalysisResult = null;

      // DOM 요소들
      const receiptForm = document.getElementById('receiptForm');
      const receiptImage = document.getElementById('receiptImage');
      const uploadSection = document.getElementById('uploadSection');
      const previewSection = document.getElementById('previewSection');
      const previewImage = document.getElementById('previewImage');
      const analyzeBtn = document.getElementById('analyzeBtn');
      const loadingSection = document.getElementById('loadingSection');
      const resultSection = document.getElementById('resultSection');
      const errorMessage = document.getElementById('errorMessage');
      const successResult = document.getElementById('successResult');
      const saveFoodsBtn = document.getElementById('saveFoodsBtn');

      // 파일 선택 이벤트
      receiptImage.addEventListener('change', function(e) {
          const file = e.target.files[0];
          if (file) {
              displayImagePreview(file);
          }
      });

      // 드래그 앤 드롭 기능 (버튼 클릭 제외)
      uploadSection.addEventListener('click', (e) => {
          // 버튼 클릭이 아닌 경우에만 파일 선택창 열기
          if (!e.target.classList.contains('upload-btn')) {
              receiptImage.click();
          }
      });

      uploadSection.addEventListener('dragover', (e) => {
          e.preventDefault();
          uploadSection.classList.add('dragover');
      });

      uploadSection.addEventListener('dragleave', () => {
          uploadSection.classList.remove('dragover');
      });

      uploadSection.addEventListener('drop', (e) => {
          e.preventDefault();
          uploadSection.classList.remove('dragover');

          const files = e.dataTransfer.files;
          if (files.length > 0) {
              receiptImage.files = files;
              displayImagePreview(files[0]);
          }
      });

      // 이미지 미리보기 표시
      function displayImagePreview(file) {
          const reader = new FileReader();
          reader.onload = function(e) {
              previewImage.src = e.target.result;
              previewSection.style.display = 'block';

              // 결과 섹션 숨기기
              resultSection.style.display = 'none';
          };
          reader.readAsDataURL(file);
      }

      // 영수증 분석 버튼 클릭
      analyzeBtn.addEventListener('click', function() {
          analyzeReceipt();
      });

      // 간단 테스트 버튼 클릭
      document.getElementById('testBtn').addEventListener('click', function() {
          testSimpleUpload();
      });

      // 영수증 분석 함수
      async function analyzeReceipt() {
          const file = receiptImage.files[0];

          if (!file) {
              alert('영수증 이미지를 선택해주세요.');
              return;
          }

          // 로딩 표시
          loadingSection.style.display = 'block';
          resultSection.style.display = 'none';
          analyzeBtn.disabled = true;

          try {
              const formData = new FormData();
              formData.append('receipt_image', file);

              const response = await fetch('/food/upload_receipt', {
                  method: 'POST',
                  body: formData
              });

              // 응답 상태 확인
              if (!response.ok) {
                  throw new Error(`HTTP ${response.status}: ${response.statusText}`);
              }

              // Content-Type 확인
              const contentType = response.headers.get('content-type');
              if (!contentType || !contentType.includes('application/json')) {
                  const text = await response.text();
                  throw new Error(`서버가 JSON이 아닌 응답을 반환했습니다: ${text.substring(0, 100)}...`);
              }

              const result = await response.json();

              loadingSection.style.display = 'none';
              analyzeBtn.disabled = false;

              if (result.success) {
                  displayAnalysisResult(result);
              } else {
                  displayError(result.error || '영수증 분석에 실패했습니다.');
              }

          } catch (error) {
              loadingSection.style.display = 'none';
              analyzeBtn.disabled = false;
              displayError('서버 연결 오류가 발생했습니다: ' + error.message);
          }
      }

      // 간단한 테스트 함수
      async function testSimpleUpload() {
          const file = receiptImage.files[0];

          if (!file) {
              alert('영수증 이미지를 선택해주세요.');
              return;
          }

          console.log('🧪 간단 테스트 시작');

          try {
              const formData = new FormData();
              formData.append('receipt_image', file);

              const response = await fetch('/food/simple_receipt', {
                  method: 'POST',
                  body: formData
              });

              const result = await response.json();
              console.log('🧪 테스트 결과:', result);

              if (result.success) {
                  alert(`✅ 테스트 성공!\n파일명: ${result.filename}\n크기: ${result.size} bytes`);
              } else {
                  alert(`❌ 테스트 실패: ${result.error}`);
              }

          } catch (error) {
              console.error('🧪 테스트 오류:', error);
              alert(`❌ 테스트 오류: ${error.message}`);
          }
      }

      // 분석 결과 표시
      function displayAnalysisResult(result) {
          currentAnalysisResult = result;

          resultSection.style.display = 'block';
          errorMessage.style.display = 'none';
          successResult.style.display = 'block';

          // 총 칼로리 표시
          document.getElementById('totalCalories').textContent =
              `🔥 총 칼로리: ${result.total_calories} kcal`;

          // 음식 테이블 생성
          const tableBody = document.getElementById('foodTableBody');
          tableBody.innerHTML = '';

          result.food_infos.forEach(food => {
              const row = tableBody.insertRow();
              row.innerHTML = `
                  <td>${food.food}</td>
                  <td>${food.calories}</td>
                  <td>${food.carbs}</td>
                  <td>${food.protein}</td>
                  <td>${food.fat}</td>
              `;
          });

          // OCR 텍스트 표시
          document.getElementById('ocrText').textContent = result.ocr_text;
      }

      // 에러 표시
      function displayError(message) {
          resultSection.style.display = 'block';
          errorMessage.style.display = 'block';
          successResult.style.display = 'none';

          errorMessage.innerHTML = `
              <strong>❌ 분석 실패</strong><br>
              ${message}
              <br><br>
              <small>
              <strong>해결 방법:</strong><br>
              • 영수증이 선명하고 잘 보이는지 확인<br>
              • 조명이 충분한 곳에서 촬영<br>
              • 지원되는 음식점인지 확인
              </small>
          `;
      }

      // 음식 저장 버튼 클릭
      saveFoodsBtn.addEventListener('click', async function() {
          if (!currentAnalysisResult) {
              alert('저장할 분석 결과가 없습니다.');
              return;
          }

          saveFoodsBtn.disabled = true;
          saveFoodsBtn.textContent = '💾 저장 중...';

          try {
              const response = await fetch('/food/save_receipt_foods', {
                  method: 'POST',
                  headers: {
                      'Content-Type': 'application/json'
                  },
                  body: JSON.stringify({
                      restaurant_name: '영수증',
                      food_infos: currentAnalysisResult.food_infos,
                      image_path: currentAnalysisResult.image_path
                  })
              });

              const result = await response.json();

              if (result.success) {
                  // 성공 메시지 표시
                  successResult.innerHTML = `
                      <div class="success">
                          <strong>✅ 저장 완료!</strong><br>
                          ${result.message}
                          <br><br>
                          <a href="/dashboard" class="nav-link">📊 대시보드에서 확인하기</a>
                      </div>
                  `;
              } else {
                  displayError(result.error || '저장에 실패했습니다.');
              }

          } catch (error) {
              displayError('저장 중 오류가 발생했습니다: ' + error.message);
          } finally {
              saveFoodsBtn.disabled = false;
              saveFoodsBtn.textContent = '💾 음식 기록 저장하기';
          }
      });

      // 페이지 로드 시 초기화
      document.addEventListener('DOMContentLoaded', function() {
          console.log('📸 영수증 분석 페이지 로드 완료');

          // Google Vision API 상태 확인
          {% if not google_vision_available %}
          console.warn('⚠️ Google Cloud Vision API가 설정되지 않음');
          {% endif %}
      });
    </script>
  </body>
</html>

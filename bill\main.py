from flask import Flask, render_template, request
from google.cloud import vision
from nutrition import get_nutrition
import os

app = Flask(__name__)
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "receipt-ocr-key.json"

def google_ocr_text_from_image(image_path):
    client = vision.ImageAnnotatorClient()
    with open(image_path, "rb") as image_file:
        content = image_file.read()
    image = vision.Image(content=content)
    response = client.text_detection(image=image)
    texts = response.text_annotations
    return texts[0].description if texts else ""

foods = [
    "김밥", "야채김밥", "참치김밥", "계란말이김밥", "불고기버거", "떡볶이",
    "라면", "삼겹살", "치킨", "피자", "짜장면", "짬뽕",
    "비빔밥", "된장찌개", "순두부찌개", "볶음밥", "오므라이스",
    "스파게티", "햄버거", "콜라", "사이다",
    "빅맥", "후렌치후라이", "치킨너겟", "치즈버거", "맥너겟"
]

def extract_food_names(text):
    lines = text.split('\n')
    detected_foods = set()
    sorted_foods = sorted(foods, key=len, reverse=True)
    for line in lines:
        for food in sorted_foods:
            if food in line:
                detected_foods.add(food)

    final_foods = set(detected_foods)
    for food in detected_foods:
        for other in detected_foods:
            if food != other and food in other:
                final_foods.discard(food)
    return list(final_foods)

@app.route("/", methods=["GET"])
def index():
    return render_template("upload.html")

@app.route("/upload", methods=["POST"])
def upload():
    try:
        file = request.files["file"]
        path = f"static/{file.filename}"
        file.save(path)

        text = google_ocr_text_from_image(path)
        food_list = extract_food_names(text)

        food_infos = []
        total_calories = 0
        for food in food_list:
            info = get_nutrition(food)
            if info:
                food_infos.append(info)
                total_calories += info["calories"]

        return render_template("upload.html", result=text,
                               food_infos=food_infos,
                               total_calories=total_calories,
                               foods=food_list)
    except Exception as e:
        return render_template("upload.html", result=f"❌ 에러 발생: {str(e)}",
                               food_infos=[], total_calories=0, foods=[])

# -*- coding: utf-8 -*-
"""
AI Restaurant Visit and Calorie Management System - Enhanced Version
위치 추적 및 자동 알림 기능 개선
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO
import json
import time
import requests
import base64
import os
from datetime import datetime, date
from restaurant_visitor import RestaurantVisitor
from food_database import db
from nutrition_api import nutrition_api, get_food_nutrition, search_foods, check_api_status
from user_profile import profile_manager
import logging
from werkzeug.utils import secure_filename
from pathlib import Path

# Enhanced logging setup
def setup_logging():
    """로깅 시스템 설정"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 콘솔 로깅
    logging.basicConfig(
        level=logging.DEBUG,  # DEBUG로 변경하여 더 자세한 로그 출력
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('app.log', encoding='utf-8')
        ]
    )
    
    # 외부 라이브러리 로깅 레벨 조정
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

setup_logging()
logger = logging.getLogger(__name__)

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins='*')

# Enhanced Configuration management
class Config:
    """애플리케이션 설정 관리 - 개선된 버전"""
    
    # File upload settings
    UPLOAD_FOLDER = os.getenv('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = set(os.getenv('ALLOWED_EXTENSIONS', 'png,jpg,jpeg,gif,webp').split(','))
    MAX_FILE_SIZE = int(os.getenv('MAX_FILE_SIZE', 10 * 1024 * 1024))  # 10MB
    
    # API settings
    ANALYZER_API_URL = os.getenv('ANALYZER_API_URL', 'http://localhost:5001')
    API_TIMEOUT = int(os.getenv('API_TIMEOUT', 30))
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database
    DATABASE_PATH = os.getenv('DATABASE_PATH', 'food_data.db')
    
    # Enhanced Location tracking settings
    DETECTION_RADIUS = float(os.getenv('DETECTION_RADIUS', 10.0))  # 7m -> 10m로 증가
    REQUIRED_STAY_TIME = int(os.getenv('REQUIRED_STAY_TIME', 5))  # 10초 -> 5초로 단축
    LOCATION_UPDATE_INTERVAL = int(os.getenv('LOCATION_UPDATE_INTERVAL', 1000))  # 1초
    
    # 자동 리디렉션 설정
    AUTO_REDIRECT_ENABLED = os.getenv('AUTO_REDIRECT_ENABLED', 'true').lower() == 'true'
    AUTO_REDIRECT_DELAY = int(os.getenv('AUTO_REDIRECT_DELAY', 3))  # 3초 후 자동 이동

# Apply configuration
app.config.update(vars(Config))

# Create necessary directories
def ensure_directories():
    """필요한 디렉토리 생성"""
    directories = [Config.UPLOAD_FOLDER, 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.debug(f"📁 Directory ensured: {directory}")

ensure_directories()

# 식품의약품안전처 API 키 설정
def setup_nutrition_api():
    """영양정보 API 설정"""
    api_key = os.getenv('MFDS_API_KEY')
    if api_key and api_key != 'YOUR_API_KEY_HERE':
        nutrition_api.api_key = api_key
        logger.info("🔑 식품의약품안전처 API 키가 설정되었습니다")
        return True
    else:
        logger.warning("⚠️ 식품의약품안전처 API 키가 설정되지 않았습니다. 기본 데이터베이스를 사용합니다.")
        return False

api_key_configured = setup_nutrition_api()

# Enhanced Visitor logic and restaurant data loading
def load_restaurant_data():
    """음식점 데이터 로드"""
    try:
        restaurant_file = Path(__file__).parent / 'restaurants.json'
        with open(restaurant_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"✅ 음식점 데이터 로드 완료: {len(data)}개 음식점")
        return data
    except FileNotFoundError:
        logger.error("❌ restaurants.json 파일을 찾을 수 없습니다")
        return []
    except json.JSONDecodeError as e:
        logger.error(f"❌ restaurants.json 파일 파싱 오류: {e}")
        return []

# Enhanced visitor with better configuration
visitor = RestaurantVisitor()
restaurant_list = load_restaurant_data()
visitor.restaurants = {r['name']: (r['lat'], r['lon']) for r in restaurant_list}
visitor.required_stay_time = Config.REQUIRED_STAY_TIME
visitor.detection_radius = Config.DETECTION_RADIUS

logger.info(f"🎯 위치 추적 설정: 반경 {Config.DETECTION_RADIUS}m, 체류시간 {Config.REQUIRED_STAY_TIME}초")

def allowed_file(filename):
    """허용된 파일 형식 확인"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def validate_file(file):
    """파일 유효성 검사"""
    if not file:
        return False, "파일이 선택되지 않았습니다"
    
    if file.filename == '':
        return False, "파일 이름이 비어있습니다"
    
    if not allowed_file(file.filename):
        return False, f"지원하지 않는 파일 형식입니다. 허용 형식: {', '.join(Config.ALLOWED_EXTENSIONS)}"
    
    return True, "OK"

def call_analyzer_api(image_path):
    """Food Analyzer API 호출 (향상된 오류 처리)"""
    try:
        # 이미지 파일 존재 확인
        if not Path(image_path).exists():
            logger.error(f"❌ 이미지 파일이 존재하지 않습니다: {image_path}")
            return None
        
        # 이미지를 base64로 인코딩
        with open(image_path, 'rb') as f:
            image_data = base64.b64encode(f.read()).decode('utf-8')
        
        # API 요청
        logger.info(f"🔍 Analyzer API 호출 중...")
        response = requests.post(
            f"{Config.ANALYZER_API_URL}/analyze",
            json={'image': image_data},
            timeout=Config.API_TIMEOUT,
            headers={'Content-Type': 'application/json'}
        )
        
        logger.info(f"📡 API Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            logger.info(f"✅ API 분석 성공: {len(result.get('predictions', []))}개 예측")
            return result
        else:
            logger.error(f"❌ API 오류: {response.status_code} - {response.text}")
            return {
                'success': False,
                'error': f'API 서버 오류 (상태 코드: {response.status_code})',
                'debug_info': {'status_code': response.status_code, 'response': response.text}
            }
            
    except requests.exceptions.Timeout:
        logger.error(f"❌ API 호출 타임아웃 ({Config.API_TIMEOUT}초)")
        return {
            'success': False,
            'error': f'API 응답 시간 초과 ({Config.API_TIMEOUT}초)',
            'debug_info': {'timeout': Config.API_TIMEOUT}
        }
    except requests.exceptions.ConnectionError:
        logger.error(f"❌ API 서버 연결 실패: {Config.ANALYZER_API_URL}")
        return {
            'success': False,
            'error': f'AI 분석 서버에 연결할 수 없습니다. 서버가 실행 중인지 확인하세요.',
            'debug_info': {'api_url': Config.ANALYZER_API_URL}
        }
    except Exception as e:
        logger.error(f"❌ API 호출 중 예상치 못한 오류: {e}")
        return {
            'success': False,
            'error': f'예상치 못한 오류가 발생했습니다: {str(e)}',
            'debug_info': {'exception': str(e)}
        }

def enhance_food_with_nutrition(food_predictions):
    """AI 분석 결과에 식품의약품안전처 API 영양정보 추가 (향상된 오류 처리)"""
    enhanced_foods = []
    
    for i, food in enumerate(food_predictions):
        try:
            food_name = food.get('food_name_ko', food.get('food_name_en', ''))
            estimated_weight = food.get('estimated_weight', 150.0)  # 기본 150g
            
            logger.debug(f"🔍 영양정보 조회 중: {food_name} ({estimated_weight}g)")
            
            # 식품의약품안전처 API에서 영양정보 조회
            nutrition_info = get_food_nutrition(food_name, estimated_weight)
            
            if nutrition_info and nutrition_info.get('calories', 0) > 0:
                # API로 정확한 영양정보 획득
                enhanced_food = {
                    **food,
                    'nutrition': nutrition_info,
                    'nutrition_source': nutrition_info.get('source', 'mfds_api'),
                    'estimated_weight': estimated_weight,
                    'api_food_code': nutrition_info.get('food_code'),
                    'api_retrieved_at': nutrition_info.get('retrieved_at')
                }
                
                logger.info(f"✅ 영양정보 API 조회 성공: {food_name} - {nutrition_info.get('calories', 0)}kcal")
            else:
                # API 실패시 기본값 사용
                enhanced_food = {
                    **food,
                    'nutrition': {
                        'calories': 200.0,
                        'protein': 10.0,
                        'carbs': 25.0,
                        'fat': 8.0,
                        'fiber': 2.0,
                        'sodium': 500.0,
                        'weight_grams': estimated_weight
                    },
                    'nutrition_source': 'default_db',
                    'estimated_weight': estimated_weight
                }
                
                logger.warning(f"⚠️ API 실패, 기본 영양정보 사용: {food_name}")
            
            enhanced_foods.append(enhanced_food)
            
        except Exception as e:
            logger.error(f"❌ 영양정보 처리 오류 ({food_name}): {e}")
            # 오류 발생시 기본값으로 처리
            enhanced_foods.append({
                **food,
                'nutrition': {
                    'calories': 200.0,
                    'protein': 10.0,
                    'carbs': 25.0,
                    'fat': 8.0,
                    'fiber': 2.0,
                    'sodium': 500.0,
                    'weight_grams': 150.0
                },
                'nutrition_source': 'fallback',
                'estimated_weight': 150.0,
                'error': str(e)
            })
    
    return enhanced_foods

# === Route Definitions ===

@app.route('/')
def index():
    """메인 페이지 - 지도 및 위치 추적"""
    try:
        # 설정값들을 템플릿에 전달
        config_data = {
            'detection_radius': Config.DETECTION_RADIUS,
            'required_stay_time': Config.REQUIRED_STAY_TIME,
            'location_update_interval': Config.LOCATION_UPDATE_INTERVAL,
            'auto_redirect_enabled': Config.AUTO_REDIRECT_ENABLED,
            'auto_redirect_delay': Config.AUTO_REDIRECT_DELAY
        }
        return render_template('index_enhanced.html', 
                             restaurants=restaurant_list,
                             config=config_data)
    except Exception as e:
        logger.error(f"❌ 메인 페이지 로드 오류: {e}")
        return f"페이지 로드 중 오류가 발생했습니다: {e}", 500

@app.route('/dashboard')
def dashboard():
    """칼로리 관리 대시보드"""
    try:
        # 오늘 요약
        today_summary = db.get_daily_summary()
        
        # 주간 통계
        weekly_stats = db.get_nutrition_stats(7)
        
        # 최근 음식 기록
        recent_foods = db.get_recent_food_records(7)
        
        # API 상태 확인
        api_status = check_api_status()
        
        # 목표 달성 진행 상황
        progress = None
        try:
            progress = profile_manager.get_progress_summary()
        except Exception as e:
            logger.warning(f"⚠️ 진행 상황 조회 오류: {e}")
        
        return render_template('dashboard.html', 
                             today=today_summary,
                             weekly=weekly_stats,
                             recent_foods=recent_foods,
                             api_status=api_status,
                             progress=progress)
    
    except Exception as e:
        logger.error(f"❌ 대시보드 로드 오류: {e}")
        return f"대시보드 로드 중 오류가 발생했습니다: {e}", 500

@app.route('/upload_food', methods=['GET', 'POST'])
def upload_food():
    """음식 사진 업로드 및 분석"""
    if request.method == 'GET':
        restaurant_name = request.args.get('restaurant', '')
        logger.info(f"📸 음식 등록 페이지 접근: {restaurant_name}")
        return render_template('upload_food.html', restaurant_name=restaurant_name)
    
    try:
        # 파일 업로드 검증
        if 'food_image' not in request.files:
            return jsonify({'success': False, 'error': '이미지 파일이 필요합니다'}), 400
        
        file = request.files['food_image']
        restaurant_name = request.form.get('restaurant_name', 'Unknown Restaurant')
        
        # 파일 유효성 검사
        is_valid, message = validate_file(file)
        if not is_valid:
            return jsonify({'success': False, 'error': message}), 400
        
        # 파일 크기 검사
        file.seek(0, 2)  # 파일 끝으로 이동
        file_size = file.tell()
        file.seek(0)  # 파일 시작으로 복원
        
        if file_size > Config.MAX_FILE_SIZE:
            return jsonify({
                'success': False, 
                'error': f'파일 크기가 너무 큽니다. 최대 {Config.MAX_FILE_SIZE // (1024*1024)}MB까지 지원합니다.'
            }), 400
        
        # 파일 저장
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = Path(Config.UPLOAD_FOLDER) / filename
        
        file.save(str(file_path))
        logger.info(f"📸 이미지 저장 완료: {file_path}")
        
        # Food Analyzer API 호출
        analysis_result = call_analyzer_api(str(file_path))
        
        if analysis_result and analysis_result.get('success'):
            logger.info("✅ AI 음식 인식 성공")
            
            # AI 예측 결과에 식품의약품안전처 API 영양정보 추가
            food_predictions = analysis_result.get('predictions', [])
            if food_predictions:
                enhanced_predictions = enhance_food_with_nutrition(food_predictions)
                
                # 개선된 분석 결과
                enhanced_result = {
                    **analysis_result,
                    'predictions': enhanced_predictions,
                    'nutrition_enhanced': True,
                    'api_key_configured': api_key_configured
                }
                
                return jsonify({
                    'success': True,
                    'message': '음식 분석이 완료되었습니다!',
                    'analysis_result': enhanced_result,
                    'image_path': str(file_path),
                    'restaurant_name': restaurant_name,
                    'requires_selection': True
                })
            else:
                return jsonify({
                    'success': False,
                    'error': 'AI 분석에서 음식을 인식하지 못했습니다. 다른 각도에서 찍은 사진을 시도해보세요.'
                }), 422
            
        else:
            logger.error("❌ AI 음식 인식 실패")
            error_msg = analysis_result.get('error', 'AI 분석 실패') if analysis_result else 'API 연결 실패'
            debug_info = analysis_result.get('debug_info', {}) if analysis_result else {}
            
            return jsonify({
                'success': False,
                'error': error_msg,
                'debug_info': debug_info,
                'suggestions': [
                    '다른 각도에서 음식 사진을 찍어보세요',
                    '조명이 밝은 곳에서 촬영해보세요',
                    'AI 분석 서버가 실행 중인지 확인하세요',
                    '음식이 명확히 보이도록 가까이서 촬영해보세요'
                ]
            }), 422
    
    except Exception as e:
        logger.error(f"❌ 음식 업로드 처리 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'서버 오류가 발생했습니다: {str(e)}',
            'debug_info': {'exception_type': type(e).__name__}
        }), 500

@app.route('/confirm_food', methods=['POST'])
def confirm_food():
    """사용자의 음식 선택 확인 및 데이터베이스 저장"""
    try:
        data = request.json
        
        if not data:
            return jsonify({'success': False, 'error': '요청 데이터가 없습니다'}), 400
        
        # 데이터 추출
        restaurant_name = data.get('restaurant_name')
        selected_food = data.get('selected_food')
        custom_food_name = data.get('custom_food_name')
        custom_weight = data.get('custom_weight')
        analysis_result = data.get('analysis_result')
        image_path = data.get('image_path')
        
        # 필수 데이터 검증
        if not restaurant_name or not analysis_result:
            return jsonify({'success': False, 'error': '필수 데이터가 누락되었습니다'}), 400
        
        # 최종 음식 선택 결정
        if custom_food_name:
            # 사용자가 직접 음식명 입력
            weight = float(custom_weight) if custom_weight else 150.0
            
            logger.info(f"🍽️ 사용자 직접 입력: {custom_food_name} ({weight}g)")
            
            # 식품의약품안전처 API에서 영양정보 조회
            nutrition_info = get_food_nutrition(custom_food_name, weight)
            
            final_food = {
                'food_name_ko': custom_food_name,
                'food_name_en': custom_food_name,
                'confidence': 1.0,  # 사용자 확인
                'nutrition': nutrition_info if nutrition_info else {
                    'calories': 200.0,
                    'protein': 10.0,
                    'carbs': 20.0,
                    'fat': 8.0,
                    'fiber': 2.0,
                    'sodium': 500.0,
                    'weight_grams': weight
                },
                'method': 'user_input_with_api',
                'nutrition_source': nutrition_info.get('source', 'default') if nutrition_info else 'default',
                'estimated_weight': weight
            }
        else:
            # 사용자가 AI 제안에서 선택
            if not selected_food:
                return jsonify({'success': False, 'error': '음식을 선택하거나 직접 입력해주세요'}), 400
            
            final_food = selected_food.copy()
            
            # 사용자가 무게를 수정했을 경우 영양정보 재계산
            if custom_weight:
                new_weight = float(custom_weight)
                old_weight = final_food['nutrition']['weight_grams']
                
                if new_weight != old_weight and old_weight > 0:
                    # 무게 비례로 영양소 재계산
                    ratio = new_weight / old_weight
                    nutrition = final_food['nutrition']
                    
                    final_food['nutrition'] = {
                        'calories': round(nutrition['calories'] * ratio, 1),
                        'protein': round(nutrition['protein'] * ratio, 1),
                        'carbs': round(nutrition['carbs'] * ratio, 1),
                        'fat': round(nutrition['fat'] * ratio, 1),
                        'fiber': round(nutrition.get('fiber', 0) * ratio, 1),
                        'sodium': round(nutrition.get('sodium', 0) * ratio, 1),
                        'weight_grams': new_weight,
                        'source': nutrition.get('source', 'adjusted')
                    }
                    
                    logger.info(f"⚖️ 무게 조정: {old_weight}g → {new_weight}g")
        
        # 최종 분석 결과 생성
        final_analysis = {
            'success': True,
            'data': final_food
        }
        
        # 데이터베이스에 저장
        record_id = db.add_food_record(restaurant_name, final_analysis, image_path)
        
        if record_id:
            logger.info(f"✅ 음식 기록 저장 완료: ID {record_id}")
            
            return jsonify({
                'success': True,
                'message': '음식 기록이 성공적으로 저장되었습니다!',
                'record_id': record_id,
                'food_name': final_food.get('food_name_ko', final_food.get('food_name_en')),
                'calories': final_food['nutrition']['calories'],
                'nutrition_source': final_food.get('nutrition_source', 'unknown'),
                'redirect': url_for('dashboard')
            })
        else:
            logger.error("❌ 데이터베이스 저장 실패")
            return jsonify({
                'success': False,
                'error': '데이터베이스에 저장하는 중 오류가 발생했습니다'
            }), 500
            
    except ValueError as e:
        logger.error(f"❌ 데이터 형식 오류: {e}")
        return jsonify({
            'success': False,
            'error': '입력 데이터 형식이 올바르지 않습니다'
        }), 400
    except Exception as e:
        logger.error(f"❌ 음식 확인 처리 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'서버 오류가 발생했습니다: {str(e)}'
        }), 500

# 기존 API 라우트들은 동일하게 유지...
@app.route('/api/search_food')
def api_search_food():
    """음식 검색 API (자동완성용)"""
    try:
        query = request.args.get('q', '').strip()
        limit = int(request.args.get('limit', 10))
        
        if not query:
            return jsonify({'success': False, 'error': '검색어가 필요합니다'}), 400
        
        if len(query) < 2:
            return jsonify({'success': True, 'data': [], 'message': '검색어가 너무 짧습니다'})
        
        # 식품의약품안전처 API에서 검색
        results = search_foods(query, limit)
        
        # 검색 기록 저장
        if results:
            db.add_search_history(query)
        
        return jsonify({
            'success': True,
            'data': results,
            'query': query,
            'count': len(results)
        })
        
    except ValueError:
        return jsonify({'success': False, 'error': '올바르지 않은 limit 값입니다'}), 400
    except Exception as e:
        logger.error(f"❌ 음식 검색 오류: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/profile')
def profile_page():
    """사용자 프로필 설정 페이지"""
    try:
        return render_template('profile.html')
    except Exception as e:
        logger.error(f"❌ 프로필 페이지 로드 오류: {e}")
        return f"프로필 페이지 로드 중 오류가 발생했습니다: {e}", 500

# === Enhanced SocketIO Event Handlers ===

@socketio.on('update_location')
def handle_location(data):
    """클라이언트에서 받은 위치 정보 처리 - 개선된 버전"""
    try:
        lat = float(data['lat'])
        lon = float(data['lon'])
        current_time = time.time()
        
        logger.debug(f"📍 위치 업데이트: {lat:.6f}, {lon:.6f}")
        
        # 모든 음식점과의 거리 계산
        for restaurant_name, restaurant_location in visitor.restaurants.items():
            distance = visitor.geodesic((lat, lon), restaurant_location).meters
            
            logger.debug(f"📏 {restaurant_name}: {distance:.2f}m")
            
            if distance <= Config.DETECTION_RADIUS:  # 설정된 감지 반경 내
                if restaurant_name not in visitor.visit_times:
                    # 새로 들어온 경우
                    visitor.visit_times[restaurant_name] = current_time
                    logger.info(f"🚶‍♂️ {restaurant_name} 입장: {distance:.2f}m")
                    socketio.emit('status_update', {
                        'restaurant': restaurant_name,
                        'status': 'entered',
                        'distance': round(distance, 2),
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    })
                elif (current_time - visitor.visit_times[restaurant_name] >= Config.REQUIRED_STAY_TIME and 
                      restaurant_name not in visitor.notification_sent):
                    # 충분히 머물렀고 아직 알림을 보내지 않은 경우
                    visitor.notification_sent.add(restaurant_name)
                    stay_duration = current_time - visitor.visit_times[restaurant_name]
                    
                    logger.info(f"🔔 {restaurant_name} 알림 발송: {stay_duration:.1f}초 체류")
                    
                    upload_url = url_for('upload_food', restaurant=restaurant_name)
                    
                    socketio.emit('notification', {
                        'restaurant': restaurant_name,
                        'message': f'{restaurant_name}에 {Config.REQUIRED_STAY_TIME}초 이상 머물고 있습니다. 음식을 등록하시겠습니까?',
                        'upload_url': upload_url,
                        'distance': round(distance, 2),
                        'stay_duration': round(stay_duration, 1),
                        'auto_redirect_enabled': Config.AUTO_REDIRECT_ENABLED,
                        'auto_redirect_delay': Config.AUTO_REDIRECT_DELAY
                    })
                    
            else:
                # 범위를 벗어난 경우
                if restaurant_name in visitor.visit_times:
                    stay_duration = current_time - visitor.visit_times[restaurant_name]
                    del visitor.visit_times[restaurant_name]
                    
                    logger.info(f"👋 {restaurant_name} 퇴장: {stay_duration:.1f}초 체류")
                    socketio.emit('status_update', {
                        'restaurant': restaurant_name,
                        'status': 'left',
                        'distance': round(distance, 2),
                        'stay_duration': round(stay_duration, 1),
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    })
                
                # 알림 기록 제거
                visitor.notification_sent.discard(restaurant_name)
                    
    except (ValueError, KeyError) as e:
        logger.error(f"❌ 위치 데이터 처리 오류: {e}")
        socketio.emit('error', {'message': '위치 데이터가 올바르지 않습니다'})
    except Exception as e:
        logger.error(f"❌ 위치 처리 중 예상치 못한 오류: {e}")

@socketio.on('connect')
def handle_connect():
    """클라이언트 연결"""
    logger.info(f"🔌 클라이언트 연결: {request.sid}")
    # 연결 시 현재 설정 정보 전송
    socketio.emit('config_update', {
        'detection_radius': Config.DETECTION_RADIUS,
        'required_stay_time': Config.REQUIRED_STAY_TIME,
        'location_update_interval': Config.LOCATION_UPDATE_INTERVAL
    })

@socketio.on('disconnect')
def handle_disconnect():
    """클라이언트 연결 해제"""
    logger.info(f"🔌 클라이언트 연결 해제: {request.sid}")

# === Enhanced Error Handlers ===

@app.errorhandler(404)
def not_found_error(error):
    """404 오류 처리"""
    logger.warning(f"📍 404 요청: {request.url}")
    return jsonify({
        'success': False,
        'error': '요청한 페이지를 찾을 수 없습니다',
        'status_code': 404
    }), 404

@app.errorhandler(500)
def internal_error(error):
    """500 오류 처리"""
    logger.error(f"💥 서버 오류: {error}")
    return jsonify({
        'success': False,
        'error': '서버 내부 오류가 발생했습니다',
        'status_code': 500
    }), 500

@app.errorhandler(413)
def file_too_large(error):
    """파일 크기 초과 오류 처리"""
    logger.warning(f"📁 파일 크기 초과: {request.url}")
    return jsonify({
        'success': False,
        'error': f'파일 크기가 너무 큽니다. 최대 {Config.MAX_FILE_SIZE // (1024*1024)}MB까지 지원합니다.',
        'status_code': 413
    }), 413

if __name__ == '__main__':
    print("🚀 AI Restaurant Visit and Calorie Management System Starting (Enhanced Version)")
    print("=" * 80)
    print("📍 Enhanced Features:")
    print("   - Improved real-time location tracking with detailed logging")
    print("   - Automatic food registration page redirect after notification")
    print("   - Enhanced debugging and error handling")
    print("   - Configurable detection radius and stay time")
    print("   - Better status reporting and notifications")
    print("=" * 80)
    print("🌐 Main Server: http://localhost:5000")
    print("🤖 Analyzer API: http://localhost:5001 (needs separate execution)")
    print("🍽️ Nutrition API: Korean Food & Drug Administration API")
    if api_key_configured:
        print("🔑 API Key: ✅ Configured")
    else:
        print("🔑 API Key: ⚠️  Not configured (using default database)")
    print("=" * 80)
    print(f"🎯 위치 추적 설정:")
    print(f"   - 감지 반경: {Config.DETECTION_RADIUS}m")
    print(f"   - 체류 시간: {Config.REQUIRED_STAY_TIME}초")
    print(f"   - 업데이트 간격: {Config.LOCATION_UPDATE_INTERVAL}ms")
    print(f"   - 자동 리디렉션: {'✅ 활성화' if Config.AUTO_REDIRECT_ENABLED else '❌ 비활성화'}")
    if Config.AUTO_REDIRECT_ENABLED:
        print(f"   - 리디렉션 지연: {Config.AUTO_REDIRECT_DELAY}초")
    print("=" * 80)
    
    # Database initialization check
    try:
        db.init_database()
        logger.info("✅ 데이터베이스 초기화 확인")
    except Exception as e:
        logger.error(f"❌ 데이터베이스 초기화 실패: {e}")
    
    # Nutrition API status check
    try:
        api_status = check_api_status()
        if api_status['api_available']:
            logger.info("✅ 식품의약품안전처 API 연결 가능")
        else:
            logger.warning("⚠️ 식품의약품안전처 API 연결 불가 (기본값 사용)")
    except Exception as e:
        logger.error(f"❌ 영양정보 API 상태 확인 실패: {e}")
    
    # Cleanup expired cache
    try:
        db.cleanup_expired_cache()
    except Exception as e:
        logger.error(f"❌ 캐시 정리 실패: {e}")
    
    print("\n🎯 테스트용 샘플 데이터 생성:")
    print("   python generate_sample_data.py")
    print("\n🚀 Starting enhanced server...")
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)

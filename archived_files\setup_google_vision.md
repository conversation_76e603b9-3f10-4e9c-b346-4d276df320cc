# Google Cloud Vision API 설정 완료 체크리스트

## ✅ 설정 완료 후 확인사항

### 1. 파일 위치 확인
```
C:\2025_Project\ai-project-gps\
├── app.py
├── receipt-ocr-key.json  ← 이 파일이 있어야 함
└── templates\
    └── receipt_upload.html
```

### 2. 환경변수 설정 (선택사항)
또는 환경변수로 설정 가능:
```bash
set GOOGLE_APPLICATION_CREDENTIALS=C:\2025_Project\ai-project-gps\receipt-ocr-key.json
```

### 3. 라이브러리 설치
```bash
pip install google-cloud-vision
```

### 4. 테스트 방법
1. 서버 실행: `python app.py`
2. 브라우저에서 http://localhost:5000/receipt 접속
3. 영수증 사진 업로드하여 테스트

## 💰 요금 정보

Google Cloud Vision API 요금 (2024년 기준):
- **무료 할당량**: 월 1,000건까지 무료
- **초과 시**: 1,000건당 $1.50

### 일반적인 사용량:
- 개인 테스트: 월 50-100건 (무료)
- 소규모 앱: 월 500-1,000건 (무료)
- 상업적 사용: 유료 (비용 효율적)

## 🛡️ 보안 주의사항

1. **JSON 키 파일을 절대 공개하지 마세요**
2. **Git에 커밋하지 마세요** (.gitignore에 추가)
3. **필요 시 키를 삭제/재생성하세요**

## 🔍 문제 해결

### API 키가 작동하지 않는 경우:
1. Vision API가 활성화되었는지 확인
2. 서비스 계정에 올바른 역할이 부여되었는지 확인
3. JSON 파일 경로가 정확한지 확인
4. 결제 계정이 연결되었는지 확인

### 오류 메시지 해결:
- `403 Forbidden`: API 활성화 또는 권한 문제
- `401 Unauthorized`: 키 파일 경로 또는 내용 문제
- `429 Too Many Requests`: 할당량 초과

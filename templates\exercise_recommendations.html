<!DOCTYPE html>
<html lang="ko">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>운동 추천 - AI 칼로리 관리</title>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
          "Segoe UI", Roboto, sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        color: #1d1d1f;
      }

      .container {
        max-width: 1000px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        border-radius: 24px;
        padding: 40px;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
      }

      .header {
        text-align: center;
        margin-bottom: 40px;
      }

      .header h1 {
        color: #1d1d1f;
        margin: 0 0 12px 0;
        font-size: 36px;
        font-weight: 700;
        letter-spacing: -0.5px;
      }

      .header p {
        color: #86868b;
        margin: 0;
        font-size: 18px;
        font-weight: 400;
      }

      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 40px;
      }

      .stat-card {
        background: #f2f2f7;
        padding: 24px;
        border-radius: 16px;
        text-align: center;
        border-left: 4px solid #007aff;
        transition: all 0.3s ease;
      }

      .stat-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
      }

      .stat-card.calories {
        border-color: #ff3b30;
      }

      .stat-card.bmr {
        border-color: #ff9500;
      }

      .stat-card.tdee {
        border-color: #34c759;
      }

      .stat-card.excess {
        border-color: #af52de;
      }

      .stat-number {
        font-size: 28px;
        font-weight: 700;
        color: #1d1d1f;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #86868b;
        font-weight: 500;
      }

      .section {
        margin-bottom: 40px;
      }

      .section h2 {
        color: #1d1d1f;
        margin-bottom: 24px;
        font-size: 24px;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      .exercise-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
      }

      .exercise-card {
        background: rgba(52, 199, 89, 0.1);
        border: 2px solid #34c759;
        border-radius: 16px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .exercise-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(52, 199, 89, 0.3);
        background: rgba(52, 199, 89, 0.15);
      }

      .exercise-name {
        font-size: 20px;
        font-weight: 600;
        color: #1b5e20;
        margin-bottom: 8px;
      }

      .exercise-time {
        font-size: 16px;
        color: #1d1d1f;
        margin-bottom: 6px;
        font-weight: 500;
      }

      .exercise-calories {
        font-size: 13px;
        color: #86868b;
      }

      .nutrition-analysis {
        background: rgba(255, 204, 0, 0.1);
        border: 1px solid #ffcc00;
        border-radius: 16px;
        padding: 24px;
      }

      .nutrition-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 16px;
        margin-bottom: 20px;
      }

      .nutrition-stat {
        text-align: center;
      }

      .nutrition-percent {
        font-size: 24px;
        font-weight: 700;
        color: #1d1d1f;
      }

      .nutrition-label {
        font-size: 13px;
        color: #86868b;
        font-weight: 500;
      }

      .alerts {
        list-style: none;
        padding: 0;
        margin: 0;
      }

      .alerts li {
        background: rgba(255, 255, 255, 0.8);
        padding: 12px 16px;
        margin-bottom: 12px;
        border-radius: 12px;
        border-left: 4px solid #ff9500;
        font-weight: 500;
      }

      .alerts li.success {
        border-color: #34c759;
        background: rgba(52, 199, 89, 0.1);
        color: #1b5e20;
      }

      .no-exercise {
        text-align: center;
        padding: 60px;
        background: rgba(52, 199, 89, 0.1);
        border-radius: 16px;
        border: 2px solid #34c759;
      }

      .no-exercise h3 {
        color: #1b5e20;
        margin-bottom: 12px;
        font-weight: 600;
        font-size: 20px;
      }

      .no-exercise p {
        color: #1b5e20;
        margin: 0;
        font-size: 16px;
      }

      .navigation {
        text-align: center;
        margin-top: 40px;
      }

      .nav-link {
        color: #007aff;
        text-decoration: none;
        font-weight: 600;
        margin: 0 12px;
        padding: 14px 28px;
        border: 2px solid #007aff;
        border-radius: 12px;
        transition: all 0.3s ease;
        display: inline-block;
        background: rgba(0, 122, 255, 0.05);
      }

      .nav-link:hover {
        background: #007aff;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
      }

      @media (max-width: 600px) {
        .container {
          padding: 24px;
          margin: 12px;
        }

        .stats-grid {
          grid-template-columns: 1fr;
        }

        .exercise-grid {
          grid-template-columns: 1fr;
        }

        .header h1 {
          font-size: 28px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>운동 추천 시스템</h1>
        <p>개인맞춤 운동 추천 및 영양소 분석</p>
      </div>

      <!-- 칼로리 통계 -->
      <div class="stats-grid">
        <div class="stat-card calories">
          <div class="stat-number">
            {{ "{:.0f}".format(daily_calories or 0) }}
          </div>
          <div class="stat-label">오늘 섭취 칼로리</div>
        </div>
        {% if exercise_data.success %}
        <div class="stat-card bmr">
          <div class="stat-number">
            {{ "{:.0f}".format(exercise_data.bmr or 0) }}
          </div>
          <div class="stat-label">기초대사율 (BMR)</div>
        </div>
        <div class="stat-card tdee">
          <div class="stat-number">
            {{ "{:.0f}".format(exercise_data.tdee or 0) }}
          </div>
          <div class="stat-label">총 일일 소모량 (TDEE)</div>
        </div>
        <div class="stat-card excess">
          <div class="stat-number">
            {{ "{:.0f}".format(exercise_data.excess_calories or 0) }}
          </div>
          <div class="stat-label">초과 칼로리</div>
        </div>
        {% endif %}
      </div>

      <!-- 운동 추천 -->
      <div class="section">
        <h2>운동 추천</h2>

        <!-- 디버깅 정보 -->
        <div
          style="
            background: #f0f0f0;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
          "
        >
          <strong>🔧 디버깅 정보:</strong><br />
          exercise_data 존재: {{ exercise_data is not none }}<br />
          {% if exercise_data %} exercise_data.success: {{ exercise_data.success
          }}<br />
          초과 칼로리: {{ exercise_data.excess_calories }} kcal<br />
          {% if exercise_data.recommendations %} recommendations 존재: True<br />
          메시지: {{ exercise_data.recommendations.message }}<br />
          {% if exercise_data.recommendations.exercises %} exercises 존재:
          True<br />
          exercises 개수: {{ exercise_data.recommendations.exercises|length
          }}<br />
          {% else %} exercises 존재: False<br />
          {% endif %} {% else %} recommendations 존재: False<br />
          {% endif %} {% else %} exercise_data가 None입니다.<br />
          {% endif %}
        </div>

        {% if exercise_data and exercise_data.recommendations %} {% if
        exercise_data.recommendations.exercises and
        exercise_data.recommendations.exercises|length > 0 %}
        <div class="exercise-grid">
          {% for exercise_name, exercise_info in
          exercise_data.recommendations.exercises.items() %}
          <div
            class="exercise-card"
            onclick="selectExercise('{{ exercise_name }}', '{{ exercise_info.time_minutes }}')"
          >
            <div class="exercise-name">{{ exercise_name }}</div>
            <div class="exercise-time">{{ exercise_info.time_minutes }}분</div>
            <div class="exercise-calories">
              {{ "{:.0f}".format(exercise_info.calories_burned or 0) }}kcal 소모
            </div>
          </div>
          {% endfor %}
        </div>

        <div
          id="exercise-selection"
          style="
            margin-top: 24px;
            padding: 20px;
            background: rgba(52, 199, 89, 0.1);
            border-radius: 16px;
            display: none;
          "
        >
          <h4 style="margin: 0 0 12px 0; color: #1b5e20; font-weight: 600">
            선택된 운동
          </h4>
          <p
            id="exercise-details"
            style="margin: 0; font-size: 16px; color: #1d1d1f"
          ></p>
        </div>
        {% else %}
        <div class="no-exercise">
          <h3>
            {{ exercise_data.recommendations.message or '운동 추천 없음' }}
          </h3>
          <p>초과 칼로리: {{ exercise_data.excess_calories or 0 }} kcal</p>
          {% if exercise_data.excess_calories and exercise_data.excess_calories
          > 0 %}
          <p style="color: #ff3b30">
            초과 칼로리가 있지만 운동 추천이 생성되지 않았습니다.
          </p>
          {% endif %}
        </div>
        {% endif %} {% else %}
        <div class="no-exercise">
          <h3>운동 추천 데이터 없음</h3>
          <p>운동 추천을 계산할 수 없습니다.</p>
        </div>
        {% endif %}
      </div>

      <!-- 주간 영양소 분석 -->
      <div class="section">
        <h2>주간 영양소 분석</h2>

        <div class="nutrition-analysis">
          <div class="nutrition-stats">
            <div class="nutrition-stat">
              <div class="nutrition-percent">
                {{ "{:.1f}".format(nutrition_analysis.carb_percent or 0) }}%
              </div>
              <div class="nutrition-label">탄수화물</div>
            </div>
            <div class="nutrition-stat">
              <div class="nutrition-percent">
                {{ "{:.1f}".format(nutrition_analysis.protein_percent or 0) }}%
              </div>
              <div class="nutrition-label">단백질</div>
            </div>
            <div class="nutrition-stat">
              <div class="nutrition-percent">
                {{ "{:.1f}".format(nutrition_analysis.fat_percent or 0) }}%
              </div>
              <div class="nutrition-label">지방</div>
            </div>
            <div class="nutrition-stat">
              <div class="nutrition-percent">
                {{ "{:.0f}".format(nutrition_analysis.avg_daily_calories or 0)
                }}
              </div>
              <div class="nutrition-label">하루 평균</div>
            </div>
          </div>

          <h4 style="margin: 20px 0 12px 0; color: #1d1d1f; font-weight: 600">
            식단 개선 제안
          </h4>
          <ul class="alerts">
            {% for alert in nutrition_analysis.alerts %}
            <li
              class="{{ 'success' if '달성' in alert or '좋습니다' in alert else '' }}"
            >
              {{ alert }}
            </li>
            {% endfor %}
          </ul>
        </div>
      </div>

      <!-- 네비게이션 -->
      <div class="navigation">
        <a href="/" class="nav-link">메인 페이지</a>
        <a href="/dashboard" class="nav-link">대시보드</a>
        <a href="/profile" class="nav-link">프로필 설정</a>
        <a href="/upload_food" class="nav-link">음식 등록</a>
      </div>
    </div>

    <script>
      function selectExercise(name, timeMinutes) {
        const selection = document.getElementById("exercise-selection");
        const details = document.getElementById("exercise-details");

        details.innerHTML = `<strong>${name}</strong>를 <strong>${timeMinutes}분</strong> 하면 초과 칼로리를 소모할 수 있습니다.`;
        selection.style.display = "block";

        // 선택된 카드 강조
        document.querySelectorAll(".exercise-card").forEach((card) => {
          card.style.border = "2px solid #34c759";
          card.style.background = "rgba(52, 199, 89, 0.1)";
        });

        event.target.style.border = "3px solid #ff3b30";
        event.target.style.background = "rgba(255, 59, 48, 0.1)";
      }

      // 페이지 로드 시 초기화
      document.addEventListener("DOMContentLoaded", function () {
        console.log("운동 추천 페이지 로드 완료");
      });
    </script>
  </body>
</html>

<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Minimal Kakao Test</title>
  </head>
  <body>
    <h1>최소 코드 테스트</h1>
    <div
      id="map"
      style="width: 500px; height: 400px; border: 2px solid red"
    ></div>

    <script src="https://dapi.kakao.com/v2/maps/sdk.js?appkey=65093059a1b8c894bd576e566c080e3d"></script>
    <script>
      console.log("1. 스크립트 시작");
      console.log("2. Kakao 객체:", typeof kakao);

      if (typeof kakao !== "undefined") {
        console.log("3. Kakao.maps:", typeof kakao.maps);

        var container = document.getElementById("map");
        var options = {
          center: new kakao.maps.LatLng(37.5665, 126.978),
          level: 3,
        };

        var map = new kakao.maps.Map(container, options);
        console.log("4. 지도 생성 완료");
      } else {
        console.error("Kakao 객체가 없습니다!");
      }
    </script>
  </body>
</html>

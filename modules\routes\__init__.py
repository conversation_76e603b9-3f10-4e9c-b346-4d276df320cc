# -*- coding: utf-8 -*-
"""
Routes 모듈 초기화
모든 라우트 Blueprint들을 모아서 앱에 등록
"""

from .main_routes import main_bp
from .food_routes import food_bp
from .exercise_routes import exercise_bp
from .api_routes import api_bp

__all__ = ['main_bp', 'food_bp', 'exercise_bp', 'api_bp']


def register_blueprints(app):
    """모든 Blueprint를 Flask 앱에 등록"""
    app.register_blueprint(main_bp)
    app.register_blueprint(food_bp)
    app.register_blueprint(exercise_bp)
    app.register_blueprint(api_bp)
    
    return app

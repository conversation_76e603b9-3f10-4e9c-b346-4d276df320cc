<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 음식점 방문 및 칼로리 관리</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <!-- Kakao Map API -->
    <script type="text/javascript" src="//dapi.kakao.com/v2/maps/sdk.js?appkey=e86f572903492915e7f56248232b5fcc"></script>
    <!-- Socket.IO -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    
    <style>
        :root {
            --bs-primary: #007aff;
            --bs-success: #34c759;
            --bs-warning: #ff9500;
            --bs-danger: #ff3b30;
            --bs-info: #5ac8fa;
            --glass-bg: rgba(255, 255, 255, 0.9);
            --glass-border: rgba(255, 255, 255, 0.3);
        }
        
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', sans-serif;
        }
        
        /* Glassmorphism effect */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-radius: 20px;
        }
        
        /* Custom navbar */
        .navbar-custom {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
            color: #1d1d1f !important;
        }
        
        /* Hero section */
        .hero-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            padding: 3rem 0;
        }
        
        .hero-title {
            font-weight: 700;
            font-size: 2.5rem;
            color: #1d1d1f;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            color: #86868b;
            margin-bottom: 2rem;
        }
        
        /* Custom buttons */
        .btn-glass {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-glass:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
        }
        
        .btn-primary-custom {
            background: linear-gradient(135deg, #007aff, #0056cc);
            border: none;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
        }
        
        /* Map container */
        #map {
            height: 500px;
            width: 100%;
            border-radius: 20px;
        }
        
        .map-controls {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .map-control-btn {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            width: 44px;
            height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            margin-bottom: 10px;
        }
        
        .map-control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }
        
        /* Status panel */
        .status-panel {
            max-height: 400px;
            overflow-y: auto;
            scrollbar-width: thin;
        }
        
        .status-item {
            border-left: 4px solid #007aff;
            transition: all 0.3s ease;
        }
        
        .status-item:hover {
            transform: translateX(4px);
        }
        
        .status-item.entered {
            border-left-color: var(--bs-success);
            background-color: rgba(52, 199, 89, 0.1);
        }
        
        .status-item.left {
            border-left-color: var(--bs-warning);
            background-color: rgba(255, 149, 0, 0.1);
        }
        
        .status-item.notification {
            border-left-color: var(--bs-danger);
            background-color: rgba(255, 59, 48, 0.1);
        }
        
        .status-item.error {
            border-left-color: var(--bs-danger);
            background-color: rgba(255, 59, 48, 0.1);
        }
        
        .status-item.debug {
            border-left-color: #af52de;
            background-color: rgba(175, 82, 222, 0.1);
            font-size: 0.875rem;
            opacity: 0.8;
        }
        
        /* Feature cards */
        .feature-card {
            transition: all 0.3s ease;
            height: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        /* Location methods */
        .location-method {
            transition: all 0.3s ease;
            cursor: pointer;
            height: 100%;
        }
        
        .location-method:hover {
            transform: translateY(-2px);
            border-color: var(--bs-primary) !important;
            background-color: rgba(0, 122, 255, 0.05);
        }
        
        .location-method.active {
            border-color: var(--bs-primary) !important;
            background-color: rgba(0, 122, 255, 0.1);
            color: var(--bs-primary);
        }
        
        .location-method .method-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        /* Restaurant info window */
        .restaurant-info {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 20px;
            min-width: 220px;
            max-width: 300px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--glass-border);
        }
        
        /* Custom modal */
        .modal-content {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
        }
        
        .modal-backdrop.show {
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        
        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.4s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .slide-in {
            animation: slideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
        
        @keyframes slideIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }
        
        /* Responsive adjustments */
        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .hero-subtitle {
                font-size: 1rem;
            }
            
            #map {
                height: 400px;
            }
            
            .feature-card {
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-custom fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-utensils me-2"></i>
                AI 칼로리 매니저
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">
                            <i class="fas fa-home me-1"></i>홈
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="fas fa-chart-line me-1"></i>대시보드
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/upload_food">
                            <i class="fas fa-camera me-1"></i>음식 등록
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/exercise">
                            <i class="fas fa-running me-1"></i>운동 추천
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/profile">
                            <i class="fas fa-user me-1"></i>프로필
                        </a>
                    </li>
                </ul>
                
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="refreshLocation()">
                        <i class="fas fa-sync-alt me-1"></i>위치 새로고침
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="testNotification()">
                        <i class="fas fa-bell me-1"></i>알림 테스트
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section mt-5">
        <div class="container text-center">
            <h1 class="hero-title">AI 음식점 방문 및 칼로리 관리</h1>
            <p class="hero-subtitle">
                <i class="fas fa-location-dot me-2"></i>실시간 위치 추적 • 
                <i class="fas fa-robot me-2"></i>자동 음식 인식 • 
                <i class="fas fa-calculator me-2"></i>스마트 칼로리 계산
            </p>
            <div class="row justify-content-center">
                <div class="col-md-8">
                    <div class="btn-toolbar justify-content-center gap-3" role="toolbar">
                        <button class="btn btn-primary-custom" data-bs-toggle="collapse" data-bs-target="#locationPanel">
                            <i class="fas fa-cog me-2"></i>위치 설정
                        </button>
                        <button class="btn btn-glass" onclick="toggleDebugMode()" id="debug-toggle-btn">
                            <i class="fas fa-bug me-2"></i>디버그 모드
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="container-fluid px-4 mt-4">
        <!-- Location Control Panel -->
        <div class="collapse" id="locationPanel">
            <div class="card glass-card mb-4 fade-in">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-map-marker-alt me-2"></i>위치 설정
                    </h5>
                    <p class="card-text text-muted">
                        테스트를 위해 현재 위치를 변경할 수 있습니다. 식당 근처로 이동하여 알림 기능을 체험해보세요!
                    </p>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-md-3 col-sm-6">
                            <div class="card location-method active" onclick="setLocationMethod('drag')">
                                <div class="card-body text-center">
                                    <div class="method-icon">🖱️</div>
                                    <h6 class="card-title">지도에서 드래그</h6>
                                    <p class="card-text small">파란 마커를 드래그하여 위치 변경</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card location-method" onclick="setLocationMethod('click')">
                                <div class="card-body text-center">
                                    <div class="method-icon">👆</div>
                                    <h6 class="card-title">지도 클릭</h6>
                                    <p class="card-text small">지도를 클릭한 곳으로 즉시 이동</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card location-method" onclick="setLocationMethod('gps')">
                                <div class="card-body text-center">
                                    <div class="method-icon">🛰️</div>
                                    <h6 class="card-title">실제 GPS</h6>
                                    <p class="card-text small">브라우저의 위치 정보 사용</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card location-method" onclick="setLocationMethod('manual')">
                                <div class="card-body text-center">
                                    <div class="method-icon">⌨️</div>
                                    <h6 class="card-title">직접 입력</h6>
                                    <p class="card-text small">위도, 경도를 직접 입력</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="collapse" id="manualInput">
                        <div class="row g-2">
                            <div class="col-md-4">
                                <input type="number" class="form-control" id="lat-input" 
                                       placeholder="위도 (예: 35.837030)" step="0.000001">
                            </div>
                            <div class="col-md-4">
                                <input type="number" class="form-control" id="lng-input" 
                                       placeholder="경도 (예: 128.751693)" step="0.000001">
                            </div>
                            <div class="col-md-4">
                                <button class="btn btn-primary w-100" onclick="setManualLocation()">
                                    <i class="fas fa-check me-1"></i>위치 설정
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3" id="current-location">
                        <i class="fas fa-info-circle me-2"></i>현재 위치: 위치를 설정해주세요
                    </div>
                    
                    <div class="collapse" id="distance-info">
                        <div class="alert alert-warning mt-3">
                            <h6><i class="fas fa-ruler me-2"></i>근처 음식점 거리:</h6>
                            <div id="distance-list" class="small"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row g-4">
            <!-- Map Section -->
            <div class="col-lg-8">
                <div class="card glass-card">
                    <div class="card-body p-0 position-relative">
                        <div class="map-controls">
                            <button class="map-control-btn" id="find-location-btn" title="현재 위치로 이동">
                                <i class="fas fa-crosshairs"></i>
                            </button>
                        </div>
                        <div id="map"></div>
                    </div>
                </div>
            </div>
            
            <!-- Status Panel -->
            <div class="col-lg-4">
                <div class="card glass-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-bell me-2"></i>알림 내역
                        </h5>
                    </div>
                    <div class="card-body status-panel">
                        <div id="status">
                            <div class="alert alert-info status-item mb-2">
                                <i class="fas fa-cog me-2"></i>시스템 준비 중...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="row g-4 mt-2">
            <div class="col-12">
                <div class="card glass-card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-star me-2"></i>주요 기능
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-4">
                            <div class="col-md-3 col-sm-6">
                                <div class="card feature-card h-100 text-center">
                                    <div class="card-body">
                                        <div class="feature-icon text-primary">📍</div>
                                        <h6 class="card-title">스마트 위치 추적</h6>
                                        <p class="card-text small text-muted">
                                            음식점 근처에서 자동 감지 및 알림
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="card feature-card h-100 text-center">
                                    <div class="card-body">
                                        <div class="feature-icon text-warning">🔔</div>
                                        <h6 class="card-title">자동 알림</h6>
                                        <p class="card-text small text-muted">
                                            체류 시간 만족시 즉시 알림 및 리디렉션
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="card feature-card h-100 text-center">
                                    <div class="card-body">
                                        <div class="feature-icon text-success">🤖</div>
                                        <h6 class="card-title">AI 음식 인식</h6>
                                        <p class="card-text small text-muted">
                                            사진만 찍으면 음식과 칼로리를 자동 계산
                                        </p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 col-sm-6">
                                <div class="card feature-card h-100 text-center">
                                    <div class="card-body">
                                        <div class="feature-icon text-info">📊</div>
                                        <h6 class="card-title">영양 관리</h6>
                                        <p class="card-text small text-muted">
                                            일일/주간 칼로리 및 영양성분 분석
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Modal -->
    <div class="modal fade" id="notificationModal" tabindex="-1" aria-labelledby="notificationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content slide-in">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="notificationModalLabel">
                        <i class="fas fa-bell me-2"></i>알림
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="notification-body">
                    <!-- Dynamic content will be inserted here -->
                </div>
                <div class="modal-footer border-0" id="notification-footer">
                    <!-- Dynamic buttons will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Notification Modal -->
    <div class="modal fade" id="exerciseModal" tabindex="-1" aria-labelledby="exerciseModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content slide-in">
                <div class="modal-header border-0">
                    <h5 class="modal-title" id="exerciseModalLabel">
                        <i class="fas fa-running me-2"></i>운동 추천
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="exercise-body">
                    <!-- Dynamic content will be inserted here -->
                </div>
                <div class="modal-footer border-0" id="exercise-footer">
                    <!-- Dynamic buttons will be inserted here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        const socket = io();
        const restaurants = JSON.parse('{{restaurants|tojson|safe}}');
        const config = JSON.parse('{{config|tojson|safe}}');
        
        let map = null;
        let userMarker = null;
        let userCircle = null;
        let restaurantMarkers = {};
        let currentLocationMethod = 'drag';
        let clickListener = null;
        let currentRestaurant = null;
        let debugMode = false;
        let autoRedirectTimer = null;
        let isConnected = false;

        // Location state
        let currentPosition = {
            lat: 35.830569788,  // Default: IT관
            lng: 128.75399385
        };

        // Bootstrap toast for quick notifications
        function showToast(message, type = 'info') {
            const toastHtml = `
                <div class="toast align-items-center text-bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                </div>
            `;
            
            // Create toast container if not exists
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }
            
            toastContainer.insertAdjacentHTML('beforeend', toastHtml);
            
            const toastElement = toastContainer.lastElementChild;
            const toast = new bootstrap.Toast(toastElement);
            toast.show();
            
            // Remove toast element after it's hidden
            toastElement.addEventListener('hidden.bs.toast', () => {
                toastElement.remove();
            });
        }

        function initMap() {
            const container = document.getElementById('map');
            const options = {
                center: new kakao.maps.LatLng(currentPosition.lat, currentPosition.lng),
                level: 3
            };
            map = new kakao.maps.Map(container, options);

            // 지도 컨트롤 추가
            var mapTypeControl = new kakao.maps.MapTypeControl();
            map.addControl(mapTypeControl, kakao.maps.ControlPosition.TOPRIGHT);

            var zoomControl = new kakao.maps.ZoomControl();
            map.addControl(zoomControl, kakao.maps.ControlPosition.BOTTOMRIGHT);

            // 음식점 마커 생성
            createRestaurantMarkers();

            // 사용자 마커 생성
            createUserMarker();

            // 초기 위치 전송
            sendLocationUpdate();
            updateLocationDisplay();
            
            addStatusMessage('지도 초기화 완료');
        }

        function createRestaurantMarkers() {
            for (const restaurant of restaurants) {
                const markerImage = new kakao.maps.MarkerImage(
                    'https://cdn-icons-png.flaticon.com/512/3514/3514491.png',
                    new kakao.maps.Size(30, 30),
                    {offset: new kakao.maps.Point(15, 15)}
                );
                
                const marker = new kakao.maps.Marker({
                    position: new kakao.maps.LatLng(restaurant.lat, restaurant.lon),
                    map: map,
                    title: restaurant.name,
                    image: markerImage
                });

                const infowindow = new kakao.maps.InfoWindow({
                    content: `
                        <div class="restaurant-info">
                            <h6 class="fw-bold mb-1">${restaurant.name}</h6>
                            <p class="text-muted small mb-1">${restaurant.category}</p>
                            <p class="small mb-0">
                                <i class="fas fa-circle-info me-1"></i>
                                반경 ${config.detection_radius}m 이내에서 ${config.required_stay_time}초 체류시 알림
                            </p>
                        </div>
                    `
                });

                let isOpen = false;
                kakao.maps.event.addListener(marker, 'click', function() {
                    if (isOpen) {
                        infowindow.close();
                        isOpen = false;
                    } else {
                        // 다른 정보창들 닫기
                        Object.values(restaurantMarkers).forEach(rm => {
                            if (rm.infowindow) rm.infowindow.close();
                            rm.isOpen = false;
                        });
                        
                        infowindow.open(map, marker);
                        isOpen = true;
                    }
                });

                restaurantMarkers[restaurant.name] = {
                    marker: marker,
                    infowindow: infowindow,
                    isOpen: isOpen
                };
            }
            
            addStatusMessage(`${restaurants.length}개 음식점 마커 생성 완료`);
        }

        function createUserMarker() {
            const userMarkerImage = new kakao.maps.MarkerImage(
                'https://t1.daumcdn.net/localimg/localimages/07/mapapidoc/markerStar.png',
                new kakao.maps.Size(24, 35)
            );

            const position = new kakao.maps.LatLng(currentPosition.lat, currentPosition.lng);

            userMarker = new kakao.maps.Marker({
                position: position,
                map: map,
                image: userMarkerImage,
                draggable: true
            });

            // 사용자 반경 표시
            userCircle = new kakao.maps.Circle({
                center: position,
                radius: config.detection_radius,
                strokeWeight: 2,
                strokeColor: '#007aff',
                strokeOpacity: 0.6,
                fillColor: '#007aff',
                fillOpacity: 0.2,
                map: map
            });

            // 마커 드래그 이벤트
            kakao.maps.event.addListener(userMarker, 'dragend', function() {
                if (currentLocationMethod === 'drag') {
                    const pos = userMarker.getPosition();
                    updateUserPosition(pos.getLat(), pos.getLng());
                }
            });

            // 현 위치 찾기 버튼
            const findLocationBtn = document.getElementById('find-location-btn');
            findLocationBtn.addEventListener('click', function() {
                const pos = userMarker.getPosition();
                map.setCenter(pos);
                map.setLevel(3);
                showToast('현재 위치로 지도를 이동했습니다', 'info');
            });
            
            addStatusMessage('사용자 위치 마커 생성 완료');
        }

        function updateUserPosition(lat, lng) {
            currentPosition = { lat, lng };
            
            const position = new kakao.maps.LatLng(lat, lng);
            userMarker.setPosition(position);
            userCircle.setPosition(position);
            
            sendLocationUpdate();
            updateLocationDisplay();
            updateDistanceInfo();
        }

        function sendLocationUpdate() {
            if (isConnected) {
                socket.emit('update_location', {
                    lat: currentPosition.lat,
                    lon: currentPosition.lng
                });
                
                if (debugMode) {
                    addStatusMessage(`위치 전송: ${currentPosition.lat.toFixed(6)}, ${currentPosition.lng.toFixed(6)}`, 'debug');
                }
            }
        }

        function updateLocationDisplay() {
            const display = document.getElementById('current-location');
            display.innerHTML = `
                <i class="fas fa-map-marker-alt me-2"></i>
                현재 위치: ${currentPosition.lat.toFixed(6)}, ${currentPosition.lng.toFixed(6)}
            `;
        }

        function updateDistanceInfo() {
            const distanceDiv = document.getElementById('distance-info');
            const distanceList = document.getElementById('distance-list');
            
            if (!debugMode) {
                document.getElementById('distance-info').classList.remove('show');
                return;
            }
            
            // 모든 음식점과의 거리 계산
            const distances = [];
            restaurants.forEach(restaurant => {
                const distance = calculateDistance(
                    currentPosition.lat, currentPosition.lng,
                    restaurant.lat, restaurant.lon
                );
                distances.push({
                    name: restaurant.name,
                    distance: distance,
                    inRange: distance <= config.detection_radius
                });
            });
            
            // 거리순 정렬
            distances.sort((a, b) => a.distance - b.distance);
            
            // 상위 10개만 표시
            let html = '';
            distances.slice(0, 10).forEach(item => {
                const icon = item.inRange ? '🎯' : '📍';
                const badgeClass = item.inRange ? 'text-bg-success' : 'text-bg-secondary';
                html += `
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>${icon} ${item.name}</span>
                        <span class="badge ${badgeClass}">${item.distance.toFixed(1)}m</span>
                    </div>
                `;
            });
            
            distanceList.innerHTML = html;
            document.getElementById('distance-info').classList.add('show');
        }

        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371000; // 지구 반지름 (미터)
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }

        function toggleDebugMode() {
            debugMode = !debugMode;
            const btn = document.getElementById('debug-toggle-btn');
            
            if (debugMode) {
                btn.innerHTML = '<i class="fas fa-bug me-2"></i>디버그 OFF';
                btn.className = 'btn btn-danger';
                addStatusMessage('디버그 모드 활성화');
                updateDistanceInfo();
                showToast('디버그 모드가 활성화되었습니다', 'success');
            } else {
                btn.innerHTML = '<i class="fas fa-bug me-2"></i>디버그 모드';
                btn.className = 'btn btn-glass';
                addStatusMessage('디버그 모드 비활성화');
                document.getElementById('distance-info').classList.remove('show');
                showToast('디버그 모드가 비활성화되었습니다', 'warning');
            }
        }

        function testNotification() {
            addStatusMessage('테스트 알림 요청');
            socket.emit('test_notification', {
                restaurant: '테스트 음식점'
            });
            showToast('테스트 알림을 요청했습니다', 'info');
        }

        function setLocationMethod(method) {
            currentLocationMethod = method;
            
            // UI 업데이트
            document.querySelectorAll('.location-method').forEach(el => {
                el.classList.remove('active');
            });
            event.target.closest('.location-method').classList.add('active');
            
            // 입력 필드 표시/숨김
            const manualInput = document.getElementById('manualInput');
            if (method === 'manual') {
                manualInput.classList.add('show');
            } else {
                manualInput.classList.remove('show');
            }
            
            // 기존 클릭 리스너 제거
            if (clickListener) {
                kakao.maps.event.removeListener(map, 'click', clickListener);
                clickListener = null;
            }
            
            // 방법별 설정
            switch (method) {
                case 'drag':
                    userMarker.setDraggable(true);
                    addStatusMessage('드래그 모드: 파란 마커를 드래그하여 위치를 변경하세요');
                    showToast('드래그 모드로 변경되었습니다', 'info');
                    break;
                    
                case 'click':
                    userMarker.setDraggable(false);
                    clickListener = kakao.maps.event.addListener(map, 'click', function(mouseEvent) {
                        const latlng = mouseEvent.latLng;
                        updateUserPosition(latlng.getLat(), latlng.getLng());
                        addStatusMessage(`클릭 위치로 이동: ${latlng.getLat().toFixed(6)}, ${latlng.getLng().toFixed(6)}`);
                    });
                    addStatusMessage('클릭 모드: 지도를 클릭하여 위치를 변경하세요');
                    showToast('클릭 모드로 변경되었습니다', 'info');
                    break;
                    
                case 'gps':
                    userMarker.setDraggable(false);
                    if (navigator.geolocation) {
                        addStatusMessage('GPS 위치를 가져오는 중...');
                        showToast('GPS 위치를 가져오는 중...', 'warning');
                        navigator.geolocation.getCurrentPosition(
                            function(position) {
                                const lat = position.coords.latitude;
                                const lng = position.coords.longitude;
                                updateUserPosition(lat, lng);
                                map.setCenter(new kakao.maps.LatLng(lat, lng));
                                addStatusMessage(`GPS 위치로 설정: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
                                showToast('GPS 위치로 설정되었습니다', 'success');
                            },
                            function(error) {
                                addStatusMessage('GPS 위치를 가져올 수 없습니다. 다른 방법을 사용해주세요.', 'error');
                                showToast('GPS 위치를 가져올 수 없습니다', 'danger');
                                setLocationMethod('drag');
                            }
                        );
                    } else {
                        addStatusMessage('이 브라우저는 GPS를 지원하지 않습니다. 다른 방법을 사용해주세요.', 'error');
                        showToast('이 브라우저는 GPS를 지원하지 않습니다', 'danger');
                        setLocationMethod('drag');
                    }
                    break;
                    
                case 'manual':
                    userMarker.setDraggable(false);
                    addStatusMessage('수동 입력 모드: 위도와 경도를 직접 입력하세요');
                    showToast('수동 입력 모드로 변경되었습니다', 'info');
                    break;
            }
        }

        function setManualLocation() {
            const latInput = document.getElementById('lat-input');
            const lngInput = document.getElementById('lng-input');
            
            const lat = parseFloat(latInput.value);
            const lng = parseFloat(lngInput.value);
            
            if (isNaN(lat) || isNaN(lng)) {
                showToast('올바른 위도와 경도를 입력해주세요', 'danger');
                return;
            }
            
            if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
                showToast('위도는 -90~90, 경도는 -180~180 범위 내에서 입력해주세요', 'danger');
                return;
            }
            
            updateUserPosition(lat, lng);
            map.setCenter(new kakao.maps.LatLng(lat, lng));
            addStatusMessage(`수동 위치 설정: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
            showToast('위치가 설정되었습니다', 'success');
        }

        function refreshLocation() {
            sendLocationUpdate();
            addStatusMessage(`위치 새로고침: ${new Date().toLocaleTimeString()}`);
            showToast('위치가 새로고침되었습니다', 'info');
        }

        function addStatusMessage(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            
            const alertClass = {
                'info': 'alert-info',
                'entered': 'alert-success',
                'left': 'alert-warning', 
                'notification': 'alert-danger',
                'error': 'alert-danger',
                'debug': 'alert-secondary'
            }[type] || 'alert-info';
            
            const iconClass = {
                'info': 'fas fa-info-circle',
                'entered': 'fas fa-door-open',
                'left': 'fas fa-door-closed',
                'notification': 'fas fa-bell',
                'error': 'fas fa-exclamation-triangle',
                'debug': 'fas fa-bug'
            }[type] || 'fas fa-info-circle';
            
            const status = document.createElement('div');
            status.className = `alert ${alertClass} status-item mb-2 py-2`;
            status.innerHTML = `
                <i class="${iconClass} me-2"></i>
                <small class="text-muted">${new Date().toLocaleTimeString()}</small> - ${message}
            `;
            
            statusDiv.appendChild(status);
            
            // 최근 15개만 유지
            while (statusDiv.children.length > 15) {
                statusDiv.removeChild(statusDiv.firstChild);
            }
            
            // 자동 스크롤 (최신 메시지가 보이도록)
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        // Bootstrap Modal을 사용한 알림 시스템
        function showNotification(data) {
            currentRestaurant = data.restaurant;
            
            const modalBody = document.getElementById('notification-body');
            const modalFooter = document.getElementById('notification-footer');
            
            modalBody.innerHTML = `
                <div class="text-center mb-3">
                    <i class="fas fa-utensils fa-3x text-primary mb-3"></i>
                    <h5>${data.message}</h5>
                </div>
                <div class="row g-3">
                    <div class="col-6">
                        <div class="text-center">
                            <i class="fas fa-ruler text-info"></i>
                            <div class="small text-muted">거리</div>
                            <div class="fw-bold">${data.distance}m</div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-center">
                            <i class="fas fa-clock text-warning"></i>
                            <div class="small text-muted">체류 시간</div>
                            <div class="fw-bold">${data.stay_duration}초</div>
                        </div>
                    </div>
                </div>
                <div class="alert alert-info mt-3 mb-0">
                    <i class="fas fa-clock me-2"></i>
                    <span id="countdown-text">10초 후 자동으로 음식 등록 페이지로 이동합니다</span>
                </div>
            `;
            
            modalFooter.innerHTML = `
                <button type="button" class="btn btn-primary" onclick="goToRegister()">
                    <i class="fas fa-camera me-2"></i>지금 등록하기
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>취소
                </button>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
            modal.show();
            
            // 자동 리디렉션 타이머 (10초)
            startAutoRedirectTimer(data.upload_url, 10);
            
            addStatusMessage(`알림 표시: ${data.restaurant}`, 'notification');
        }

        function showExerciseNotification(data) {
            const modalBody = document.getElementById('exercise-body');
            const modalFooter = document.getElementById('exercise-footer');
            
            modalBody.innerHTML = `
                <div class="text-center mb-3">
                    <i class="fas fa-running fa-3x text-success mb-3"></i>
                    <h5>${data.message}</h5>
                </div>
                <div class="alert alert-info mb-3">
                    <i class="fas fa-fire me-2"></i>
                    오늘 섭취 칼로리: <strong>${data.daily_calories || 0}캠</strong>
                </div>
                <div class="text-center">
                    <p class="text-muted">운동으로 건강을 챙겨보세요!</p>
                </div>
                ${!data.test ? `
                <div class="alert alert-warning mt-3 mb-0">
                    <i class="fas fa-clock me-2"></i>
                    <span id="exercise-countdown-text">8초 후 자동으로 운동 추천 페이지로 이동합니다</span>
                </div>
                ` : ''}
            `;
            
            modalFooter.innerHTML = `
                <button type="button" class="btn btn-success" onclick="goToExercise()">
                    <i class="fas fa-running me-2"></i>운동 추천 보기
                </button>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>다음에
                </button>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('exerciseModal'));
            modal.show();
            
            // 자동 리디렉션 타이머 (8초, 테스트가 아닌 경우만)
            if (!data.test) {
                startAutoRedirectTimer(data.exercise_url || '/exercise', 8, 'exercise-countdown-text');
            }
            
            addStatusMessage(`운동 알림 표시: ${data.trigger_reason}`, 'notification');
        }

        function startAutoRedirectTimer(url, seconds, elementId = 'countdown-text') {
            let timeLeft = seconds;
            const countdownElement = document.getElementById(elementId);
            
            autoRedirectTimer = setInterval(() => {
                timeLeft--;
                if (countdownElement) {
                    if (elementId === 'countdown-text') {
                        countdownElement.textContent = `${timeLeft}초 후 자동으로 음식 등록 페이지로 이동합니다`;
                    } else {
                        countdownElement.textContent = `${timeLeft}초 후 자동으로 운동 추천 페이지로 이동합니다`;
                    }
                }
                
                if (timeLeft <= 0) {
                    clearAutoRedirectTimer();
                    hideAllModals();
                    window.location.href = url;
                }
            }, 1000);
        }

        function clearAutoRedirectTimer() {
            if (autoRedirectTimer) {
                clearInterval(autoRedirectTimer);
                autoRedirectTimer = null;
            }
        }

        function hideAllModals() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                const modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            });
        }

        function goToRegister() {
            clearAutoRedirectTimer();
            hideAllModals();
            if (currentRestaurant) {
                window.location.href = `/upload_food?restaurant=${encodeURIComponent(currentRestaurant)}`;
            } else {
                window.location.href = '/upload_food';
            }
        }

        function goToExercise() {
            clearAutoRedirectTimer();
            hideAllModals();
            window.location.href = '/exercise';
        }

        // SocketIO Event Handlers
        socket.on('connect', () => {
            isConnected = true;
            addStatusMessage('서버 연결됨');
            showToast('서버에 연결되었습니다', 'success');
        });

        socket.on('disconnect', () => {
            isConnected = false;
            addStatusMessage('서버 연결 해제됨', 'error');
            showToast('서버와의 연결이 끊어졌습니다', 'danger');
        });

        socket.on('status_update', (data) => {
            // 입장/퇴장 메시지는 표시하지 않음 (사용자 요청에 따라 제거)
        });

        socket.on('notification', (data) => {
            addStatusMessage(`알림 발송: ${data.restaurant} (거리: ${data.distance}m, 체류: ${data.stay_duration}초)`, 'notification');
            showNotification(data);
        });

        socket.on('exercise_notification', (data) => {
            addStatusMessage(`운동 알림: ${data.trigger_reason}`, 'notification');
            showExerciseNotification(data);
        });

        socket.on('exercise_status_update', (data) => {
            if (debugMode) {
                addStatusMessage(`식사: ${data.meal_count}번, 시간: ${data.current_hour}시, 알림: ${data.should_recommend ? '✅' : '❌'}`, 'debug');
            }
        });

        socket.on('config_info', (data) => {
            Object.assign(config, data);
            
            if (userCircle) {
                userCircle.setRadius(config.detection_radius);
            }
            
            addStatusMessage(`설정 수신: 반경 ${config.detection_radius}m, 체류시간 ${config.required_stay_time}초`);
        });

        socket.on('error', (data) => {
            addStatusMessage(`오류: ${data.message}`, 'error');
            showToast(`오류: ${data.message}`, 'danger');
        });

        // 1초마다 위치 전송
        setInterval(() => {
            if (isConnected) {
                sendLocationUpdate();
            }
        }, 1000);

        // 카카오맵 로드 후 초기화
        kakao.maps.load(() => {
            initMap();
            setLocationMethod('drag');
            addStatusMessage('시스템 준비 완료! 음식점 근처로 이동하면 자동 알림이 나타납니다.');
            showToast('시스템이 준비되었습니다!', 'success');
        });
    </script>
</body>
</html>
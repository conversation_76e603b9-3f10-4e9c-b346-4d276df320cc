# -*- coding: utf-8 -*-
"""
Data Processing Utilities
데이터 변환, 검증, 처리 관련 유틸리티 함수들
"""

import json
import logging
from datetime import datetime, date, timedelta
from pathlib import Path
from typing import Any, Optional, Union, List, Dict

logger = logging.getLogger(__name__)


def safe_float_conversion(value: Any, default: float = 0.0) -> float:
    """안전한 float 변환 함수"""
    try:
        if value is None or value == '' or value == 'None' or value == 'null':
            return default
        
        # 문자열인 경우 처리
        if isinstance(value, str):
            # 공백과 단위 제거
            value = value.strip().replace('칼', '').replace('kcal', '').replace('cal', '')
            
            # 비어있으면 기본값 리턴
            if not value:
                return default
            
            # 숫자가 아닌 문자 제거 (소수점과 마이너스 제외)
            import re
            value = re.sub(r'[^0-9.-]', '', value)
            if not value or value == '.' or value == '-':
                return default
        
        # 이미 숫자 타입인 경우
        if isinstance(value, (int, float)):
            return float(value)
            
        return float(value)
    except (ValueError, TypeError) as e:
        logger.debug(f"float 변환 실패: {value} -> {default} (error: {e})")
        return default


def safe_int_conversion(value: Any, default: int = 0) -> int:
    """안전한 int 변환 함수"""
    try:
        float_val = safe_float_conversion(value, default)
        return int(float_val)
    except (ValueError, TypeError):
        return default


def load_json_file(file_path: Union[str, Path], default: Any = None) -> Any:
    """JSON 파일을 안전하게 로드"""
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return default or []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
            
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.error(f"JSON 파일 로드 실패: {file_path}, 오류: {e}")
        return default or []
    except Exception as e:
        logger.error(f"예상치 못한 JSON 로드 오류: {file_path}, 오류: {e}")
        return default or []


def save_json_file(data: Any, file_path: Union[str, Path], indent: int = 2) -> bool:
    """JSON 파일을 안전하게 저장"""
    try:
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=indent)
        
        logger.debug(f"JSON 파일 저장 성공: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"JSON 파일 저장 실패: {file_path}, 오류: {e}")
        return False


def filter_records_by_date(records: List[Dict], target_date: Union[str, date], 
                          date_field: str = 'timestamp') -> List[Dict]:
    """날짜로 기록 필터링"""
    try:
        if isinstance(target_date, str):
            if 'T' in target_date:  # ISO format with time
                target_date = datetime.fromisoformat(target_date).date()
            else:
                target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
        
        filtered = []
        for record in records:
            try:
                record_date = datetime.fromisoformat(record[date_field]).date()
                if record_date == target_date:
                    filtered.append(record)
            except (KeyError, ValueError) as e:
                logger.debug(f"날짜 필터링 중 레코드 스킵: {e}")
                continue
        
        return filtered
        
    except Exception as e:
        logger.error(f"날짜 필터링 오류: {e}")
        return []


def filter_records_by_date_range(records: List[Dict], start_date: Union[str, date], 
                                end_date: Union[str, date], date_field: str = 'timestamp') -> List[Dict]:
    """날짜 범위로 기록 필터링"""
    try:
        # 날짜 변환
        if isinstance(start_date, str):
            start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
        
        filtered = []
        for record in records:
            try:
                record_date = datetime.fromisoformat(record[date_field]).date()
                if start_date <= record_date <= end_date:
                    filtered.append(record)
            except (KeyError, ValueError):
                continue
        
        return filtered
        
    except Exception as e:
        logger.error(f"날짜 범위 필터링 오류: {e}")
        return []


def calculate_nutrition_summary(records: List[Dict]) -> Dict[str, float]:
    """영양소 요약 계산"""
    summary = {
        'total_calories': 0.0,
        'total_protein': 0.0,
        'total_carbs': 0.0,
        'total_fat': 0.0,
        'meal_count': len(records)
    }
    
    for record in records:
        summary['total_calories'] += safe_float_conversion(record.get('calories', 0))
        summary['total_protein'] += safe_float_conversion(record.get('protein', 0))
        summary['total_carbs'] += safe_float_conversion(record.get('carbs', 0))
        summary['total_fat'] += safe_float_conversion(record.get('fat', 0))
    
    return summary


def calculate_weekly_data(records: List[Dict], target_date: Optional[date] = None) -> List[Dict]:
    """주간 데이터 계산 (최근 7일)"""
    if target_date is None:
        target_date = date.today()
    
    weekly_data = []
    
    for i in range(7):
        day_date = target_date - timedelta(days=6-i)
        day_records = filter_records_by_date(records, day_date)
        day_summary = calculate_nutrition_summary(day_records)
        
        weekly_data.append({
            'date': day_date.strftime('%Y-%m-%d'),
            'date_display': day_date.strftime('%m/%d'),
            'weekday': day_date.strftime('%a'),
            **day_summary
        })
    
    return weekly_data


def validate_nutrition_data(data: Dict) -> Dict[str, Any]:
    """영양소 데이터 유효성 검사"""
    result = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'cleaned_data': {}
    }
    
    # 필수 필드
    required_fields = ['calories', 'protein', 'carbs', 'fat']
    
    for field in required_fields:
        value = safe_float_conversion(data.get(field, 0))
        
        # 음수 값 검사
        if value < 0:
            result['errors'].append(f'{field}는 음수일 수 없습니다')
            result['valid'] = False
        
        # 상식적인 범위 검사
        max_values = {
            'calories': 5000,  # 하루 최대 칼로리
            'protein': 500,    # 하루 최대 단백질 (g)
            'carbs': 1000,     # 하루 최대 탄수화물 (g)
            'fat': 300         # 하루 최대 지방 (g)
        }
        
        if value > max_values[field]:
            result['warnings'].append(f'{field} 값이 비정상적으로 높습니다: {value}')
        
        result['cleaned_data'][field] = value
    
    return result


def format_number(value: Union[int, float], decimal_places: int = 1) -> str:
    """숫자를 사용자 친화적으로 포맷팅"""
    try:
        if isinstance(value, (int, float)):
            if decimal_places == 0:
                return f"{value:,.0f}"
            else:
                return f"{value:,.{decimal_places}f}"
        return str(value)
    except (ValueError, TypeError):
        return "0"


def get_date_display(date_value: Union[str, date, datetime]) -> str:
    """날짜를 사용자 친화적으로 표시"""
    try:
        if isinstance(date_value, str):
            if 'T' in date_value:  # ISO format with time
                date_value = datetime.fromisoformat(date_value).date()
            else:
                date_value = datetime.strptime(date_value, '%Y-%m-%d').date()
        elif isinstance(date_value, datetime):
            date_value = date_value.date()
        
        today = date.today()
        
        if date_value == today:
            return "오늘"
        elif date_value == today - timedelta(days=1):
            return "어제"
        elif date_value == today + timedelta(days=1):
            return "내일"
        else:
            return date_value.strftime('%m월 %d일')
            
    except Exception:
        return str(date_value)

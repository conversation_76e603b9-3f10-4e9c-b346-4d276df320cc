# -*- coding: utf-8 -*-
"""
Food Routes
음식 등록, 영수증 OCR, 음식 분석 관련 라우트
"""

from flask import Blueprint, render_template, request, jsonify
from datetime import datetime
import requests
import logging
from pathlib import Path

from modules.config import config
from modules.utils import (
    allowed_file, save_uploaded_file, encode_image_to_base64,
    load_json_file, save_json_file, safe_float_conversion
)

logger = logging.getLogger(__name__)

# Blueprint 생성
food_bp = Blueprint('food', __name__)

# 영수증 OCR을 위한 라이브러리 (optional)
try:
    from google.cloud import vision
    GOOGLE_VISION_AVAILABLE = True
except ImportError:
    GOOGLE_VISION_AVAILABLE = False


@food_bp.route('/upload')
@food_bp.route('/upload_food', methods=['GET', 'POST'])
def upload_food():
    """음식 등록 페이지 및 처리"""
    if request.method == 'GET':
        # 음식 등록 페이지 표시
        restaurant_name = request.args.get('restaurant', None)
        return render_template('upload_food.html', restaurant_name=restaurant_name)
    
    # POST: 이미지 업로드 및 분석
    try:
        logger.info("음식 등록 요청 수신")
        
        # 요청 데이터 확인
        restaurant_name = request.form.get('restaurant_name', '알 수 없는 음식점')
        
        if 'food_image' not in request.files:
            return jsonify({
                'success': False,
                'error': '이미지 파일이 업로드되지 않았습니다.'
            }), 400
        
        file = request.files['food_image']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '파일이 선택되지 않았습니다.'
            }), 400
        
        # 파일 저장
        if file and allowed_file(file.filename, config.ALLOWED_EXTENSIONS):
            file_path = save_uploaded_file(file, config.UPLOAD_FOLDER, "food")
            
            if not file_path:
                return jsonify({
                    'success': False,
                    'error': '파일 저장에 실패했습니다.'
                }), 500
            
            logger.info(f"이미지 저장: {file_path}")
            
            # Analyzer API로 분석 요청
            try:
                analysis_result = analyze_food_image(file_path)
                
                if analysis_result.get('success'):
                    # 분석 성공 - 선택 UI 표시
                    logger.info(f"음식 분석 성공: {analysis_result.get('method', 'unknown')}")
                    
                    return jsonify({
                        'success': True,
                        'requires_selection': True,
                        'analysis_result': analysis_result,
                        'image_path': file_path,
                        'restaurant_name': restaurant_name
                    })
                else:
                    # 분석 실패
                    logger.warning(f"음식 분석 실패: {analysis_result.get('error', 'Unknown error')}")
                    
                    return jsonify({
                        'success': False,
                        'error': '음식 분석에 실패했습니다. 다른 사진으로 다시 시도해주세요.',
                        'debug_info': analysis_result
                    }), 400
                    
            except Exception as api_error:
                logger.error(f"Analyzer API 오류: {api_error}")
                return jsonify({
                    'success': False,
                    'error': 'AI 분석 서비스에 연결할 수 없습니다. 서버 상태를 확인해주세요.',
                    'debug_info': {
                        'api_url': config.ANALYZER_API_URL,
                        'error': str(api_error)
                    }
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': '지원되지 않는 파일 형식입니다. (JPG, PNG, GIF, WebP 지원)'
            }), 400
            
    except Exception as e:
        logger.error(f"음식 등록 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'서버 오류가 발생했습니다: {str(e)}'
        }), 500


@food_bp.route('/confirm_food', methods=['POST'])
def confirm_food():
    """선택된 음식 확인 및 저장"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'JSON 데이터가 필요합니다.'
            }), 400
        
        restaurant_name = data.get('restaurant_name', '알 수 없는 음식점')
        selected_food = data.get('selected_food')
        custom_food_name = data.get('custom_food_name', '').strip()
        custom_weight = data.get('custom_weight')
        
        # 음식 정보 결정
        if custom_food_name:
            # 사용자 직접 입력
            food_name = custom_food_name
            food_name_ko = custom_food_name
            food_name_en = None
            weight = float(custom_weight) if custom_weight else 150.0
            
            # 기본 영양정보 추정
            nutrition = {
                'calories': weight * 1.5,  # 추정 칼로리
                'protein': weight * 0.1,
                'carbs': weight * 0.2,
                'fat': weight * 0.05,
                'weight_grams': weight
            }
            nutrition_source = 'user_input'
            
        elif selected_food:
            # AI 분석 결과 사용
            food_name_ko = selected_food.get('food_name_ko')
            food_name_en = selected_food.get('food_name_en')
            food_name = food_name_ko if food_name_ko else (food_name_en if food_name_en else '알 수 없는 음식')
            nutrition = selected_food.get('nutrition', {})
            nutrition_source = selected_food.get('nutrition_source', 'ai_analysis')
            
        else:
            return jsonify({
                'success': False,
                'error': '음식을 선택하거나 직접 입력해주세요.'
            }), 400
        
        # 데이터베이스에 저장
        food_record = {
            'restaurant_name': restaurant_name,
            'food_name': food_name,
            'food_name_ko': food_name_ko,
            'food_name_en': food_name_en,
            'calories': nutrition.get('calories', 0),
            'protein': nutrition.get('protein', 0),
            'carbs': nutrition.get('carbs', 0),
            'fat': nutrition.get('fat', 0),
            'weight_grams': nutrition.get('weight_grams', 150),
            'nutrition_source': nutrition_source,
            'confidence': selected_food.get('confidence', 0.0) if selected_food else 0.0,
            'timestamp': datetime.now().isoformat(),
            'image_path': data.get('image_path')
        }
        
        # DB 저장
        record_id = save_food_record(food_record)
        
        logger.info(f"음식 등록 완료: {food_name} ({nutrition.get('calories', 0):.0f} kcal)")
        
        return jsonify({
            'success': True,
            'message': '음식이 성공적으로 등록되었습니다!',
            'record_id': record_id,
            'food_name': food_name,
            'calories': nutrition.get('calories', 0),
            'nutrition_source': nutrition_source,
            'redirect': '/dashboard'
        })
        
    except Exception as e:
        logger.error(f"음식 확인 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'저장 중 오류가 발생했습니다: {str(e)}'
        }), 500


@food_bp.route('/receipt')
def receipt_upload():
    """영수증 업로드 페이지"""
    # Google Vision API 사용 가능 여부 확인
    api_available = (
        GOOGLE_VISION_AVAILABLE and 
        config.GOOGLE_CREDENTIALS_PATH.exists()
    )
    
    # 지원되는 음식 목록
    supported_foods = get_supported_receipt_foods()
    
    return render_template('receipt_upload.html', 
                          google_vision_available=api_available,
                          supported_foods=supported_foods[:20])


@food_bp.route('/upload_receipt', methods=['POST'])
def upload_receipt():
    """영수증 이미지 분석 및 음식 추출"""
    try:
        if 'receipt_image' not in request.files:
            return jsonify({
                'success': False,
                'error': '이미지 파일이 업로드되지 않았습니다.'
            }), 400
        
        file = request.files['receipt_image']
        restaurant_name = request.form.get('restaurant_name', '영수증 분석')
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '파일이 선택되지 않았습니다.'
            }), 400
        
        # 파일 저장
        if file and allowed_file(file.filename, config.ALLOWED_EXTENSIONS):
            file_path = save_uploaded_file(file, config.UPLOAD_FOLDER, "receipt")
            
            if not file_path:
                return jsonify({
                    'success': False,
                    'error': '파일 저장에 실패했습니다.'
                }), 500
            
            logger.info(f"영수증 이미지 저장: {file_path}")
            
            # OCR 텍스트 추출
            ocr_text = google_ocr_text_from_image(file_path)
            
            # OCR 실패 시 처리
            if ocr_text.startswith("❌"):
                return jsonify({
                    'success': False,
                    'error': 'OCR 처리에 실패했습니다. 이미지가 선명한지 확인해주세요.',
                    'ocr_error': ocr_text
                }), 400
            
            # 음식명 추출
            food_list = extract_food_names_from_receipt(ocr_text)
            
            if not food_list:
                supported_foods = get_supported_receipt_foods()
                return jsonify({
                    'success': False,
                    'error': '영수증에서 인식 가능한 음식을 찾지 못했습니다.',
                    'ocr_text': ocr_text,
                    'detected_foods': [],
                    'supported_foods_sample': supported_foods[:10]
                }), 400
            
            # 영양 정보 가져오기
            food_infos = []
            total_calories = 0
            
            for food_name in food_list:
                nutrition_info = get_simple_nutrition(food_name)
                if nutrition_info:
                    food_infos.append(nutrition_info)
                    total_calories += nutrition_info['calories']
                    
            logger.info(f"영수증 분석 완료: {len(food_infos)}개 음식, {total_calories} kcal")
            
            return jsonify({
                'success': True,
                'ocr_text': ocr_text,
                'detected_foods': food_list,
                'food_infos': food_infos,
                'total_calories': total_calories,
                'restaurant_name': restaurant_name,
                'image_path': file_path,
                'ocr_method': 'google_vision'
            })
        
        else:
            return jsonify({
                'success': False,
                'error': '지원되지 않는 파일 형식입니다. (JPG, PNG, GIF, WebP 지원)'
            }), 400
            
    except Exception as e:
        logger.error(f"영수증 분석 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'서버 오류가 발생했습니다: {str(e)}'
        }), 500


@food_bp.route('/save_receipt_foods', methods=['POST'])
def save_receipt_foods():
    """영수증에서 추출한 음식들을 데이터베이스에 저장"""
    try:
        data = request.get_json()
        
        if not data or 'food_infos' not in data:
            return jsonify({
                'success': False,
                'error': '음식 정보가 필요합니다.'
            }), 400
        
        restaurant_name = data.get('restaurant_name', '영수증 분석')
        food_infos = data.get('food_infos', [])
        image_path = data.get('image_path')
        
        saved_records = []
        
        # 각 음식을 개별적으로 저장
        for food_info in food_infos:
            food_record = {
                'restaurant_name': restaurant_name,
                'food_name': food_info['food'],
                'food_name_ko': food_info['food'],
                'food_name_en': None,
                'calories': food_info['calories'],
                'protein': food_info['protein'],
                'carbs': food_info['carbs'],
                'fat': food_info['fat'],
                'weight_grams': 100,  # 기본값
                'nutrition_source': 'receipt_ocr',
                'confidence': 0.85,
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path,
                'method': 'receipt_ocr',
                'ocr_source': 'google_vision'
            }
            
            record_id = save_food_record(food_record)
            saved_records.append({
                'id': record_id,
                'food_name': food_info['food'],
                'calories': food_info['calories']
            })
        
        logger.info(f"영수증 음식 {len(saved_records)}개 저장 완료")
        
        return jsonify({
            'success': True,
            'message': f'영수증에서 {len(saved_records)}개 음식이 성공적으로 등록되었습니다!',
            'saved_records': saved_records,
            'total_foods': len(saved_records),
            'total_calories': sum(record.get('calories', 0) for record in saved_records),
            'method': 'receipt_ocr',
            'redirect': '/dashboard'
        })
        
    except Exception as e:
        logger.error(f"영수증 음식 저장 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'저장 중 오류가 발생했습니다: {str(e)}'
        }), 500


# === 유틸리티 함수들 ===

def analyze_food_image(image_path: str) -> dict:
    """이미지를 Analyzer API로 분석"""
    try:
        # 이미지를 base64로 인코딩
        image_base64 = encode_image_to_base64(image_path)
        
        if not image_base64:
            return {
                'success': False,
                'error': 'Image encoding failed'
            }
        
        # Analyzer API 호출
        api_url = f"{config.ANALYZER_API_URL}/analyze"
        
        payload = {
            'image': image_base64
        }
        
        response = requests.post(
            api_url,
            json=payload,
            timeout=config.API_TIMEOUT
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # 결과를 우리 형식에 맞게 변환
            if result.get('success'):
                predictions = []
                
                # 메인 결과 추가
                main_result = {
                    'food_name_ko': result['data'].get('food_name_ko'),
                    'food_name_en': result['data'].get('food_name_en'),
                    'confidence': result['data'].get('confidence', 0),
                    'nutrition': result['data'].get('nutrition', {}),
                    'nutrition_source': 'analyzer_api'
                }
                predictions.append(main_result)
                
                # 대체 옵션들 추가
                for alt in result.get('korean_alternatives', [])[:2]:
                    alt_result = {
                        'food_name_ko': alt.get('food_name_ko'),
                        'food_name_en': alt.get('food_name_en'),
                        'confidence': alt.get('confidence', 0),
                        'nutrition': alt.get('nutrition', {}),
                        'nutrition_source': 'analyzer_api'
                    }
                    predictions.append(alt_result)
                
                for alt in result.get('general_alternatives', [])[:2]:
                    alt_result = {
                        'food_name_ko': None,
                        'food_name_en': alt.get('food_name'),
                        'confidence': alt.get('confidence', 0),
                        'nutrition': alt.get('nutrition', {}),
                        'nutrition_source': 'analyzer_api'
                    }
                    predictions.append(alt_result)
                
                return {
                    'success': True,
                    'method': result['data'].get('method', 'unknown'),
                    'predictions': predictions,
                    'caption': result['data'].get('caption', '')
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'API analysis failed'),
                    'raw_result': result
                }
        else:
            return {
                'success': False,
                'error': f'API request failed: {response.status_code}',
                'response_text': response.text
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': f'Analysis request failed: {str(e)}'
        }


def save_food_record(food_record: dict) -> str:
    """음식 기록을 데이터베이스에 저장"""
    try:
        # 기존 기록 로드
        records = load_json_file('food_records.json', [])
        
        # 새 기록 추가
        record_id = len(records) + 1
        food_record['id'] = record_id
        records.append(food_record)
        
        # 파일 저장
        if save_json_file(records, 'food_records.json'):
            return str(record_id)
        else:
            return f"error_{int(datetime.now().timestamp())}"
            
    except Exception as e:
        logger.error(f"데이터베이스 저장 오류: {e}")
        return f"error_{int(datetime.now().timestamp())}"


def google_ocr_text_from_image(image_path: str) -> str:
    """영수증 이미지에서 텍스트 추출"""
    if not GOOGLE_VISION_AVAILABLE:
        return "❌ Google Cloud Vision API가 설치되지 않음"
    
    try:
        client = vision.ImageAnnotatorClient()
        
        # 이미지 파일 읽기
        with open(image_path, "rb") as image_file:
            content = image_file.read()
        
        image = vision.Image(content=content)
        
        # OCR 수행
        response = client.text_detection(image=image)
        
        if response.error.message:
            raise Exception(f"Google Vision API 오류: {response.error.message}")
        
        texts = response.text_annotations
        
        if texts:
            full_text = texts[0].description
            logger.info(f"OCR 텍스트 추출 성공: {len(full_text)}자")
            return full_text
        else:
            logger.warning("OCR에서 텍스트를 찾지 못함")
            return ""
            
    except Exception as e:
        logger.error(f"OCR 오류: {e}")
        return f"❌ OCR 오류: {str(e)}"


def get_supported_receipt_foods() -> list:
    """영수증에서 인식 가능한 음식 목록 반환"""
    return [
        # 한식
        "김밥", "야채김밥", "참치김밥", "떡볶이", "라면", "비빔밥", 
        "된장찌개", "순두부찌개", "김치찌개", "볶음밥", "제육볶음", "삼겹살",
        "냉면", "삼계탕", "갈비구이", "불고기", "돈까스", "우동", "칼국수",
        
        # 양식
        "스파게티", "파스타", "피자", "햄버거", "치즈버거", "샌드위치", "토스트", "샐러드",
        
        # 패스트푸드
        "빅맥", "후렌치후라이", "치킨너겟", "양념치킨", "후라이드치킨",
        
        # 일식/중식
        "짜장면", "짬뽕", "탕수육", "초밥", "라멘", "돈카츠", "가츠동", "규동",
        
        # 음료
        "콜라", "사이다", "커피", "아메리카노", "카페라떼", "우유", "주스"
    ]


def extract_food_names_from_receipt(text: str) -> list:
    """영수증 텍스트에서 음식명 추출"""
    if not text or text.startswith("❌"):
        return []
        
    lines = text.split('\n')
    detected_foods = set()
    
    # 음식명을 길이 순으로 정렬 (긴 것부터 매칭)
    supported_foods = get_supported_receipt_foods()
    sorted_foods = sorted(supported_foods, key=len, reverse=True)
    
    for line in lines:
        line = line.strip()
        if not line:
            continue
            
        # 각 줄에서 음식명 찾기
        for food in sorted_foods:
            if food in line:
                detected_foods.add(food)
                break  # 한 줄에서 첫 번째 매칭되는 음식만 추가
    
    # 중복 제거 (예: "치킨버거"가 있으면 "치킨" 제거)
    final_foods = set(detected_foods)
    for food in detected_foods:
        for other in detected_foods:
            if food != other and food in other and len(food) < len(other):
                final_foods.discard(food)
    
    return list(final_foods)


def get_simple_nutrition(food_name: str) -> dict:
    """간단한 영양 정보 추정 (영수증용)"""
    # 기본 영양 데이터베이스
    nutrition_db = {
        '김밥': {'calories': 150, 'carbs': 22, 'protein': 6, 'fat': 4},
        '참치김밥': {'calories': 160, 'carbs': 20, 'protein': 8, 'fat': 5},
        '떡볶이': {'calories': 120, 'carbs': 25, 'protein': 3, 'fat': 2},
        '라면': {'calories': 500, 'carbs': 70, 'protein': 15, 'fat': 18},
        '비빔밥': {'calories': 160, 'carbs': 22, 'protein': 6, 'fat': 4},
        '김치찌개': {'calories': 85, 'carbs': 4, 'protein': 6, 'fat': 5},
        '볶음밥': {'calories': 180, 'carbs': 28, 'protein': 8, 'fat': 6},
        '삼겹살': {'calories': 350, 'carbs': 0, 'protein': 15, 'fat': 32},
        '냉면': {'calories': 130, 'carbs': 25, 'protein': 6, 'fat': 1},
        '돈까스': {'calories': 280, 'carbs': 25, 'protein': 15, 'fat': 15},
        '피자': {'calories': 266, 'carbs': 33, 'protein': 11, 'fat': 10},
        '햄버거': {'calories': 295, 'carbs': 23, 'protein': 17, 'fat': 16},
        '짜장면': {'calories': 180, 'carbs': 32, 'protein': 8, 'fat': 3},
        '짬뽕': {'calories': 160, 'carbs': 20, 'protein': 12, 'fat': 5},
        '콜라': {'calories': 140, 'carbs': 35, 'protein': 0, 'fat': 0},
        '커피': {'calories': 5, 'carbs': 1, 'protein': 0, 'fat': 0},
        '카페라떼': {'calories': 120, 'carbs': 10, 'protein': 6, 'fat': 6}
    }
    
    nutrition = nutrition_db.get(food_name, {
        'calories': 200,  # 기본값
        'carbs': 25,
        'protein': 10,
        'fat': 8
    })
    
    return {
        'food': food_name,
        'calories': nutrition['calories'],
        'carbs': nutrition['carbs'],
        'protein': nutrition['protein'],
        'fat': nutrition['fat']
    }

from flask import Flask, render_template
from exercise_data import exercise_data

app = Flask(__name__)

# ---------------------------
# 사용자 정보 (입력 대신 임시 저장)
user = {
    "age": 25,
    "height": 178,
    "weight": 70,
    "total_calories": 3000
}

# ---------------------------
# 권장 칼로리 계산 (남자 기준)
def calculate_recommended_calories(height, weight, age, activity_factor=1.55):
    bmr = 66.47 + (13.75 * weight) + (5.003 * height) - (6.755 * age)
    return round(bmr * activity_factor)

# ---------------------------
# 운동 시간 계산
def get_exercise_suggestions(excess_calories):
    result = {}
    for name, per_hour in exercise_data.items():
        minutes = round((excess_calories / per_hour) * 60)
        result[name] = f"{minutes}분"
    return result

# ---------------------------
# 주간 영양소 분석 + 식단 제안
def analyze_weekly_nutrition(weekly_data):
    totals = {"calories": 0, "carbs": 0, "protein": 0, "fat": 0}
    for day in weekly_data:
        for key in totals:
            totals[key] += day[key]

    total_calories = totals["calories"]
    carb_percent = round((totals["carbs"] * 4 / total_calories) * 100, 1)
    protein_percent = round((totals["protein"] * 4 / total_calories) * 100, 1)
    fat_percent = round((totals["fat"] * 9 / total_calories) * 100, 1)

    result = {
        "calories": total_calories,
        "carb_percent": carb_percent,
        "protein_percent": protein_percent,
        "fat_percent": fat_percent
    }

    suggestions = []
    if carb_percent < 55:
        suggestions.append(f"탄수화물 비율이 {carb_percent}%로 부족합니다. 쌀, 고구마, 현미 등을 섭취해보세요.")
    elif carb_percent > 65:
        suggestions.append(f"탄수화물 비율이 {carb_percent}%로 과다합니다. 밥/빵 섭취를 줄여보세요.")

    if protein_percent < 7:
        suggestions.append(f"단백질 비율이 {protein_percent}%로 부족합니다. 닭가슴살, 계란, 두부 등을 추가해보세요.")
    elif protein_percent > 20:
        suggestions.append(f"단백질 비율이 {protein_percent}%로 과다합니다. 단백질 보충제를 줄여보세요.")

    if fat_percent < 15:
        suggestions.append(f"지방 비율이 {fat_percent}%로 부족합니다. 견과류나 아보카도를 추가해보세요.")
    elif fat_percent > 30:
        suggestions.append(f"지방 비율이 {fat_percent}%로 과다합니다. 튀김류, 기름진 음식을 줄여보세요.")

    return {"result": result, "alerts": suggestions}

# ---------------------------
@app.route('/')
def index():
    recommended = calculate_recommended_calories(user["height"], user["weight"], user["age"])
    excess = max(0, user["total_calories"] - recommended)
    exercise_suggestions = get_exercise_suggestions(excess)

    # 주간 데이터 예시
    weekly_data = [
        {"day": "월", "calories": 2300, "carbs": 290, "protein": 70, "fat": 60},
        {"day": "화", "calories": 2000, "carbs": 260, "protein": 65, "fat": 50},
        {"day": "수", "calories": 2100, "carbs": 250, "protein": 60, "fat": 55},
        {"day": "목", "calories": 2400, "carbs": 270, "protein": 65, "fat": 60},
        {"day": "금", "calories": 2200, "carbs": 260, "protein": 60, "fat": 58},
        {"day": "토", "calories": 2500, "carbs": 300, "protein": 80, "fat": 65},
        {"day": "일", "calories": 2600, "carbs": 310, "protein": 85, "fat": 70},
    ]
    nutrition_analysis = analyze_weekly_nutrition(weekly_data)

    return render_template("index.html",
                           user=user,
                           recommended=recommended,
                           excess=excess,
                           suggestions=exercise_suggestions,
                           nutrition=nutrition_analysis["result"],
                           nutrition_alerts=nutrition_analysis["alerts"])

if __name__ == '__main__':
    app.run(debug=True)

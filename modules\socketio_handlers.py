# -*- coding: utf-8 -*-
"""
SocketIO Event Handlers
실시간 위치 추적, 알림 시스템 관련 소켓 이벤트 핸들러
"""

import time
from datetime import datetime
from flask import request
from flask_socketio import emit
import logging

from modules.config import config
from modules.utils import LocationTracker, check_location_is_restaurant

logger = logging.getLogger(__name__)

# 위치 추적기 인스턴스
location_tracker = LocationTracker(
    api_key=config.KAKAO_REST_API_KEY,
    detection_radius=config.DETECTION_RADIUS,
    required_stay_time=config.REQUIRED_STAY_TIME
)


def register_socketio_events(socketio):
    """SocketIO 이벤트 핸들러 등록"""
    
    @socketio.on('connect')
    def handle_connect():
        """클라이언트 연결"""
        logger.info(f"클라이언트 연결: {request.sid}")
        
        # 연결 시 현재 설정 정보 전송
        emit('config_info', {
            'detection_radius': config.DETECTION_RADIUS,
            'required_stay_time': config.REQUIRED_STAY_TIME,
            'kakao_configured': config.is_kakao_configured()
        })
    
    @socketio.on('disconnect')
    def handle_disconnect():
        """클라이언트 연결 해제"""
        logger.info(f"클라이언트 연결 해제: {request.sid}")
    
    @socketio.on('update_location')
    def handle_location(data):
        """사용자 위치 업데이트 처리"""
        try:
            lat = float(data['lat'])
            lon = float(data['lon'])
            
            logger.debug(f"위치 업데이트 수신: {lat:.6f}, {lon:.6f}")
            
            # 위치 추적기로 상태 업데이트
            location_status = location_tracker.update_location(lat, lon)
            
            # 상태별 처리
            if location_status['status'] == 'entered':
                # 새로운 장소 입장
                place_info = location_status['place_info']
                emit('status_update', {
                    'restaurant': place_info['place_name'],
                    'category': place_info['category'],
                    'status': 'entered',
                    'distance': round(place_info['distance'], 1),
                    'confidence': round(place_info['confidence'], 2),
                    'timestamp': datetime.now().strftime('%H:%M:%S'),
                    'address': place_info.get('address', ''),
                    'detection_method': 'real_time_api'
                })
                
            elif location_status['status'] == 'staying' and location_status['notification_ready']:
                # 알림 전송 (한 번만)
                place_info = location_status['place_info']
                
                logger.info(f"알림 전송: {place_info['place_name']}")
                
                emit('notification', {
                    'type': 'restaurant_notification',
                    'restaurant': place_info['place_name'],
                    'category': place_info['category'],
                    'title': f"{place_info['place_name']} 알림",
                    'message': f"{place_info['place_name']}({place_info['category']})에 {config.REQUIRED_STAY_TIME}초 이상 머물고 있습니다.\n\n음식을 등록하시겠습니까?",
                    'upload_url': f"/upload_food?restaurant={place_info['place_name']}",
                    'distance': round(place_info['distance'], 1),
                    'stay_duration': round(location_status['stay_duration'], 1),
                    'confidence': round(place_info['confidence'], 2),
                    'address': place_info.get('address', ''),
                    'phone': place_info.get('phone', ''),
                    'detection_method': 'real_time_api',
                    'timestamp': datetime.now().isoformat()
                })
                
            elif location_status['status'] == 'outside':
                # 퇴장 처리
                for left_place in location_status.get('left_places', []):
                    emit('status_update', {
                        'restaurant': left_place['name'],
                        'status': 'left',
                        'stay_duration': round(left_place['stay_duration'], 1),
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    })
            
            # 현재 위치 상태 업데이트 전송
            current_place = None
            if location_status['status'] in ['entered', 'staying']:
                place_info = location_status['place_info']
                current_place = {
                    'name': place_info['place_name'],
                    'category': place_info['category'],
                    'distance': round(place_info['distance'], 1),
                    'confidence': round(place_info['confidence'], 2),
                    'address': place_info.get('address', ''),
                    'stay_duration': round(location_status.get('stay_duration', 0), 1),
                    'notification_sent': location_status.get('notification_ready', False)
                }
            
            emit('location_status', {
                'is_in_restaurant': location_status['status'] in ['entered', 'staying'],
                'current_place': current_place,
                'all_nearby_places': location_status['place_info'].get('all_places', [])[:3] if location_status['place_info'].get('all_places') else [],
                'detection_radius': config.DETECTION_RADIUS,
                'required_stay_time': config.REQUIRED_STAY_TIME,
                'current_position': {'lat': lat, 'lon': lon}
            })
                    
        except (ValueError, KeyError) as e:
            logger.error(f"위치 데이터 처리 오류: {e}")
            emit('error', {'message': '위치 데이터가 올바르지 않습니다'})
        except Exception as e:
            logger.error(f"위치 처리 중 예상치 못한 오류: {e}")
            emit('error', {'message': f'위치 처리 오류: {str(e)}'})
    
    @socketio.on('force_notification_test')
    def handle_force_notification_test(data):
        """강제 알림 테스트 (디버깅용)"""
        try:
            restaurant_name = data.get('restaurant', '테스트 음식점')
            logger.info(f"강제 알림 테스트: {restaurant_name}")
            
            emit('notification', {
                'type': 'restaurant_notification',
                'restaurant': restaurant_name,
                'category': '테스트 카테고리',
                'title': f'{restaurant_name} 테스트 알림',
                'message': f'{restaurant_name}에서 테스트 알림입니다!\n\n음식을 등록하시겠습니까?',
                'upload_url': f'/upload_food?restaurant={restaurant_name}',
                'distance': 5.0,
                'stay_duration': 10.0,
                'confidence': 1.0,
                'address': '테스트 주소',
                'phone': '010-0000-0000',
                'detection_method': 'force_test',
                'timestamp': datetime.now().isoformat(),
                'test': True
            })
            
            logger.info(f"강제 알림 전송 완료: {restaurant_name}")
            
        except Exception as e:
            logger.error(f"강제 알림 테스트 오류: {e}")
    
    @socketio.on('test_notification')
    def handle_test_notification(data):
        """기존 테스트 알림"""
        try:
            restaurant_name = data.get('restaurant', '테스트 음식점')
            logger.info(f"테스트 알림 요청: {restaurant_name}")
            
            emit('notification', {
                'type': 'restaurant_notification',
                'restaurant': restaurant_name,
                'category': '테스트',
                'title': f'{restaurant_name} 테스트',
                'message': f'{restaurant_name}에서 테스트 알림입니다.',
                'upload_url': f'/upload_food?restaurant={restaurant_name}',
                'distance': 5.0,
                'stay_duration': 10.0,
                'test': True,
                'timestamp': datetime.now().isoformat()
            })
            
            logger.info(f"테스트 알림 전송 완료: {restaurant_name}")
            
        except Exception as e:
            logger.error(f"테스트 알림 처리 오류: {e}")
    
    @socketio.on('reset_location_tracker')
    def handle_reset_tracker():
        """위치 추적기 초기화"""
        try:
            location_tracker.reset()
            emit('status_update', {
                'message': '위치 추적기가 초기화되었습니다',
                'timestamp': datetime.now().strftime('%H:%M:%S')
            })
            logger.info("위치 추적기 초기화 완료")
        except Exception as e:
            logger.error(f"위치 추적기 초기화 오류: {e}")
            emit('error', {'message': '초기화 중 오류가 발생했습니다'})
    
    logger.info("SocketIO 이벤트 핸들러 등록 완료")
    
    return socketio

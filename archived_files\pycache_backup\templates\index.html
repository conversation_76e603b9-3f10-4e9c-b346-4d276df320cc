<!DOCTYPE html>
<html lang="ko">
<head>
  <meta charset="UTF-8">
  <title>운동 제안 및 주간 영양소 분석</title>
  <style>
    body {
      font-family: 'Segoe UI', sans-serif;
      background-color: #f9f9f9;
      color: #333;
      max-width: 800px;
      margin: 50px auto;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.08);
    }

    h1, h2, h3 {
      color: #2c3e50;
    }

    ul {
      list-style-type: none;
      padding-left: 0;
    }

    li {
      padding: 4px 0;
    }

    select {
      width: 100%;
      padding: 10px;
      font-size: 16px;
      border-radius: 6px;
      border: 1px solid #ccc;
      margin-top: 10px;
      margin-bottom: 20px;
      background-color: #fff;
    }

    #exercise-result {
      background-color: #eafbea;
      border-left: 5px solid #3cba54;
      padding: 15px;
      border-radius: 6px;
      font-weight: bold;
      margin-top: 10px;
    }

    .alert-list {
      background-color: #fff3cd;
      border-left: 5px solid #ffcc00;
      padding: 15px;
      border-radius: 6px;
      margin-top: 10px;
    }

    .alert-list li {
      font-weight: bold;
      color: #7f6000;
    }
  </style>
</head>
<body>
  <h1>운동 제안 및 주간 영양소 분석</h1>

  <h2>칼로리 요약</h2>
  <ul>
    <li>섭취 칼로리: {{ user.total_calories }} kcal</li>
    <li>권장 칼로리: {{ recommended }} kcal</li>
    <li><strong>초과 칼로리: {{ excess }} kcal</strong></li>
  </ul>

  <h2>운동 선택</h2>
  <select id="exercise-select">
    <option value="">운동을 선택하세요</option>
    {% for exercise in suggestions %}
      <option value="{{ exercise }}">{{ exercise }}</option>
    {% endfor %}
  </select>

  <div id="exercise-result"></div>

  <h2>주간 영양소 분석</h2>
  <ul>
    <li>총 섭취 칼로리: {{ nutrition.calories }} kcal</li>
    <li>탄수화물 비율: {{ nutrition.carb_percent }}%</li>
    <li>단백질 비율: {{ nutrition.protein_percent }}%</li>
    <li>지방 비율: {{ nutrition.fat_percent }}%</li>
  </ul>

  <h3>식단 개선 제안</h3>
  {% if nutrition_alerts %}
  <ul class="alert-list">
    {% for alert in nutrition_alerts %}
      <li>⚠️ {{ alert }}</li>
    {% endfor %}
  </ul>
  {% else %}
    <p>✅ 모든 영양소가 권장 범위 내에 있습니다!</p>
  {% endif %}

  <script>
    const suggestions = {{ suggestions | tojson | safe }};
    const select = document.getElementById('exercise-select');
    const resultDiv = document.getElementById('exercise-result');

    select.addEventListener('change', () => {
      const exercise = select.value;
      if (exercise && suggestions[exercise]) {
        resultDiv.innerHTML = `<p><strong>${exercise}</strong>를 <strong>${suggestions[exercise]}</strong> 하면 초과 칼로리를 소모할 수 있어요.</p>`;
      } else {
        resultDiv.innerHTML = '';
      }
    });
  </script>
</body>
</html>

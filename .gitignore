# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment Variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Google Cloud Vision API Keys
receipt-ocr-key.json
*-key.json
service-account-*.json

# API Keys
*-api-key.json
*.pem
*.key

# Logs
*.log
logs/
app.log

# Uploads
uploads/
static/uploads/

# Database
*.db
*.sqlite
*.sqlite3

# Cache
.cache/
*.cache

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Food records (contains personal data)
food_records.json
user_profile.json

# Backup files
*.bak
*.backup

# Development files
test_*.py
debug_*.py
sample_*.py

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

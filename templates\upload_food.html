<!DOCTYPE html>
<html>
<head>
    <title>음식 등록 - AI 칼로리 관리</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 700px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        .api-status {
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 6px;
            padding: 10px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
        }
        .api-status.offline {
            background: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .restaurant-info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin-bottom: 25px;
            border-radius: 4px;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #4CAF50;
            background: #f9f9f9;
        }
        .upload-area.dragover {
            border-color: #4CAF50;
            background: #f0f8f0;
        }
        .upload-icon {
            font-size: 48px;
            color: #bbb;
            margin-bottom: 15px;
        }
        .upload-text {
            color: #666;
            margin-bottom: 15px;
        }
        #file-input {
            display: none;
        }
        .file-info {
            margin: 15px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            display: none;
        }
        .preview-container {
            margin: 20px 0;
            text-align: center;
            display: none;
        }
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }
        .btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        .btn-primary:hover:not(:disabled) {
            background: #45a049;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4CAF50;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        
        /* === Enhanced Food Selection UI === */
        .food-selection {
            margin-top: 25px;
            display: none;
        }
        .food-selection h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        .food-options {
            display: grid;
            gap: 15px;
            margin-bottom: 20px;
        }
        .food-option {
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        .food-option:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        .food-option.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        .food-name {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 8px;
            color: #2c3e50;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nutrition-source {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            background: #e3f2fd;
            color: #1976d2;
            margin-left: 10px;
        }
        .nutrition-source.mfds_api {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .nutrition-source.default_db {
            background: #fff3e0;
            color: #ef6c00;
        }
        .food-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        .food-detail {
            text-align: center;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 14px;
        }
        .food-detail-value {
            font-weight: bold;
            color: #e91e63;
        }
        .confidence-badge {
            display: inline-block;
            background: #2196f3;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }
        .weight-section {
            margin-top: 15px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 6px;
        }
        .weight-input {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-top: 8px;
        }
        .weight-input input {
            width: 80px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        .custom-input-section {
            border-top: 2px dashed #ddd;
            padding-top: 20px;
            margin-top: 20px;
        }
        .search-container {
            position: relative;
            margin-bottom: 15px;
        }
        .custom-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .custom-input:focus {
            outline: none;
            border-color: #4CAF50;
        }
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
        }
        .search-item {
            padding: 10px;
            cursor: pointer;
            border-bottom: 1px solid #eee;
        }
        .search-item:hover {
            background: #f5f5f5;
        }
        .search-item:last-child {
            border-bottom: none;
        }
        .selection-buttons {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }
        .nutrition-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .nutrition-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
        }
        .nutrition-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        .nav-link {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
            margin: 0 15px;
            padding: 10px 20px;
            border: 2px solid #4CAF50;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .nav-link:hover {
            background: #4CAF50;
            color: white;
        }
        .debug-info {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍽️ 음식 등록</h1>
            <p>음식 사진을 업로드하면 AI가 분석하고 식품의약품안전처 영양데이터를 제공합니다</p>
        </div>

        <!-- API Status -->
        <div class="api-status" id="api-status">
            <span id="api-status-text">🔄 식품영양 API 상태를 확인 중...</span>
        </div>

        {% if restaurant_name %}
        <div class="restaurant-info">
            <strong>📍 식당:</strong> {{ restaurant_name }}
        </div>
        {% endif %}

        <!-- Step 1: Image Upload -->
        <div id="upload-step">
            <form id="upload-form" enctype="multipart/form-data">
                <input type="hidden" name="restaurant_name" value="{{ restaurant_name or '직접 입력' }}">
                
                <div class="upload-area" onclick="document.getElementById('file-input').click()">
                    <div class="upload-icon">📸</div>
                    <div class="upload-text">
                        <strong>클릭하거나 파일을 드래그해서 업로드하세요</strong><br>
                        <small>지원 형식: JPG, PNG, GIF, WebP (최대 10MB)</small>
                    </div>
                    <input type="file" id="file-input" name="food_image" accept="image/*" required>
                </div>

                <div class="file-info" id="file-info"></div>
                
                <div class="preview-container" id="preview-container">
                    <img id="preview-image" class="preview-image" alt="미리보기">
                </div>

                <div class="buttons">
                    <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                        ← 뒤로가기
                    </button>
                    <button type="submit" class="btn btn-primary" id="analyze-btn" disabled>
                        🔍 AI 분석하기
                    </button>
                </div>
            </form>
        </div>

        <!-- Loading Animation -->
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>AI가 음식을 분석하고 영양정보를 조회하고 있습니다...<br><small>최대 30초 소요</small></p>
        </div>

        <!-- Step 2: Food Selection -->
        <div class="food-selection" id="food-selection">
            <h3>🤖 AI 분석 결과 - 가장 적합한 음식을 선택하세요</h3>
            
            <div class="food-options" id="food-options">
                <!-- AI suggested foods will be populated here -->
            </div>
            
            <div class="custom-input-section">
                <h4>💭 위 결과가 맞지 않나요?</h4>
                <div class="search-container">
                    <input type="text" class="custom-input" id="custom-food-name" 
                           placeholder="음식 이름을 입력하면 자동 완성됩니다 (예: 치킨텐더, 불고기덮밥)"
                           autocomplete="off">
                    <div class="search-results" id="search-results"></div>
                </div>
                <div class="weight-input">
                    <label>무게:</label>
                    <input type="number" id="custom-weight" placeholder="150" min="1" max="2000" step="1">
                    <span>g</span>
                    <small style="color: #666; margin-left: 10px;">선택사항 (기본: 150g)</small>
                </div>
            </div>
            
            <div class="selection-buttons">
                <button type="button" class="btn btn-secondary" onclick="goBackToUpload()">
                    📸 다른 사진으로 다시 촬영
                </button>
                <button type="button" class="btn btn-primary" id="confirm-btn" onclick="confirmSelection()">
                    ✅ 선택 완료
                </button>
            </div>
        </div>

        <!-- Step 3: Final Result -->
        <div class="result" id="result"></div>

        <div class="navigation">
            <a href="/" class="nav-link">🗺️ 지도로 돌아가기</a>
            <a href="/receipt" class="nav-link">📄 영수증 분석</a>
            <a href="/dashboard" class="nav-link">📊 대시보드 보기</a>
            <a href="/profile" class="nav-link">👤 프로필 설정</a>
        </div>
    </div>

    <script>
        const fileInput = document.getElementById('file-input');
        const uploadArea = document.querySelector('.upload-area');
        const fileInfo = document.getElementById('file-info');
        const previewContainer = document.getElementById('preview-container');
        const previewImage = document.getElementById('preview-image');
        const analyzeBtn = document.getElementById('analyze-btn');
        const form = document.getElementById('upload-form');
        const loading = document.getElementById('loading');
        const uploadStep = document.getElementById('upload-step');
        const foodSelection = document.getElementById('food-selection');
        const result = document.getElementById('result');
        const customFoodInput = document.getElementById('custom-food-name');
        const searchResults = document.getElementById('search-results');

        // Global variables for analysis result
        let currentAnalysisResult = null;
        let currentImagePath = null;
        let currentRestaurantName = null;
        let selectedFood = null;
        let searchTimeout = null;

        // Check API status on load
        checkApiStatus();

        // Drag and drop events
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect();
            }
        });

        // File selection event
        fileInput.addEventListener('change', handleFileSelect);

        // Food search functionality
        customFoodInput.addEventListener('input', handleFoodSearch);
        customFoodInput.addEventListener('focus', handleFoodSearch);
        customFoodInput.addEventListener('blur', () => {
            // Hide search results after a short delay
            setTimeout(() => {
                searchResults.style.display = 'none';
            }, 200);
        });

        async function checkApiStatus() {
            try {
                const response = await fetch('/api/nutrition_api_status');
                const data = await response.json();
                
                const statusElement = document.getElementById('api-status');
                const statusText = document.getElementById('api-status-text');
                
                if (data.success && data.data.api_available) {
                    statusElement.className = 'api-status';
                    statusText.textContent = '✅ 식품의약품안전처 영양데이터 API 연결됨';
                } else {
                    statusElement.className = 'api-status offline';
                    statusText.textContent = '⚠️ API 연결 불가 - 기본 영양정보 사용';
                }
            } catch (error) {
                const statusElement = document.getElementById('api-status');
                const statusText = document.getElementById('api-status-text');
                statusElement.className = 'api-status offline';
                statusText.textContent = '⚠️ API 상태 확인 실패 - 기본 영양정보 사용';
            }
        }

        async function handleFoodSearch() {
            const query = customFoodInput.value.trim();
            
            if (query.length < 2) {
                searchResults.style.display = 'none';
                return;
            }

            // Clear previous timeout
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }

            // Debounce search
            searchTimeout = setTimeout(async () => {
                try {
                    const response = await fetch(`/api/search_food?q=${encodeURIComponent(query)}&limit=5`);
                    const data = await response.json();
                    
                    if (data.success && data.data.length > 0) {
                        showSearchResults(data.data);
                    } else {
                        searchResults.style.display = 'none';
                    }
                } catch (error) {
                    console.error('Search error:', error);
                    searchResults.style.display = 'none';
                }
            }, 300);
        }

        function showSearchResults(results) {
            searchResults.innerHTML = '';
            
            results.forEach(food => {
                const item = document.createElement('div');
                item.className = 'search-item';
                item.innerHTML = `
                    <strong>${food.food_name}</strong>
                    <small style="color: #666; margin-left: 10px;">${food.calories_per_100g.toFixed(0)} kcal/100g</small>
                `;
                
                item.addEventListener('click', () => {
                    customFoodInput.value = food.food_name;
                    searchResults.style.display = 'none';
                });
                
                searchResults.appendChild(item);
            });
            
            searchResults.style.display = 'block';
        }

        function handleFileSelect() {
            const file = fileInput.files[0];
            if (!file) return;

            // File size validation
            if (file.size > 10 * 1024 * 1024) {
                alert('파일 크기가 너무 큽니다. 10MB 이하의 파일을 선택해주세요.');
                return;
            }

            // Show file info
            fileInfo.innerHTML = `
                <strong>선택된 파일:</strong> ${file.name}<br>
                <small>크기: ${(file.size / 1024 / 1024).toFixed(2)}MB</small>
            `;
            fileInfo.style.display = 'block';

            // Image preview
            const reader = new FileReader();
            reader.onload = (e) => {
                previewImage.src = e.target.result;
                previewContainer.style.display = 'block';
            };
            reader.readAsDataURL(file);

            // Enable analyze button
            analyzeBtn.disabled = false;
        }

        // Form submission
        form.addEventListener('submit', async (e) => {
            e.preventDefault();

            const formData = new FormData(form);
            
            // UI state change
            analyzeBtn.disabled = true;
            loading.style.display = 'block';
            uploadStep.style.display = 'none';

            try {
                console.log('Sending request to /upload_food...');
                
                const response = await fetch('/upload_food', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status);

                const data = await response.json();
                console.log('Response data:', data);

                if (data.success && data.requires_selection) {
                    // Show food selection UI
                    currentAnalysisResult = data.analysis_result;
                    currentImagePath = data.image_path;
                    currentRestaurantName = data.restaurant_name;
                    
                    showFoodSelection(data.analysis_result);
                } else {
                    // Show error
                    showError(data.error || 'Unknown error', data.debug_info);
                }

            } catch (error) {
                console.error('Network error:', error);
                showError('네트워크 오류가 발생했습니다. 다시 시도해주세요.');
            } finally {
                loading.style.display = 'none';
            }
        });

        function showFoodSelection(analysisResult) {
            console.log('Showing food selection for:', analysisResult);
            
            const predictions = analysisResult.predictions || [];
            const foodOptionsContainer = document.getElementById('food-options');
            foodOptionsContainer.innerHTML = '';

            // Show up to 3 predictions
            predictions.slice(0, 3).forEach((food, index) => {
                const foodOption = createFoodOptionCard(food, index);
                foodOptionsContainer.appendChild(foodOption);
            });

            // Auto-select first option
            if (predictions.length > 0) {
                selectFoodOption(0);
            }

            // Show selection UI
            uploadStep.style.display = 'none';
            foodSelection.style.display = 'block';
        }

        function createFoodOptionCard(food, index) {
            const div = document.createElement('div');
            div.className = 'food-option';
            div.dataset.index = index;
            div.onclick = () => selectFoodOption(index);

            const displayName = food.food_name_ko || food.food_name_en || 'Unknown Food';
            const confidence = ((food.confidence || 0) * 100).toFixed(1);
            const nutrition = food.nutrition || {};
            const nutritionSource = food.nutrition_source || 'unknown';

            // Nutrition source display
            let sourceText = '';
            let sourceClass = '';
            switch (nutritionSource) {
                case 'mfds_api':
                    sourceText = '식약처 API';
                    sourceClass = 'mfds_api';
                    break;
                case 'default_db':
                    sourceText = '기본 DB';
                    sourceClass = 'default_db';
                    break;
                default:
                    sourceText = '추정값';
                    sourceClass = 'default_db';
            }

            div.innerHTML = `
                <div class="food-name">
                    <span>
                        ${displayName}
                        <span class="confidence-badge">${confidence}% 신뢰도</span>
                    </span>
                    <span class="nutrition-source ${sourceClass}">${sourceText}</span>
                </div>
                <div class="food-details">
                    <div class="food-detail">
                        <div class="food-detail-value">${(nutrition.calories || 0).toFixed(0)}</div>
                        <div>칼로리</div>
                    </div>
                    <div class="food-detail">
                        <div class="food-detail-value">${(nutrition.protein || 0).toFixed(1)}g</div>
                        <div>단백질</div>
                    </div>
                    <div class="food-detail">
                        <div class="food-detail-value">${(nutrition.carbs || 0).toFixed(1)}g</div>
                        <div>탄수화물</div>
                    </div>
                    <div class="food-detail">
                        <div class="food-detail-value">${(nutrition.fat || 0).toFixed(1)}g</div>
                        <div>지방</div>
                    </div>
                </div>
                <div class="weight-section">
                    <strong>무게:</strong> ${(nutrition.weight_grams || 150).toFixed(0)}g
                    <div class="weight-input">
                        <label>조정:</label>
                        <input type="number" 
                               onchange="updateNutrition(${index}, this.value)" 
                               value="${(nutrition.weight_grams || 150).toFixed(0)}" 
                               min="1" max="2000" step="1">
                        <span>g</span>
                    </div>
                </div>
            `;

            return div;
        }

        function updateNutrition(optionIndex, newWeight) {
            const predictions = currentAnalysisResult.predictions;
            if (!predictions[optionIndex]) return;

            const food = predictions[optionIndex];
            const oldWeight = food.nutrition.weight_grams || 150;
            const ratio = parseFloat(newWeight) / oldWeight;

            // Update nutrition values
            food.nutrition.calories = (food.nutrition.calories || 0) * ratio;
            food.nutrition.protein = (food.nutrition.protein || 0) * ratio;
            food.nutrition.carbs = (food.nutrition.carbs || 0) * ratio;
            food.nutrition.fat = (food.nutrition.fat || 0) * ratio;
            food.nutrition.weight_grams = parseFloat(newWeight);

            // Update display
            const option = document.querySelector(`[data-index="${optionIndex}"]`);
            const details = option.querySelectorAll('.food-detail-value');
            if (details.length >= 4) {
                details[0].textContent = food.nutrition.calories.toFixed(0);
                details[1].textContent = food.nutrition.protein.toFixed(1) + 'g';
                details[2].textContent = food.nutrition.carbs.toFixed(1) + 'g';
                details[3].textContent = food.nutrition.fat.toFixed(1) + 'g';
            }
        }

        function selectFoodOption(index) {
            // Remove previous selection
            document.querySelectorAll('.food-option').forEach(option => {
                option.classList.remove('selected');
            });

            // Select new option
            const selectedOption = document.querySelector(`[data-index="${index}"]`);
            if (selectedOption) {
                selectedOption.classList.add('selected');
                
                // Store selected food data
                selectedFood = currentAnalysisResult.predictions[index];
                
                // Clear custom input
                customFoodInput.value = '';
                document.getElementById('custom-weight').value = '';
            }
        }

        async function confirmSelection() {
            const customFoodName = customFoodInput.value.trim();
            const customWeight = document.getElementById('custom-weight').value;
            
            if (!selectedFood && !customFoodName) {
                alert('음식을 선택하거나 직접 입력해주세요.');
                return;
            }

            const confirmBtn = document.getElementById('confirm-btn');
            confirmBtn.disabled = true;
            confirmBtn.textContent = '저장 중...';

            try {
                const requestData = {
                    restaurant_name: currentRestaurantName,
                    analysis_result: currentAnalysisResult,
                    image_path: currentImagePath,
                    selected_food: selectedFood,
                    custom_food_name: customFoodName,
                    custom_weight: customWeight
                };

                console.log('Confirming selection:', requestData);

                const response = await fetch('/confirm_food', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const data = await response.json();
                console.log('Confirmation response:', data);

                if (data.success) {
                    showSuccess(data);
                    
                    // Redirect to dashboard after 3 seconds
                    setTimeout(() => {
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                    }, 3000);
                } else {
                    showError(data.error || 'Failed to save food record');
                }

            } catch (error) {
                console.error('Confirmation error:', error);
                showError('네트워크 오류가 발생했습니다. 다시 시도해주세요.');
            } finally {
                confirmBtn.disabled = false;
                confirmBtn.textContent = '✅ 선택 완료';
            }
        }

        function showSuccess(data) {
            foodSelection.style.display = 'none';
            
            result.className = 'result success';
            result.innerHTML = `
                <h3>✅ 음식 등록 완료!</h3>
                <p><strong>음식:</strong> ${data.food_name}</p>
                <p><strong>칼로리:</strong> ${data.calories.toFixed(0)} kcal</p>
                <p><strong>영양정보 출처:</strong> ${data.nutrition_source || 'API'}</p>
                <p><strong>기록 ID:</strong> #${data.record_id}</p>
                <p style="margin-top: 15px;"><small>💾 데이터가 저장되었습니다. 3초 후 대시보드로 이동합니다...</small></p>
            `;
            result.style.display = 'block';
        }

        function showError(error, debugInfo = null) {
            uploadStep.style.display = 'none';
            foodSelection.style.display = 'none';
            
            result.className = 'result error';
            result.innerHTML = `
                <h3>❌ 분석 실패</h3>
                <p>${error}</p>
                <p><small>다른 각도에서 찍은 사진으로 다시 시도해보세요.</small></p>
                ${debugInfo ? `<div class="debug-info"><strong>디버그 정보:</strong><br>${JSON.stringify(debugInfo, null, 2)}</div>` : ''}
            `;
            result.style.display = 'block';
        }

        function goBackToUpload() {
            // Reset UI to upload step
            foodSelection.style.display = 'none';
            result.style.display = 'none';
            uploadStep.style.display = 'block';
            
            // Reset form
            form.reset();
            fileInfo.style.display = 'none';
            previewContainer.style.display = 'none';
            analyzeBtn.disabled = true;
            
            // Clear data
            currentAnalysisResult = null;
            currentImagePath = null;
            selectedFood = null;
        }
    </script>
</body>
</html>
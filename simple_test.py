from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Simple Test</title>
    </head>
    <body>
        <h1>Simple Test Page</h1>
        <p>If you can see this, <PERSON>lask is working.</p>
        <script>
            console.log("JavaScript is working!");
            document.addEventListener('DOMContentLoaded', function() {
                console.log("DOM loaded!");
                alert("Page loaded successfully!");
            });
        </script>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("Starting simple Flask server...")
    app.run(debug=True, host='0.0.0.0', port=5001)

"""
임시 해결책: 키워드 검색 API 사용
카테고리 검색이 비활성화된 동안 사용할 수 있는 대안 함수
"""

def check_current_location_is_restaurant_keyword(lat, lon, radius=50):
    """
    키워드 검색 API를 사용한 음식점/카페 감지 (임시 해결책)
    카테고리 검색이 비활성화된 경우 대안으로 사용
    """
    try:
        if not KAKAO_REST_API_KEY or KAKAO_REST_API_KEY == 'YOUR_KAKAO_REST_API_KEY':
            logger.warning("⚠️ Kakao REST API 키가 설정되지 않았습니다")
            return {'is_restaurant': False, 'error': 'API key not configured'}
        
        # 키워드 검색 API 사용
        keywords = ['음식점', '카페', '레스토랑', '식당', '커피']
        
        found_places = []
        
        for keyword in keywords:
            try:
                url = "https://dapi.kakao.com/v2/local/search/keyword.json"
                params = {
                    'query': keyword,
                    'x': lon,  # 경도
                    'y': lat,  # 위도
                    'radius': radius * 20,  # 키워드 검색은 더 넓은 범위 필요
                    'sort': 'distance'
                }
                
                headers = {
                    'Authorization': f'KakaoAK {KAKAO_REST_API_KEY}'
                }
                
                logger.debug(f"🔍 키워드 검색: '{keyword}', 반경 {radius*20}m")
                
                response = requests.get(url, params=params, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    data = response.json()
                    places = data.get('documents', [])
                    
                    for place in places:
                        # 거리 계산 (키워드 검색은 distance 필드가 없을 수 있음)
                        place_lat = float(place.get('y', 0))
                        place_lon = float(place.get('x', 0))
                        
                        # 간단한 거리 계산 (미터)
                        from math import radians, sin, cos, sqrt, atan2
                        
                        R = 6371000  # 지구 반지름 (미터)
                        lat1, lon1 = radians(lat), radians(lon)
                        lat2, lon2 = radians(place_lat), radians(place_lon)
                        
                        dlat = lat2 - lat1
                        dlon = lon2 - lon1
                        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
                        c = 2 * atan2(sqrt(a), sqrt(1-a))
                        distance = R * c
                        
                        if distance <= radius:  # 설정된 반경 내에 있는 장소만
                            category_name = place.get('category_name', '')
                            
                            # 음식점/카페 관련 카테고리인지 확인
                            food_keywords = ['음식점', '카페', '커피', '식당', '레스토랑', '치킨', '피자', '한식', '중식', '일식', '양식']
                            is_food_related = any(food_word in category_name for food_word in food_keywords)
                            
                            if is_food_related:
                                found_places.append({
                                    'place_name': place.get('place_name', ''),
                                    'category_name': category_name,
                                    'distance': distance,
                                    'keyword': keyword,
                                    'address': place.get('address_name', ''),
                                    'road_address': place.get('road_address_name', ''),
                                    'phone': place.get('phone', ''),
                                    'place_url': place.get('place_url', '')
                                })
                
                else:
                    logger.warning(f"키워드 '{keyword}' 검색 실패: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"키워드 '{keyword}' 검색 오류: {e}")
                continue
        
        if found_places:
            # 가장 가까운 장소 선택
            closest_place = min(found_places, key=lambda x: x['distance'])
            
            # 신뢰도 계산
            confidence = max(0.1, min(1.0, 1.0 - (closest_place['distance'] / radius)))
            
            logger.info(f"✅ 음식점/카페 감지 (키워드): {closest_place['place_name']} ({closest_place['distance']:.1f}m)")
            
            return {
                'is_restaurant': True,
                'place_name': closest_place['place_name'],
                'category': closest_place['category_name'],
                'distance': closest_place['distance'],
                'confidence': confidence,
                'address': closest_place['address'],
                'road_address': closest_place['road_address'],
                'phone': closest_place['phone'],
                'place_url': closest_place['place_url'],
                'detection_method': 'keyword_search',
                'all_places': found_places[:3]
            }
        else:
            logger.debug(f"📍 주변 {radius}m 내에 음식점/카페 없음 (키워드 검색)")
            return {
                'is_restaurant': False,
                'message': f'주변 {radius}m 내에 음식점/카페가 없습니다 (키워드 검색)',
                'detection_method': 'keyword_search'
            }
            
    except Exception as e:
        logger.error(f"❌ 키워드 검색 중 오류: {e}")
        return {
            'is_restaurant': False,
            'error': f'Keyword search error: {str(e)}',
            'detection_method': 'keyword_search'
        }

# app.py의 handle_location 함수에서 사용하는 방법:
# place_info = check_current_location_is_restaurant_keyword(lat, lon, radius=Config.DETECTION_RADIUS)

# -*- coding: utf-8 -*-
"""
Location and GPS Utilities
위치 추적, 거리 계산, Kakao API 연동 관련 유틸리티 함수들
"""

import requests
import logging
from typing import Dict, List, Optional, Tuple
from math import radians, cos, sin, asin, sqrt
import time

logger = logging.getLogger(__name__)


def calculate_distance(lat1: float, lon1: float, lat2: float, lon2: float) -> float:
    """두 지점 간 거리를 미터 단위로 계산 (Haversine formula)"""
    try:
        # 지구 반지름 (미터)
        R = 6371000
        
        # 라디안으로 변환
        lat1, lon1, lat2, lon2 = map(radians, [lat1, lon1, lat2, lon2])
        
        # Haversine 공식
        dlat = lat2 - lat1
        dlon = lon2 - lon1
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a))
        
        return R * c
        
    except Exception as e:
        logger.error(f"거리 계산 오류: {e}")
        return float('inf')


def is_within_radius(center_lat: float, center_lon: float, 
                    target_lat: float, target_lon: float, radius: float) -> bool:
    """지정된 반경 내에 있는지 확인"""
    distance = calculate_distance(center_lat, center_lon, target_lat, target_lon)
    return distance <= radius


def check_location_is_restaurant(lat: float, lon: float, api_key: str, 
                                radius: int = 10) -> Dict:
    """
    현재 위치가 음식점/카페인지 Kakao 장소 검색 API로 확인
    
    Args:
        lat (float): 위도
        lon (float): 경도
        api_key (str): Kakao REST API 키
        radius (int): 검색 반경 (미터)
    
    Returns:
        dict: 검색 결과 정보
    """
    try:
        if not api_key or api_key == 'YOUR_KAKAO_REST_API_KEY':
            logger.warning("Kakao REST API 키가 설정되지 않았습니다")
            return {
                'is_restaurant': False,
                'error': 'API key not configured'
            }
        
        # Kakao 장소 검색 API URL
        api_url = "https://dapi.kakao.com/v2/local/search/category.json"
        
        # CE7: 카페, FD6: 음식점 카테고리
        categories = ['CE7', 'FD6']
        
        found_places = []
        
        for category in categories:
            params = {
                'category_group_code': category,
                'x': lon,  # 경도
                'y': lat,  # 위도 
                'radius': radius,  # 검색 반경 (미터)
                'sort': 'distance'  # 거리순 정렬
            }
            
            headers = {
                'Authorization': f'KakaoAK {api_key}'
            }
            
            logger.debug(f"Kakao API 호출: {category} 카테고리, 반경 {radius}m")
            
            response = requests.get(
                api_url,
                params=params,
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                places = data.get('documents', [])
                
                logger.debug(f"{category} 검색 결과: {len(places)}개 장소")
                
                for place in places:
                    distance = float(place.get('distance', 999))
                    if distance <= radius:  # 설정된 반경 내에 있는 장소만
                        found_places.append({
                            'place_name': place.get('place_name', ''),
                            'category_name': place.get('category_name', ''),
                            'distance': distance,
                            'category_code': category,
                            'address': place.get('address_name', ''),
                            'road_address': place.get('road_address_name', ''),
                            'phone': place.get('phone', ''),
                            'place_url': place.get('place_url', '')
                        })
            else:
                logger.error(f"Kakao API 오류: {response.status_code} - {response.text}")
        
        if found_places:
            # 가장 가까운 장소 선택
            closest_place = min(found_places, key=lambda x: x['distance'])
            
            # 신뢰도 계산 (거리가 가까울수록 높음)
            confidence = max(0.1, min(1.0, 1.0 - (closest_place['distance'] / radius)))
            
            logger.info(f"음식점/카페 감지: {closest_place['place_name']} ({closest_place['distance']}m)")
            
            return {
                'is_restaurant': True,
                'place_name': closest_place['place_name'],
                'category': closest_place['category_name'],
                'distance': closest_place['distance'],
                'confidence': confidence,
                'address': closest_place['address'],
                'road_address': closest_place['road_address'],
                'phone': closest_place['phone'],
                'place_url': closest_place['place_url'],
                'all_places': found_places  # 모든 감지된 장소들
            }
        else:
            logger.debug(f"주변 {radius}m 내에 음식점/카페 없음")
            return {
                'is_restaurant': False,
                'message': f'주변 {radius}m 내에 음식점/카페가 없습니다'
            }
            
    except requests.exceptions.Timeout:
        logger.error("Kakao API 타임아웃")
        return {
            'is_restaurant': False,
            'error': 'API timeout'
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"Kakao API 요청 오류: {e}")
        return {
            'is_restaurant': False,
            'error': f'API request failed: {str(e)}'
        }
    except Exception as e:
        logger.error(f"장소 검색 중 예상치 못한 오류: {e}")
        return {
            'is_restaurant': False,
            'error': f'Unexpected error: {str(e)}'
        }


def validate_coordinates(lat: float, lon: float) -> Tuple[bool, str]:
    """좌표 유효성 검사"""
    try:
        lat = float(lat)
        lon = float(lon)
        
        if not (-90 <= lat <= 90):
            return False, f"위도는 -90~90 범위여야 합니다: {lat}"
        
        if not (-180 <= lon <= 180):
            return False, f"경도는 -180~180 범위여야 합니다: {lon}"
        
        return True, "Valid coordinates"
        
    except (ValueError, TypeError) as e:
        return False, f"좌표가 숫자가 아닙니다: {e}"


def get_location_display_name(lat: float, lon: float, api_key: str) -> Optional[str]:
    """좌표를 주소로 변환 (Reverse Geocoding)"""
    try:
        if not api_key or api_key == 'YOUR_KAKAO_REST_API_KEY':
            return None
        
        api_url = "https://dapi.kakao.com/v2/local/geo/coord2address.json"
        
        params = {
            'x': lon,
            'y': lat,
            'input_coord': 'WGS84'
        }
        
        headers = {
            'Authorization': f'KakaoAK {api_key}'
        }
        
        response = requests.get(api_url, params=params, headers=headers, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            documents = data.get('documents', [])
            
            if documents:
                # 도로명 주소 우선, 없으면 지번 주소
                address_info = documents[0]
                road_address = address_info.get('road_address')
                jibun_address = address_info.get('address')
                
                if road_address:
                    return road_address.get('address_name', '')
                elif jibun_address:
                    return jibun_address.get('address_name', '')
        
        return None
        
    except Exception as e:
        logger.debug(f"주소 변환 실패: {e}")
        return None


def find_nearby_places(lat: float, lon: float, api_key: str, 
                      category: str = 'FD6', radius: int = 100) -> List[Dict]:
    """주변 장소 검색"""
    try:
        if not api_key or api_key == 'YOUR_KAKAO_REST_API_KEY':
            return []
        
        api_url = "https://dapi.kakao.com/v2/local/search/category.json"
        
        params = {
            'category_group_code': category,
            'x': lon,
            'y': lat,
            'radius': radius,
            'sort': 'distance',
            'size': 15  # 최대 15개
        }
        
        headers = {
            'Authorization': f'KakaoAK {api_key}'
        }
        
        response = requests.get(api_url, params=params, headers=headers, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            places = data.get('documents', [])
            
            result = []
            for place in places:
                result.append({
                    'name': place.get('place_name', ''),
                    'category': place.get('category_name', ''),
                    'distance': float(place.get('distance', 0)),
                    'address': place.get('address_name', ''),
                    'road_address': place.get('road_address_name', ''),
                    'phone': place.get('phone', ''),
                    'url': place.get('place_url', ''),
                    'x': float(place.get('x', 0)),
                    'y': float(place.get('y', 0))
                })
            
            return result
        
        return []
        
    except Exception as e:
        logger.error(f"주변 장소 검색 오류: {e}")
        return []


class LocationTracker:
    """위치 추적 관리 클래스"""
    
    def __init__(self, api_key: str, detection_radius: float = 10.0, 
                 required_stay_time: int = 5):
        self.api_key = api_key
        self.detection_radius = detection_radius
        self.required_stay_time = required_stay_time
        self.visit_times = {}  # 장소별 입장 시간
        self.notification_sent = set()  # 알림 전송된 장소들
        
    def update_location(self, lat: float, lon: float) -> Dict:
        """위치 업데이트 및 상태 반환"""
        current_time = time.time()
        
        # 현재 위치 검사
        place_info = check_location_is_restaurant(
            lat, lon, self.api_key, int(self.detection_radius)
        )
        
        if place_info.get('is_restaurant', False):
            place_name = place_info['place_name']
            
            # 방문 기록 관리
            if place_name not in self.visit_times:
                # 새로운 장소 입장
                self.visit_times[place_name] = current_time
                return {
                    'status': 'entered',
                    'place_info': place_info,
                    'stay_duration': 0,
                    'notification_ready': False
                }
            else:
                # 기존 장소에서 체류 중
                stay_duration = current_time - self.visit_times[place_name]
                notification_ready = (
                    stay_duration >= self.required_stay_time and 
                    place_name not in self.notification_sent
                )
                
                if notification_ready:
                    self.notification_sent.add(place_name)
                
                return {
                    'status': 'staying',
                    'place_info': place_info,
                    'stay_duration': stay_duration,
                    'notification_ready': notification_ready
                }
        else:
            # 음식점/카페 외부
            left_places = []
            for place_name in list(self.visit_times.keys()):
                stay_duration = current_time - self.visit_times[place_name]
                left_places.append({
                    'name': place_name,
                    'stay_duration': stay_duration
                })
                del self.visit_times[place_name]
            
            # 알림 상태 초기화
            self.notification_sent.clear()
            
            return {
                'status': 'outside',
                'place_info': place_info,
                'left_places': left_places,
                'notification_ready': False
            }
    
    def reset(self):
        """추적 상태 초기화"""
        self.visit_times.clear()
        self.notification_sent.clear()
        logger.info("위치 추적 상태 초기화")

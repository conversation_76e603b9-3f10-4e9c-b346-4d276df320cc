#!/usr/bin/env python3
"""
AI Project GPS Installation Verifier
환경 설정과 설치 상태를 확인하고 문제를 진단합니다.
"""

import os
import sys
import importlib
import json
from pathlib import Path
from datetime import datetime

# 색상 코드 (Windows 콘솔 지원)
class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    RESET = '\033[0m'
    
    @staticmethod
    def init_windows_colors():
        """Windows에서 ANSI 색상 코드 활성화"""
        if sys.platform == 'win32':
            try:
                import ctypes
                kernel32 = ctypes.windll.kernel32
                kernel32.SetConsoleMode(kernel32.GetStdHandle(-11), 7)
            except:
                pass

Colors.init_windows_colors()

def print_header():
    """헤더 출력"""
    print(f"\n{Colors.BLUE}{'='*70}{Colors.RESET}")
    print(f"{Colors.BLUE}AI Project GPS - Installation Verifier{Colors.RESET}")
    print(f"{Colors.BLUE}{'='*70}{Colors.RESET}\n")

def check_python_version():
    """Python 버전 확인"""
    print(f"{Colors.YELLOW}[1/7] Python 버전 확인{Colors.RESET}")
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"{Colors.GREEN}✓ Python {version.major}.{version.minor}.{version.micro} - OK{Colors.RESET}")
        return True
    else:
        print(f"{Colors.RED}✗ Python {version.major}.{version.minor}.{version.micro} - 3.8 이상 필요{Colors.RESET}")
        return False

def check_required_packages():
    """필수 패키지 확인"""
    print(f"\n{Colors.YELLOW}[2/7] 필수 패키지 확인{Colors.RESET}")
    
    required_packages = {
        'flask': 'Flask',
        'flask_socketio': 'Flask-SocketIO',
        'geopy': 'geopy',
        'werkzeug': 'Werkzeug',
        'requests': 'requests'
    }
    
    missing_packages = []
    installed_packages = []
    
    for module, package in required_packages.items():
        try:
            importlib.import_module(module)
            installed_packages.append(package)
            print(f"{Colors.GREEN}✓ {package} - 설치됨{Colors.RESET}")
        except ImportError:
            missing_packages.append(package)
            print(f"{Colors.RED}✗ {package} - 설치 필요{Colors.RESET}")
    
    return missing_packages, installed_packages

def check_env_file():
    """환경 설정 파일 확인"""
    print(f"\n{Colors.YELLOW}[3/7] 환경 설정 파일 확인{Colors.RESET}")
    
    env_path = Path('.env')
    if not env_path.exists():
        print(f"{Colors.RED}✗ .env 파일이 없습니다{Colors.RESET}")
        return False
    
    print(f"{Colors.GREEN}✓ .env 파일 발견{Colors.RESET}")
    
    # .env 파일 내용 검증
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 주석이 값에 포함되어 있는지 확인
        issues = []
        for line in content.split('\n'):
            if '=' in line and not line.strip().startswith('#'):
                key, value = line.split('=', 1)
                if '#' in value:
                    issues.append(f"  - {key.strip()}: 값에 주석이 포함되어 있습니다")
        
        if issues:
            print(f"{Colors.YELLOW}⚠ .env 파일 문제 발견:{Colors.RESET}")
            for issue in issues:
                print(f"{Colors.YELLOW}{issue}{Colors.RESET}")
            return False
        else:
            print(f"{Colors.GREEN}✓ .env 파일 형식 정상{Colors.RESET}")
            return True
            
    except Exception as e:
        print(f"{Colors.RED}✗ .env 파일 읽기 오류: {e}{Colors.RESET}")
        return False

def check_required_files():
    """필수 파일 확인"""
    print(f"\n{Colors.YELLOW}[4/7] 필수 파일 확인{Colors.RESET}")
    
    required_files = [
        'app.py',
        'restaurants.json',
        'restaurant_visitor.py',
        'food_database.py',
        'nutrition_api.py',
        'user_profile.py',
        'templates/index.html'
    ]
    
    missing_files = []
    
    for file in required_files:
        if Path(file).exists():
            print(f"{Colors.GREEN}✓ {file} - OK{Colors.RESET}")
        else:
            missing_files.append(file)
            print(f"{Colors.RED}✗ {file} - 파일 없음{Colors.RESET}")
    
    return missing_files

def check_directories():
    """필수 디렉토리 확인"""
    print(f"\n{Colors.YELLOW}[5/7] 필수 디렉토리 확인{Colors.RESET}")
    
    required_dirs = ['uploads', 'logs', 'templates']
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"{Colors.GREEN}✓ {dir_name}/ - OK{Colors.RESET}")
        else:
            # 디렉토리 생성 시도
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"{Colors.YELLOW}✓ {dir_name}/ - 생성됨{Colors.RESET}")
            except Exception as e:
                print(f"{Colors.RED}✗ {dir_name}/ - 생성 실패: {e}{Colors.RESET}")

def check_analyzer_api():
    """Analyzer API 서버 연결 확인"""
    print(f"\n{Colors.YELLOW}[6/7] Analyzer API 서버 확인{Colors.RESET}")
    
    try:
        import requests
        # .env에서 URL 읽기
        analyzer_url = 'http://localhost:5001'
        
        try:
            response = requests.get(f"{analyzer_url}/health", timeout=2)
            if response.status_code == 200:
                print(f"{Colors.GREEN}✓ Analyzer API 서버 연결 성공{Colors.RESET}")
                return True
            else:
                print(f"{Colors.YELLOW}⚠ Analyzer API 서버 응답 오류 (상태 코드: {response.status_code}){Colors.RESET}")
                return False
        except requests.exceptions.ConnectionError:
            print(f"{Colors.RED}✗ Analyzer API 서버에 연결할 수 없습니다{Colors.RESET}")
            print(f"  → analyzer-master 폴더에서 다음 명령 실행:")
            print(f"  → cd C:\\2025_Project\\analyzer-master")
            print(f"  → python api_server.py")
            return False
        except Exception as e:
            print(f"{Colors.RED}✗ API 확인 중 오류: {e}{Colors.RESET}")
            return False
            
    except ImportError:
        print(f"{Colors.RED}✗ requests 모듈이 설치되지 않아 확인할 수 없습니다{Colors.RESET}")
        return False

def test_imports():
    """모든 필요한 모듈 import 테스트"""
    print(f"\n{Colors.YELLOW}[7/7] 모듈 import 테스트{Colors.RESET}")
    
    test_imports_list = [
        ('flask', 'Flask, render_template, request, jsonify, redirect, url_for'),
        ('flask_socketio', 'SocketIO'),
        ('geopy.distance', 'geodesic'),
        ('werkzeug.utils', 'secure_filename'),
    ]
    
    all_good = True
    
    for module, items in test_imports_list:
        try:
            exec(f"from {module} import {items}")
            print(f"{Colors.GREEN}✓ from {module} import {items} - OK{Colors.RESET}")
        except Exception as e:
            print(f"{Colors.RED}✗ from {module} import {items} - 실패: {e}{Colors.RESET}")
            all_good = False
    
    return all_good

def generate_fix_script(missing_packages):
    """문제 해결 스크립트 생성"""
    if missing_packages:
        print(f"\n{Colors.YELLOW}설치 명령:{Colors.RESET}")
        print(f"pip install {' '.join(missing_packages)}")
        
        # 배치 파일 생성
        with open('fix_install.bat', 'w') as f:
            f.write('@echo off\n')
            f.write('echo Installing missing packages...\n')
            f.write(f'pip install {" ".join(missing_packages)}\n')
            f.write('echo.\n')
            f.write('echo Installation complete!\n')
            f.write('pause\n')
        
        print(f"\n{Colors.GREEN}✓ fix_install.bat 파일이 생성되었습니다.{Colors.RESET}")
        print(f"  이 파일을 실행하여 누락된 패키지를 설치할 수 있습니다.")

def main():
    """메인 함수"""
    print_header()
    
    # 현재 디렉토리 확인
    current_dir = Path.cwd()
    print(f"현재 디렉토리: {current_dir}")
    
    if not (current_dir / 'app.py').exists():
        print(f"\n{Colors.RED}오류: ai-project-gps 디렉토리에서 실행해주세요!{Colors.RESET}")
        return
    
    # 각 항목 체크
    results = {
        'python_version': check_python_version(),
        'packages': check_required_packages(),
        'env_file': check_env_file(),
        'files': check_required_files(),
        'directories': check_directories(),
        'analyzer_api': check_analyzer_api(),
        'imports': test_imports()
    }
    
    # 결과 요약
    print(f"\n{Colors.BLUE}{'='*70}{Colors.RESET}")
    print(f"{Colors.BLUE}검증 결과 요약{Colors.RESET}")
    print(f"{Colors.BLUE}{'='*70}{Colors.RESET}")
    
    missing_packages, installed_packages = results['packages']
    missing_files = results['files']
    
    all_good = True
    
    if not results['python_version']:
        print(f"{Colors.RED}✗ Python 버전을 업그레이드하세요 (3.8 이상){Colors.RESET}")
        all_good = False
    
    if missing_packages:
        print(f"{Colors.RED}✗ 누락된 패키지: {', '.join(missing_packages)}{Colors.RESET}")
        generate_fix_script(missing_packages)
        all_good = False
    
    if not results['env_file']:
        print(f"{Colors.RED}✗ .env 파일을 확인하고 수정하세요{Colors.RESET}")
        all_good = False
    
    if missing_files:
        print(f"{Colors.RED}✗ 누락된 파일: {', '.join(missing_files)}{Colors.RESET}")
        all_good = False
    
    if not results['analyzer_api']:
        print(f"{Colors.YELLOW}⚠ Analyzer API 서버를 시작하세요{Colors.RESET}")
    
    if all_good:
        print(f"\n{Colors.GREEN}✅ 모든 설정이 정상입니다! app.py를 실행할 수 있습니다.{Colors.RESET}")
        print(f"\n실행 명령: python app.py")
    else:
        print(f"\n{Colors.YELLOW}⚠ 위의 문제들을 해결한 후 다시 실행하세요.{Colors.RESET}")
    
    # 로그 파일 생성
    log_content = {
        'timestamp': datetime.now().isoformat(),
        'results': {
            'python_version': results['python_version'],
            'missing_packages': missing_packages,
            'installed_packages': installed_packages,
            'env_file_ok': results['env_file'],
            'missing_files': missing_files,
            'analyzer_api': results['analyzer_api'],
            'imports_ok': results['imports']
        }
    }
    
    with open('verify_install_log.json', 'w', encoding='utf-8') as f:
        json.dump(log_content, f, indent=2, ensure_ascii=False)
    
    print(f"\n{Colors.BLUE}상세 로그: verify_install_log.json{Colors.RESET}")

if __name__ == "__main__":
    main()

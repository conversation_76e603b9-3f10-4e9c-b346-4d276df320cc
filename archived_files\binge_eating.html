<!DOCTYPE html>
<html>
<head>
    <title>과식한 날 - AI 칼로리 관리</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #1d1d1f;
            min-height: 100vh;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            color: #1d1d1f;
            padding: 30px 20px;
            text-align: center;
            box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .header h1 {
            margin: 0 0 12px 0;
            font-size: 36px;
            font-weight: 700;
            letter-spacing: -0.5px;
        }
        
        .header p {
            margin: 0;
            color: #86868b;
            font-size: 18px;
            font-weight: 400;
        }
        
        .nav-bar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            padding: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            justify-content: center;
            gap: 16px;
            flex-wrap: wrap;
        }
        
        .nav-btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.3s ease;
            display: inline-block;
            cursor: pointer;
        }
        
        .nav-btn:hover {
            background: #0056cc;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
        }
        
        .nav-btn.current {
            background: #ff3b30;
        }
        
        .nav-btn.current:hover {
            background: #d70015;
            box-shadow: 0 8px 24px rgba(255, 59, 48, 0.3);
        }
        
        .nav-btn.secondary {
            background: #34c759;
        }
        
        .nav-btn.secondary:hover {
            background: #28a745;
            box-shadow: 0 8px 24px rgba(52, 199, 89, 0.3);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 24px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .status-card h2 {
            margin: 0 0 20px 0;
            font-size: 24px;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .calorie-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 24px;
        }
        
        .calorie-item {
            text-align: center;
            padding: 24px;
            background: #f2f2f7;
            border-radius: 16px;
            transition: all 0.3s ease;
        }
        
        .calorie-item.excess {
            background: rgba(255, 59, 48, 0.1);
            border-left: 4px solid #ff3b30;
        }
        
        .calorie-item.normal {
            background: rgba(52, 199, 89, 0.1);
            border-left: 4px solid #34c759;
        }
        
        .calorie-item.under {
            background: rgba(255, 149, 0, 0.1);
            border-left: 4px solid #ff9500;
        }
        
        .calorie-value {
            font-size: 28px;
            font-weight: 700;
            color: #1d1d1f;
            margin-bottom: 8px;
        }
        
        .calorie-label {
            color: #86868b;
            font-size: 14px;
            font-weight: 500;
        }
        
        .analysis-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }
        
        .analysis-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 24px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .analysis-card h3 {
            margin: 0 0 16px 0;
            font-size: 20px;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .food-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .food-item {
            padding: 12px 16px;
            margin: 8px 0;
            background: #f2f2f7;
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .food-item.high-cal {
            background: rgba(255, 59, 48, 0.1);
            border-left: 3px solid #ff3b30;
        }
        
        .food-name {
            font-weight: 500;
            color: #1d1d1f;
        }
        
        .food-calories {
            font-weight: 600;
            color: #86868b;
        }
        
        .food-time {
            font-size: 12px;
            color: #86868b;
        }
        
        .suggestion-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .suggestion-item {
            padding: 12px 16px;
            margin: 8px 0;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 12px;
            border-left: 3px solid #007aff;
            color: #1d1d1f;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .coping-strategies {
            margin-top: 24px;
        }
        
        .strategy-category {
            margin-bottom: 20px;
        }
        
        .strategy-category h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: #1d1d1f;
        }
        
        .strategy-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .strategy-item {
            padding: 8px 12px;
            margin: 4px 0;
            background: #f8f9fa;
            border-radius: 8px;
            font-size: 14px;
            color: #1d1d1f;
            border-left: 2px solid #007aff;
        }
        
        .alert-message {
            padding: 16px 20px;
            border-radius: 12px;
            margin: 16px 0;
            font-weight: 500;
        }
        
        .alert-success {
            background: rgba(52, 199, 89, 0.1);
            color: #1d1d1f;
            border: 1px solid rgba(52, 199, 89, 0.3);
        }
        
        .alert-warning {
            background: rgba(255, 149, 0, 0.1);
            color: #1d1d1f;
            border: 1px solid rgba(255, 149, 0, 0.3);
        }
        
        .alert-danger {
            background: rgba(255, 59, 48, 0.1);
            color: #1d1d1f;
            border: 1px solid rgba(255, 59, 48, 0.3);
        }
        
        .action-buttons {
            display: flex;
            gap: 16px;
            margin-top: 24px;
            flex-wrap: wrap;
        }
        
        .action-btn {
            background: #007aff;
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .action-btn:hover {
            background: #0056cc;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
        }
        
        .action-btn.exercise {
            background: #34c759;
        }
        
        .action-btn.exercise:hover {
            background: #28a745;
            box-shadow: 0 8px 24px rgba(52, 199, 89, 0.3);
        }
        
        .timeline {
            margin-top: 20px;
        }
        
        .timeline-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-left: 2px solid #e5e5ea;
            padding-left: 16px;
            margin-left: 8px;
            position: relative;
        }
        
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -5px;
            width: 8px;
            height: 8px;
            background: #007aff;
            border-radius: 50%;
        }
        
        .timeline-time {
            font-weight: 600;
            color: #007aff;
            margin-right: 12px;
            min-width: 60px;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .timeline-food {
            font-weight: 500;
            color: #1d1d1f;
        }
        
        .timeline-calories {
            font-size: 12px;
            color: #86868b;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 16px;
            }
            
            .header h1 {
                font-size: 28px;
            }
            
            .nav-bar {
                padding: 16px;
            }
            
            .nav-btn {
                padding: 10px 20px;
                font-size: 13px;
            }
            
            .calorie-summary {
                grid-template-columns: 1fr;
            }
            
            .analysis-section {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>과식한 날 분석</h1>
        <p>오늘의 식습관을 분석하고 건강한 대처 방법을 찾아보세요</p>
    </div>

    <div class="nav-bar">
        <a href="/profile" class="nav-btn secondary">프로필</a>
        <a href="/dashboard" class="nav-btn secondary">대시보드</a>
        <a href="/upload_food" class="nav-btn">음식 등록</a>
        <a href="/receipt" class="nav-btn">영수증 등록</a>
        <a href="/exercise" class="nav-btn">운동 추천</a>
        <a href="/" class="nav-btn">위치 추적</a>
        <a href="/binge_eating" class="nav-btn current">과식한 날</a>
    </div>

    <div class="container">
        <!-- 칼로리 요약 카드 -->
        <div class="status-card">
            <h2>오늘 칼로리 요약</h2>
            <div class="calorie-summary">
                <div class="calorie-item {{ 'excess' if is_binge else 'normal' }}">
                    <div class="calorie-value">{{ "%.0f"|format(daily_calories) }}</div>
                    <div class="calorie-label">섭취 칼로리 (kcal)</div>
                </div>
                <div class="calorie-item">
                    <div class="calorie-value">{{ "%.0f"|format(target_calories) }}</div>
                    <div class="calorie-label">목표 칼로리 (kcal)</div>
                </div>
                <div class="calorie-item {{ 'excess' if excess_calories > 0 else 'under' }}">
                    <div class="calorie-value">{{ "%.0f"|format(excess_calories) if excess_calories > 0 else "0" }}</div>
                    <div class="calorie-label">초과 칼로리 (kcal)</div>
                </div>
                <div class="calorie-item">
                    <div class="calorie-value">{{ today_records|length }}</div>
                    <div class="calorie-label">식사/간식 횟수</div>
                </div>
            </div>
            
            {% if is_binge %}
                <div class="alert-message alert-warning">
                    <strong>⚠️ 과식 패턴 감지</strong><br>
                    오늘 {{ "%.0f"|format(excess_calories) }} kcal를 초과 섭취했습니다. 
                    걱정하지 마세요. 아래 분석과 대처 방법을 참고해보세요.
                </div>
            {% elif excess_calories > 0 %}
                <div class="alert-message alert-warning">
                    <strong>📊 약간의 초과 섭취</strong><br>
                    목표보다 {{ "%.0f"|format(excess_calories) }} kcal 초과했지만 정상 범위입니다.
                </div>
            {% else %}
                <div class="alert-message alert-success">
                    <strong>✅ 훌륭한 칼로리 관리</strong><br>
                    오늘 목표 칼로리를 잘 지켰습니다!
                </div>
            {% endif %}
        </div>

        <!-- 분석 섹션 -->
        <div class="analysis-section">
            <!-- 오늘 먹은 음식 -->
            <div class="analysis-card">
                <h3>🍽️ 오늘 먹은 음식</h3>
                {% if today_records %}
                    <div class="timeline">
                        {% for record in today_records %}
                        <div class="timeline-item">
                            <div class="timeline-time">{{ record.timestamp[11:16] }}</div>
                            <div class="timeline-content">
                                <div class="timeline-food">{{ record.food_name }}</div>
                                <div class="timeline-calories">{{ "%.0f"|format(record.calories) }} kcal • {{ record.restaurant_name }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <p>오늘 등록된 음식이 없습니다.</p>
                {% endif %}
            </div>

            <!-- 문제 음식 분석 -->
            <div class="analysis-card">
                <h3>🚨 고칼로리 음식</h3>
                {% if binge_analysis.high_calorie_meals %}
                    <ul class="food-list">
                        {% for meal in binge_analysis.high_calorie_meals %}
                        <li class="food-item high-cal">
                            <div>
                                <div class="food-name">{{ meal.food_name }}</div>
                                <div class="food-time">{{ meal.timestamp[11:16] }} • {{ meal.restaurant_name }}</div>
                            </div>
                            <div class="food-calories">{{ "%.0f"|format(meal.calories) }} kcal</div>
                        </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="alert-message alert-success">
                        고칼로리 음식 섭취가 없었습니다! 👍
                    </div>
                {% endif %}
            </div>

            <!-- 식습관 개선 제안 -->
            <div class="analysis-card">
                <h3>💡 식습관 개선 제안</h3>
                <ul class="suggestion-list">
                    {% for suggestion in binge_analysis.suggestions %}
                    <li class="suggestion-item">{{ suggestion }}</li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <!-- 대처 전략 -->
        {% if is_binge %}
        <div class="status-card">
            <h2>🎯 과식 대처 전략</h2>
            <div class="coping-strategies">
                <div class="strategy-category">
                    <h4>🚨 즉시 실행</h4>
                    <ul class="strategy-list">
                        {% for strategy in coping_strategies.immediate %}
                        <li class="strategy-item">{{ strategy }}</li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="strategy-category">
                    <h4>🏃‍♂️ 운동으로 해결</h4>
                    <ul class="strategy-list">
                        {% for strategy in coping_strategies.exercise %}
                        <li class="strategy-item">{{ strategy }}</li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="strategy-category">
                    <h4>🥗 다음 식사 계획</h4>
                    <ul class="strategy-list">
                        {% for strategy in coping_strategies.nutrition %}
                        <li class="strategy-item">{{ strategy }}</li>
                        {% endfor %}
                    </ul>
                </div>

                <div class="strategy-category">
                    <h4>🧠 마음가짐</h4>
                    <ul class="strategy-list">
                        {% for strategy in coping_strategies.mindset %}
                        <li class="strategy-item">{{ strategy }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- 행동 버튼 -->
        <div class="status-card">
            <h2>🚀 지금 바로 실행하기</h2>
            <div class="action-buttons">
                <a href="/exercise" class="action-btn exercise">운동 추천 보기</a>
                <a href="/upload_food" class="action-btn">건강한 음식 추가</a>
                <a href="/dashboard" class="action-btn">대시보드로 돌아가기</a>
                <button onclick="generateSampleData()" class="action-btn">샘플 데이터 생성</button>
            </div>
        </div>
    </div>

    <script>
        // 샘플 데이터 생성 함수
        async function generateSampleData() {
            if (confirm('운동 기능 테스트를 위한 고칼로리 샘플 데이터를 생성합니다.\n(4150 kcal 샘플 음식 + 프로필)\n\n계속하시겠습니까?')) {
                try {
                    showToast('샘플 데이터 생성 중...', 'info');
                    
                    const response = await fetch('/api/add_sample_data', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        showToast(`✅ 샘플 데이터 생성 완료! (${result.data.total_calories} kcal)`, 'success');
                        
                        // 상세 정보 표시
                        alert(`🎉 샘플 데이터 생성 완료!\n\n` +
                              `🍔 음식: ${result.data.food_count}개\n` +
                              `🔥 총 칼로리: ${result.data.total_calories} kcal\n` +
                              `⚡ TDEE: ${result.data.tdee} kcal\n` +
                              `🏃‍♂️ 초과 칼로리: ${result.data.excess_calories:.0f} kcal\n\n` +
                              `📝 테스트 방법:\n` +
                              `1. 페이지 새로고침으로 데이터 확인\n` +
                              `2. 운동 추천 페이지에서 초과 칼로리 확인\n` +
                              `3. 대처 전략 확인`);
                        
                        // 3초 후 페이지 새로고침
                        setTimeout(() => {
                            window.location.reload();
                        }, 3000);
                        
                    } else {
                        showToast(`❌ 샘플 데이터 생성 실패: ${result.error}`, 'error');
                        console.error('샘플 데이터 오류:', result.error);
                    }
                    
                } catch (error) {
                    showToast('❌ 샘플 데이터 생성 실패', 'error');
                    console.error('샘플 데이터 오류:', error);
                }
            }
        }

        function showToast(message, type = 'info') {
            // 기존 토스트 컨테이너 찾기
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 300px;
                `;
                document.body.appendChild(toastContainer);
            }
            
            // 토스트 요소 생성
            const toast = document.createElement('div');
            const bgColor = {
                'info': 'rgba(0, 122, 255, 0.9)',
                'success': 'rgba(52, 199, 89, 0.9)',
                'warning': 'rgba(255, 149, 0, 0.9)',
                'error': 'rgba(255, 59, 48, 0.9)'
            }[type] || 'rgba(0, 122, 255, 0.9)';
            
            toast.style.cssText = `
                background: ${bgColor};
                color: white;
                padding: 12px 16px;
                border-radius: 12px;
                margin-bottom: 8px;
                font-size: 14px;
                font-weight: 500;
                backdrop-filter: blur(20px);
                -webkit-backdrop-filter: blur(20px);
                box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
                animation: slideInRight 0.3s ease;
                cursor: pointer;
            `;
            
            toast.textContent = message;
            
            // 클릭시 제거
            toast.addEventListener('click', () => {
                toast.remove();
            });
            
            // 3초 후 자동 제거
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
            
            toastContainer.appendChild(toast);
        }
    </script>
</body>
</html>
<!DOCTYPE html>
<html>
<head>
    <title>사용자 프로필 설정 - AI 칼로리 관리</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #2c3e50;
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        .form-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .form-section h3 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 18px;
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 10px;
        }
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .form-group {
            display: flex;
            flex-direction: column;
        }
        .form-group label {
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        .form-group input, .form-group select {
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #4CAF50;
        }
        .form-group small {
            margin-top: 5px;
            color: #666;
            font-size: 12px;
        }
        .preset-profiles {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .preset-card {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        .preset-card:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        .preset-card.selected {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        .preset-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        .preset-desc {
            font-size: 12px;
            color: #666;
        }
        .calculation-result {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .calc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .calc-item {
            text-align: center;
            padding: 15px;
            background: white;
            border-radius: 6px;
        }
        .calc-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .calc-label {
            font-size: 12px;
            color: #666;
        }
        .buttons {
            display: flex;
            gap: 15px;
            margin-top: 30px;
        }
        .btn {
            flex: 1;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        .btn-primary:hover:not(:disabled) {
            background: #45a049;
            transform: translateY(-1px);
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        .navigation {
            text-align: center;
            margin-top: 30px;
        }
        .nav-link {
            color: #4CAF50;
            text-decoration: none;
            font-weight: 500;
            margin: 0 15px;
            padding: 10px 20px;
            border: 2px solid #4CAF50;
            border-radius: 6px;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .nav-link:hover {
            background: #4CAF50;
            color: white;
        }
        .result {
            margin-top: 25px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            .form-grid {
                grid-template-columns: 1fr;
            }
            .calc-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            .buttons {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👤 사용자 프로필 설정</h1>
            <p>목표 달성을 위한 개인 정보를 설정하고 맞춤형 칼로리 목표를 계산해보세요</p>
        </div>

        <!-- 미리 설정된 프로필 -->
        <div class="form-section">
            <h3>🎯 빠른 설정</h3>
            <div class="preset-profiles">
                <div class="preset-card" onclick="loadPreset('weight_loss')">
                    <div class="preset-title">📉 체중 감량</div>
                    <div class="preset-desc">건강한 체중 감량을 위한 설정</div>
                </div>
                <div class="preset-card" onclick="loadPreset('weight_gain')">
                    <div class="preset-title">📈 체중 증량</div>
                    <div class="preset-desc">근육량 증가를 위한 설정</div>
                </div>
                <div class="preset-card" onclick="loadPreset('maintenance')">
                    <div class="preset-title">⚖️ 체중 유지</div>
                    <div class="preset-desc">현재 체중 유지를 위한 설정</div>
                </div>
            </div>
        </div>

        <form id="profile-form">
            <!-- 기본 정보 -->
            <div class="form-section">
                <h3>📋 기본 정보</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="current_weight">현재 체중 (kg)</label>
                        <input type="number" id="current_weight" name="current_weight" 
                               step="0.1" min="30" max="200" required>
                        <small>정확한 체중을 입력해주세요</small>
                    </div>
                    <div class="form-group">
                        <label for="height">키 (cm)</label>
                        <input type="number" id="height" name="height" 
                               step="0.1" min="100" max="250" required>
                        <small>신발을 벗은 상태의 키</small>
                    </div>
                    <div class="form-group">
                        <label for="age">나이</label>
                        <input type="number" id="age" name="age" 
                               min="10" max="100" required>
                        <small>만 나이로 입력</small>
                    </div>
                    <div class="form-group">
                        <label for="gender">성별</label>
                        <select id="gender" name="gender" required>
                            <option value="">선택해주세요</option>
                            <option value="male">남성</option>
                            <option value="female">여성</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 목표 설정 -->
            <div class="form-section">
                <h3>🎯 목표 설정</h3>
                <div class="form-grid">
                    <div class="form-group">
                        <label for="target_weight">목표 체중 (kg)</label>
                        <input type="number" id="target_weight" name="target_weight" 
                               step="0.1" min="30" max="200" required>
                        <small>달성하고 싶은 체중</small>
                    </div>
                    <div class="form-group">
                        <label for="target_date">목표 달성일</label>
                        <input type="date" id="target_date" name="target_date" required>
                        <small>현실적인 기간을 설정하세요</small>
                    </div>
                    <div class="form-group">
                        <label for="activity_level">활동 수준</label>
                        <select id="activity_level" name="activity_level" required>
                            <option value="">선택해주세요</option>
                            <option value="sedentary">거의 운동 안함</option>
                            <option value="light">가벼운 운동 (주 1-3회)</option>
                            <option value="moderate">보통 운동 (주 3-5회)</option>
                            <option value="active">활발한 운동 (주 6-7회)</option>
                            <option value="very_active">매우 활발 (하루 2회)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 계산 결과 -->
            <div class="calculation-result" id="calculation-result" style="display: none;">
                <h3>📊 계산 결과</h3>
                <div class="calc-grid">
                    <div class="calc-item">
                        <div class="calc-value" id="bmr-value">-</div>
                        <div class="calc-label">기초대사율 (BMR)</div>
                    </div>
                    <div class="calc-item">
                        <div class="calc-value" id="tdee-value">-</div>
                        <div class="calc-label">일일 소모 칼로리</div>
                    </div>
                    <div class="calc-item">
                        <div class="calc-value" id="goal-value">-</div>
                        <div class="calc-label">목표 칼로리</div>
                    </div>
                    <div class="calc-item">
                        <div class="calc-value" id="period-value">-</div>
                        <div class="calc-label">목표까지 일수</div>
                    </div>
                </div>
            </div>

            <div class="buttons">
                <button type="button" class="btn btn-secondary" onclick="calculatePreview()">
                    🧮 미리 계산하기
                </button>
                <button type="submit" class="btn btn-primary" id="save-btn">
                    💾 저장하기
                </button>
            </div>
        </form>

        <div class="result" id="result"></div>

        <div class="navigation">
            <a href="/dashboard" class="nav-link">📊 대시보드로 돌아가기</a>
            <a href="/" class="nav-link">🗺️ 메인 페이지</a>
            <a href="/upload_food" class="nav-link">📸 음식 등록</a>
        </div>
    </div>

    <script>
        // 프리셋 데이터
        const presets = {
            weight_loss: {
                current_weight: 70,
                target_weight: 65,
                height: 170,
                age: 25,
                gender: 'male',
                activity_level: 'moderate',
                target_date: getDateAfterDays(60) // 2개월 후
            },
            weight_gain: {
                current_weight: 55,
                target_weight: 60,
                height: 165,
                age: 22,
                gender: 'female',
                activity_level: 'light',
                target_date: getDateAfterDays(90) // 3개월 후
            },
            maintenance: {
                current_weight: 65,
                target_weight: 65,
                height: 168,
                age: 30,
                gender: 'male',
                activity_level: 'moderate',
                target_date: getDateAfterDays(365) // 1년 후
            }
        };

        function getDateAfterDays(days) {
            const date = new Date();
            date.setDate(date.getDate() + days);
            return date.toISOString().split('T')[0];
        }

        function loadPreset(presetName) {
            const preset = presets[presetName];
            if (!preset) return;

            // UI 업데이트
            document.querySelectorAll('.preset-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.preset-card').classList.add('selected');

            // 폼 데이터 설정
            Object.keys(preset).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = preset[key];
                }
            });

            // 미리 계산
            setTimeout(calculatePreview, 100);
        }

        function calculateBMR(weight, height, age, gender) {
            if (gender === 'male') {
                return 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
            } else {
                return 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
            }
        }

        function calculateTDEE(bmr, activityLevel) {
            const multipliers = {
                'sedentary': 1.2,
                'light': 1.375,
                'moderate': 1.55,
                'active': 1.725,
                'very_active': 1.9
            };
            return bmr * (multipliers[activityLevel] || 1.2);
        }

        function calculatePreview() {
            try {
                // 폼 데이터 수집
                const formData = new FormData(document.getElementById('profile-form'));
                const data = Object.fromEntries(formData);

                // 유효성 검사
                const requiredFields = ['current_weight', 'height', 'age', 'gender', 'target_weight', 'target_date', 'activity_level'];
                for (const field of requiredFields) {
                    if (!data[field]) {
                        alert(`${field} 항목을 입력해주세요.`);
                        return;
                    }
                }

                // 계산
                const weight = parseFloat(data.current_weight);
                const height = parseFloat(data.height);
                const age = parseInt(data.age);
                const targetWeight = parseFloat(data.target_weight);
                const targetDate = new Date(data.target_date);
                const today = new Date();

                const bmr = calculateBMR(weight, height, age, data.gender);
                const tdee = calculateTDEE(bmr, data.activity_level);

                // 목표까지 일수
                const daysToTarget = Math.ceil((targetDate - today) / (1000 * 60 * 60 * 24));
                
                // 체중 변화량
                const weightChange = targetWeight - weight;
                
                // 목표 칼로리 계산 (1kg = 7700kcal)
                const totalCalorieChange = weightChange * 7700;
                const dailyCalorieAdjustment = totalCalorieChange / daysToTarget;
                let goalCalories = tdee + dailyCalorieAdjustment;

                // 안전 범위 체크
                const minSafe = bmr * 0.8;
                const maxSafe = tdee * 1.2;
                goalCalories = Math.max(minSafe, Math.min(maxSafe, goalCalories));

                // 결과 표시
                document.getElementById('bmr-value').textContent = Math.round(bmr);
                document.getElementById('tdee-value').textContent = Math.round(tdee);
                document.getElementById('goal-value').textContent = Math.round(goalCalories);
                document.getElementById('period-value').textContent = daysToTarget + '일';

                document.getElementById('calculation-result').style.display = 'block';

            } catch (error) {
                console.error('계산 오류:', error);
                alert('계산 중 오류가 발생했습니다. 입력값을 확인해주세요.');
            }
        }

        // 폼 제출
        document.getElementById('profile-form').addEventListener('submit', async (e) => {
            e.preventDefault();

            const saveBtn = document.getElementById('save-btn');
            const resultDiv = document.getElementById('result');

            saveBtn.disabled = true;
            saveBtn.textContent = '저장 중...';

            try {
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);

                const response = await fetch('/api/profile', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();

                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>✅ 프로필 저장 완료!</h3>
                        <p>설정이 성공적으로 저장되었습니다.</p>
                        <p><strong>일일 목표 칼로리:</strong> ${Math.round(result.data.daily_calorie_goal)} kcal</p>
                        <p>대시보드에서 진행 상황을 확인하세요!</p>
                    `;
                    resultDiv.style.display = 'block';

                    // 3초 후 대시보드로 이동
                    setTimeout(() => {
                        window.location.href = '/dashboard';
                    }, 3000);
                } else {
                    throw new Error(result.error || '저장 실패');
                }

            } catch (error) {
                console.error('저장 오류:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>❌ 저장 실패</h3>
                    <p>${error.message}</p>
                    <p>다시 시도해주세요.</p>
                `;
                resultDiv.style.display = 'block';
            } finally {
                saveBtn.disabled = false;
                saveBtn.textContent = '💾 저장하기';
            }
        });

        // 페이지 로드 시 기존 프로필 불러오기
        async function loadExistingProfile() {
            try {
                const response = await fetch('/api/profile');
                const result = await response.json();

                if (result.success && result.data) {
                    const profile = result.data;
                    
                    // 폼에 데이터 설정
                    Object.keys(profile).forEach(key => {
                        const element = document.getElementById(key);
                        if (element && profile[key] !== null) {
                            element.value = profile[key];
                        }
                    });

                    // 계산 결과 표시
                    calculatePreview();
                }
            } catch (error) {
                console.log('기존 프로필 없음 - 새로 설정합니다.');
            }
        }

        // 페이지 로드 완료 후 실행
        document.addEventListener('DOMContentLoaded', loadExistingProfile);

        // 입력값 변경시 자동 계산
        ['current_weight', 'height', 'age', 'gender', 'target_weight', 'target_date', 'activity_level'].forEach(fieldId => {
            const element = document.getElementById(fieldId);
            if (element) {
                element.addEventListener('change', () => {
                    const form = document.getElementById('profile-form');
                    const formData = new FormData(form);
                    const data = Object.fromEntries(formData);
                    
                    // 모든 필수 필드가 채워져 있으면 자동 계산
                    const requiredFields = ['current_weight', 'height', 'age', 'gender', 'target_weight', 'target_date', 'activity_level'];
                    const allFilled = requiredFields.every(field => data[field]);
                    
                    if (allFilled) {
                        setTimeout(calculatePreview, 100);
                    }
                });
            }
        });
    </script>
</body>
</html>
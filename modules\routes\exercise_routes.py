# -*- coding: utf-8 -*-
"""
Exercise Routes
운동 추천, 운동 기록, 건강 관리 관련 라우트
"""

from flask import Blueprint, render_template, request, jsonify, redirect
from datetime import datetime, date, timedelta
import logging

from modules.config import config
from modules.utils import (
    load_json_file, safe_float_conversion, filter_records_by_date,
    calculate_nutrition_summary
)

logger = logging.getLogger(__name__)

# Blueprint 생성
exercise_bp = Blueprint('exercise', __name__)


@exercise_bp.route('/exercise')
@exercise_bp.route('/exercise_recommendations')
def exercise_recommendations():
    """운동 추천 페이지"""
    try:
        # 사용자 프로필 로드
        profile = load_json_file('user_profile.json')
        if not profile:
            # 프로필이 없으면 설정 페이지로 리다이렉트
            return redirect('/profile?message=프로필을 먼저 설정해주세요')
        
        # 오늘 음식 기록 로드
        today = datetime.now().date()
        records = load_json_file('food_records.json', [])
        today_records = filter_records_by_date(records, today)
        
        # 오늘 총 칼로리 계산
        daily_calories = sum(safe_float_conversion(r.get('calories', 0)) for r in today_records)
        
        # 운동 추천 계산
        exercise_data = get_exercise_recommendations(profile, daily_calories)
        
        # 주간 영양소 분석 (최근 7일)
        recent_records = [r for r in records if 
                         (datetime.now() - datetime.fromisoformat(r['timestamp'])).days <= 7]
        nutrition_analysis = analyze_nutrition(recent_records)
        
        return render_template('exercise_recommendations.html',
                             profile=profile,
                             exercise_data=exercise_data,
                             nutrition_analysis=nutrition_analysis,
                             today_records=today_records,
                             daily_calories=daily_calories)
        
    except Exception as e:
        logger.error(f"운동 추천 페이지 오류: {e}")
        # 오류 발생 시 기본 데이터로 사용
        default_profile = {
            'current_weight': 70,
            'height': 170,
            'age': 25,
            'gender': 'male',
            'activity_level': 'moderate'
        }
        
        default_exercise_data = {
            'success': True,
            'bmr': 1500,
            'tdee': 2000,
            'consumed_calories': 0,
            'excess_calories': 0,
            'recommendations': {
                'message': '오류로 인해 추천을 생성할 수 없습니다.',
                'exercises': {}
            }
        }
        
        default_nutrition = {
            'carb_percent': 50,
            'protein_percent': 20,
            'fat_percent': 30,
            'avg_daily_calories': 0,
            'alerts': ['데이터가 없어 분석할 수 없습니다.']
        }
        
        return render_template('exercise_recommendations.html',
                             profile=default_profile,
                             exercise_data=default_exercise_data,
                             nutrition_analysis=default_nutrition,
                             today_records=[],
                             daily_calories=0)


# === 운동 추천 관련 함수들 ===

def get_exercise_recommendations(profile: dict, daily_calories: float) -> dict:
    """사용자 프로필과 칼로리 섭취량에 기반한 운동 추천"""
    try:
        # 기본 정보 추출
        bmr = profile.get('bmr', 1500)
        tdee = profile.get('tdee', 2000)
        weight = safe_float_conversion(profile.get('current_weight', 70))
        
        # 초과 칼로리 계산
        excess_calories = daily_calories - tdee
        
        # 운동별 칼로리 소모량 (체중 70kg 기준, 1시간)
        exercise_calories_per_hour = {
            '걷기 (빠르게)': 280,
            '조깅': 420,
            '달리기': 600,
            '자전거 타기': 400,
            '수영': 500,
            '등산': 450,
            '계단 오르기': 650,
            '요가': 200,
            '웨이트 트레이닝': 350,
            '배드민턴': 350,
            '농구': 450,
            '축구': 500,
            '테니스': 400,
            '줄넘기': 600,
            '댄스': 300
        }
        
        # 체중에 따른 칼로리 조정 (70kg 기준)
        weight_factor = weight / 70.0
        adjusted_exercise_calories = {
            exercise: int(calories * weight_factor)
            for exercise, calories in exercise_calories_per_hour.items()
        }
        
        # 운동 시간 계산
        recommendations = {}
        
        if excess_calories > 0:
            # 초과 칼로리가 있는 경우
            for exercise, calories_per_hour in adjusted_exercise_calories.items():
                hours_needed = excess_calories / calories_per_hour
                minutes_needed = hours_needed * 60
                
                if minutes_needed < 120:  # 2시간 이내인 경우만 추천
                    recommendations[exercise] = {
                        'time_minutes': int(minutes_needed),
                        'calories_burned': int(excess_calories),
                        'intensity': get_exercise_intensity(exercise),
                        'description': get_exercise_description(exercise)
                    }
        else:
            # 칼로리가 적정하거나 부족한 경우 - 건강 유지 운동 추천
            maintenance_exercises = ['걷기 (빠르게)', '요가', '자전거 타기', '수영']
            for exercise in maintenance_exercises:
                if exercise in adjusted_exercise_calories:
                    recommendations[exercise] = {
                        'time_minutes': 30,  # 30분 기본 추천
                        'calories_burned': int(adjusted_exercise_calories[exercise] / 2),
                        'intensity': get_exercise_intensity(exercise),
                        'description': get_exercise_description(exercise)
                    }
        
        # 추천 메시지 생성
        if excess_calories > 500:
            message = f"오늘 {excess_calories:.0f} kcal를 초과 섭취했습니다. 아래 운동으로 균형을 맞춰보세요!"
        elif excess_calories > 0:
            message = f"오늘 {excess_calories:.0f} kcal를 약간 초과했습니다. 가벼운 운동을 추천합니다."
        else:
            message = "오늘 칼로리 섭취가 적정합니다. 건강 유지를 위한 운동을 해보세요!"
        
        return {
            'success': True,
            'bmr': bmr,
            'tdee': tdee,
            'consumed_calories': daily_calories,
            'excess_calories': max(0, excess_calories),
            'recommendations': {
                'message': message,
                'exercises': recommendations
            },
            'weight': weight
        }
        
    except Exception as e:
        logger.error(f"운동 추천 계산 오류: {e}")
        return {
            'success': False,
            'error': str(e),
            'recommendations': {
                'message': '운동 추천을 계산할 수 없습니다.',
                'exercises': {}
            }
        }


def get_exercise_intensity(exercise: str) -> str:
    """운동 강도 반환"""
    high_intensity = ['달리기', '줄넘기', '계단 오르기', '농구', '축구']
    medium_intensity = ['조깅', '수영', '등산', '배드민턴', '테니스', '웨이트 트레이닝']
    low_intensity = ['걷기 (빠르게)', '요가', '자전거 타기', '댄스']
    
    if exercise in high_intensity:
        return '고강도'
    elif exercise in medium_intensity:
        return '중강도'
    else:
        return '저강도'


def get_exercise_description(exercise: str) -> str:
    """운동 설명 반환"""
    descriptions = {
        '걷기 (빠르게)': '가장 쉽게 시작할 수 있는 유산소 운동',
        '조깅': '적당한 강도의 유산소 운동으로 체력 향상에 좋음',
        '달리기': '고강도 유산소 운동으로 빠른 칼로리 소모',
        '자전거 타기': '관절에 부담이 적은 유산소 운동',
        '수영': '전신 운동으로 근력과 지구력 향상',
        '등산': '자연과 함께하는 전신 운동',
        '계단 오르기': '일상에서 쉽게 할 수 있는 고강도 운동',
        '요가': '유연성과 근력을 동시에 기르는 운동',
        '웨이트 트레이닝': '근력 증가와 기초대사량 향상',
        '배드민턴': '재미있게 할 수 있는 라켓 스포츠',
        '농구': '팀워크와 함께하는 고강도 스포츠',
        '축구': '지구력과 순발력을 기르는 스포츠',
        '테니스': '전신 협응력을 기르는 라켓 스포츠',
        '줄넘기': '짧은 시간에 높은 효과를 얻는 운동',
        '댄스': '음악과 함께 즐겁게 하는 유산소 운동'
    }
    
    return descriptions.get(exercise, '건강에 좋은 운동')


def analyze_nutrition(records: list) -> dict:
    """영양소 분석"""
    try:
        if not records:
            return {
                'carb_percent': 0,
                'protein_percent': 0,
                'fat_percent': 0,
                'avg_daily_calories': 0,
                'alerts': ['분석할 데이터가 없습니다.']
            }
        
        # 총 영양소 계산
        total_calories = sum(safe_float_conversion(r.get('calories', 0)) for r in records)
        total_protein = sum(safe_float_conversion(r.get('protein', 0)) for r in records)
        total_carbs = sum(safe_float_conversion(r.get('carbs', 0)) for r in records)
        total_fat = sum(safe_float_conversion(r.get('fat', 0)) for r in records)
        
        # 일평균 계산 (최근 7일 기준)
        days = min(7, len(set(r['timestamp'][:10] for r in records)))
        avg_daily_calories = total_calories / days if days > 0 else 0
        
        # 칼로리 기준 비율 계산
        protein_calories = total_protein * 4  # 단백질 1g = 4kcal
        carbs_calories = total_carbs * 4      # 탄수화물 1g = 4kcal  
        fat_calories = total_fat * 9          # 지방 1g = 9kcal
        
        total_macro_calories = protein_calories + carbs_calories + fat_calories
        
        if total_macro_calories > 0:
            protein_percent = (protein_calories / total_macro_calories) * 100
            carb_percent = (carbs_calories / total_macro_calories) * 100
            fat_percent = (fat_calories / total_macro_calories) * 100
        else:
            protein_percent = carb_percent = fat_percent = 0
        
        # 영양 균형 알림
        alerts = []
        
        # 권장 비율: 탄수화물 45-65%, 단백질 10-35%, 지방 20-35%
        if carb_percent < 45:
            alerts.append('탄수화물 섭취가 부족합니다. 곡류나 과일을 더 드세요.')
        elif carb_percent > 65:
            alerts.append('탄수화물 섭취가 과도합니다. 단백질과 지방 섭취를 늘려보세요.')
        
        if protein_percent < 10:
            alerts.append('단백질 섭취가 부족합니다. 고기, 생선, 콩류를 더 드세요.')
        elif protein_percent > 35:
            alerts.append('단백질 섭취가 과도합니다.')
        
        if fat_percent < 20:
            alerts.append('지방 섭취가 부족합니다. 견과류나 올리브오일을 드세요.')
        elif fat_percent > 35:
            alerts.append('지방 섭취가 과도합니다. 기름진 음식을 줄여보세요.')
        
        if not alerts:
            alerts.append('영양 균형이 잘 맞춰져 있습니다!')
        
        return {
            'carb_percent': round(carb_percent, 1),
            'protein_percent': round(protein_percent, 1),
            'fat_percent': round(fat_percent, 1),
            'avg_daily_calories': round(avg_daily_calories, 1),
            'total_protein': round(total_protein, 1),
            'total_carbs': round(total_carbs, 1),
            'total_fat': round(total_fat, 1),
            'alerts': alerts
        }
        
    except Exception as e:
        logger.error(f"영양소 분석 오류: {e}")
        return {
            'carb_percent': 0,
            'protein_percent': 0,
            'fat_percent': 0,
            'avg_daily_calories': 0,
            'alerts': ['분석 중 오류가 발생했습니다.']
        }

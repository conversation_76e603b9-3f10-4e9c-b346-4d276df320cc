#!/usr/bin/env python3
"""
환경변수 로딩 문제 해결 스크립트
.env 파일이 제대로 로드되지 않는 문제를 진단하고 해결합니다.
"""

import os
import sys
from pathlib import Path

def check_and_fix_env():
    """환경변수 로딩 문제 확인 및 해결"""
    print("🔧 환경변수 로딩 문제 해결 중...")
    print("=" * 50)
    
    # 1. 현재 디렉토리 확인
    current_dir = Path.cwd()
    print(f"현재 디렉토리: {current_dir}")
    
    # 2. .env 파일 존재 확인
    env_file = current_dir / '.env'
    if not env_file.exists():
        print("❌ .env 파일을 찾을 수 없습니다!")
        return False
    
    print("✅ .env 파일 발견")
    
    # 3. python-dotenv 설치 확인
    try:
        import dotenv
        print("✅ python-dotenv 설치됨")
        
        # 4. .env 파일 로드 테스트
        dotenv.load_dotenv()
        print("✅ .env 파일 로드 성공")
        
    except ImportError:
        print("❌ python-dotenv가 설치되지 않음")
        print("   설치 명령: pip install python-dotenv")
        
        # 수동으로 .env 파일 로드
        print("📝 수동으로 .env 파일 로드 시도...")
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    # 주석 제거
                    if '#' in value:
                        value = value.split('#')[0].strip()
                    os.environ[key.strip()] = value.strip()
        print("✅ 수동 로드 완료")
    
    # 5. Kakao API 키 확인
    kakao_key = os.getenv('KAKAO_REST_API_KEY', 'NOT_SET')
    print(f"\n🔑 Kakao API 키 상태:")
    print(f"   값: {kakao_key}")
    print(f"   길이: {len(kakao_key) if kakao_key != 'NOT_SET' else 0}")
    print(f"   상태: {'✅ 설정완료' if kakao_key != 'NOT_SET' and len(kakao_key) > 10 else '❌ 미설정'}")
    
    # 6. 모든 환경변수 출력 (디버깅용)
    print(f"\n📋 현재 설정된 환경변수:")
    env_vars = [
        'KAKAO_REST_API_KEY',
        'DETECTION_RADIUS', 
        'REQUIRED_STAY_TIME',
        'ANALYZER_API_URL',
        'MFDS_API_KEY'
    ]
    
    for var in env_vars:
        value = os.getenv(var, 'NOT_SET')
        print(f"   {var}: {value}")
    
    return kakao_key != 'NOT_SET' and len(kakao_key) > 10

def test_api_key():
    """API 키로 실제 요청 테스트"""
    kakao_key = os.getenv('KAKAO_REST_API_KEY')
    if not kakao_key or kakao_key == 'NOT_SET':
        print("❌ API 키가 설정되지 않아 테스트를 건너뜁니다.")
        return False
    
    try:
        import requests
        
        # 간단한 카테고리 검색 API 테스트
        url = "https://dapi.kakao.com/v2/local/search/category.json"
        params = {
            'category_group_code': 'FD6',  # 음식점
            'x': '128.748885',  # 대구 경도
            'y': '35.831234',   # 대구 위도
            'radius': '100'
        }
        headers = {
            'Authorization': f'KakaoAK {kakao_key}'
        }
        
        print(f"\n🧪 API 키 테스트 중...")
        response = requests.get(url, params=params, headers=headers, timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            place_count = len(data.get('documents', []))
            print(f"✅ API 키 정상 작동! (검색된 장소: {place_count}개)")
            return True
        else:
            print(f"❌ API 응답 오류: {response.status_code}")
            print(f"   응답: {response.text[:200]}...")
            return False
            
    except ImportError:
        print("❌ requests 모듈이 없어 API 테스트를 건너뜁니다.")
        return False
    except Exception as e:
        print(f"❌ API 테스트 중 오류: {e}")
        return False

if __name__ == "__main__":
    print("🔧 Kakao API 키 문제 해결 스크립트")
    print("=" * 50)
    
    # 환경변수 로딩 확인
    env_ok = check_and_fix_env()
    
    if env_ok:
        # API 키 테스트
        api_ok = test_api_key()
        
        if api_ok:
            print(f"\n✅ 모든 설정이 정상입니다!")
            print(f"이제 app.py를 실행하면 정상 작동할 것입니다.")
        else:
            print(f"\n⚠️ API 키는 설정되었지만 작동하지 않습니다.")
            print(f"카카오 개발자 콘솔에서 다음을 확인하세요:")
            print(f"  1. API 키가 올바른지")
            print(f"  2. 플랫폼 설정에 http://localhost:5000이 등록되었는지")
            print(f"  3. '장소 검색' API가 활성화되었는지")
    else:
        print(f"\n❌ 환경설정에 문제가 있습니다.")
        print(f"위의 문제들을 해결한 후 다시 시도하세요.")
    
    print(f"\n이 스크립트 실행 후 app.py를 재시작하세요!")

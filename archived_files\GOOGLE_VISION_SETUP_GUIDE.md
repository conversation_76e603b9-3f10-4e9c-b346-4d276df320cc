# 📋 Google Cloud Vision API 완전 설정 가이드

## 🎯 목표
영수증 OCR 기능을 사용하기 위해 Google Cloud Vision API를 설정합니다.

---

## 📚 **1단계: Google Cloud Console 계정 준비**

### 1.1 Google Cloud Console 접속
- 브라우저에서 https://console.cloud.google.com/ 접속
- Google 계정으로 로그인 (Gmail 계정 사용 가능)
- 처음 사용하는 경우 이용약관 동의

### 1.2 결제 계정 설정 (중요!)
- 무료 크레딧 $300 받기 (신규 사용자)
- 신용카드 등록 필요 (무료 한도 내에서는 과금 안됨)
- **월 1,000회까지 무료** 사용 가능

---

## 🏗️ **2단계: 프로젝트 생성**

### 2.1 새 프로젝트 만들기
1. **화면 상단 왼쪽**의 **"프로젝트 선택"** 드롭다운 클릭
2. 팝업에서 **"새 프로젝트"** 버튼 클릭
3. **프로젝트 세부정보** 입력:
   ```
   프로젝트 이름: receipt-ocr-app
   프로젝트 ID: receipt-ocr-app-xxxxx (자동 생성)
   위치: 조직 없음 (개인 사용자의 경우)
   ```
4. **"만들기"** 버튼 클릭
5. 생성 완료까지 **1-2분 대기**

### 2.2 프로젝트 선택 확인
- 상단에 생성한 프로젝트명이 표시되는지 확인

---

## 🔌 **3단계: Vision API 활성화**

### 3.1 API 라이브러리로 이동
1. **왼쪽 햄버거 메뉴(≡)** 클릭
2. **"API 및 서비스"** → **"라이브러리"** 클릭

### 3.2 Vision API 찾기 및 활성화
1. 검색창에 **"vision"** 입력
2. **"Cloud Vision API"** 클릭 (가장 첫 번째 결과)
3. **"사용 설정"** 버튼 클릭
4. 활성화 완료까지 **30초-1분 대기**
5. "API가 사용 설정됨" 메시지 확인

---

## 🔐 **4단계: 서비스 계정 생성**

### 4.1 사용자 인증 정보 페이지로 이동
1. **왼쪽 메뉴** → **"API 및 서비스"** → **"사용자 인증 정보"** 클릭

### 4.2 서비스 계정 생성
1. **상단의 "+ 사용자 인증 정보 만들기"** 클릭
2. 드롭다운에서 **"서비스 계정"** 선택

### 4.3 서비스 계정 세부정보 입력
```
서비스 계정 이름: receipt-ocr-service
서비스 계정 ID: receipt-ocr-service (자동 입력됨)
서비스 계정 설명: 영수증 OCR 처리용 서비스 계정
```
3. **"만들기 및 계속하기"** 클릭

### 4.4 역할 부여
1. **"역할 선택"** 드롭다운 클릭
2. 검색창에 **"Vision"** 입력
3. **"Cloud Vision API 사용자"** 선택
4. **"계속"** 클릭
5. **사용자 액세스 권한 부여** 섹션은 건너뛰기
6. **"완료"** 클릭

---

## 📁 **5단계: JSON 키 파일 다운로드**

### 5.1 서비스 계정 상세 페이지로 이동
1. **생성된 서비스 계정** (receipt-ocr-service) 클릭

### 5.2 키 생성 및 다운로드
1. **"키"** 탭 클릭
2. **"키 추가"** 버튼 → **"새 키 만들기"** 선택
3. **키 유형**: **"JSON"** 선택 (기본값)
4. **"만들기"** 클릭
5. **JSON 파일이 자동으로 다운로드됨**
   - 파일명 예시: `receipt-ocr-app-xxxxx-yyyyyy.json`

---

## 🔧 **6단계: 프로젝트에 키 파일 설정**

### 6.1 키 파일 이름 변경 및 이동
1. **다운로드된 JSON 파일**을 찾기 (보통 Downloads 폴더)
2. 파일명을 **`receipt-ocr-key.json`**으로 변경
3. 파일을 **프로젝트 폴더**로 복사:
   ```
   C:\2025_Project\ai-project-gps\receipt-ocr-key.json
   ```

### 6.2 폴더 구조 확인
```
C:\2025_Project\ai-project-gps\
├── app.py
├── receipt-ocr-key.json  ← 이 파일이 있어야 함!
├── templates\
│   └── receipt_upload.html
└── requirements.txt
```

---

## 🧪 **7단계: 설정 테스트**

### 7.1 자동 설정 스크립트 실행
```cmd
cd C:\2025_Project\ai-project-gps
setup_google_vision.bat
```

### 7.2 수동 테스트 (선택사항)
```cmd
cd C:\2025_Project\ai-project-gps
pip install google-cloud-vision
python test_google_vision.py
```

### 7.3 웹사이트에서 테스트
1. 서버 실행: `python app.py`
2. 브라우저에서 http://localhost:5000/receipt 접속
3. 영수증 사진 업로드하여 테스트

---

## 💰 **요금 및 할당량 정보**

### 무료 할당량 (매월)
- **텍스트 감지**: 1,000회/월 무료
- **일반적인 개인 사용**: 무료 범위 내에서 충분

### 유료 요금 (무료 할당량 초과 시)
- **텍스트 감지**: $1.50 / 1,000회
- **예상 비용**: 월 100회 사용 시 무료

### 사용량 모니터링
1. Google Cloud Console → **"결제"** → **"보고서"**
2. Vision API 사용량 확인 가능

---

## 🛡️ **보안 및 주의사항**

### 중요한 보안 수칙
1. **JSON 키 파일을 절대 공개하지 마세요**
2. **GitHub에 업로드하지 마세요**
3. **다른 사람과 공유하지 마세요**
4. **불필요 시 키를 삭제하세요**

### .gitignore에 추가
```gitignore
# Google Cloud 키 파일
receipt-ocr-key.json
*.json
!package.json
```

---

## 🔍 **문제 해결**

### 자주 발생하는 오류

#### 1. `403 Forbidden` 오류
**원인**: API가 활성화되지 않음 또는 권한 부족
**해결책**: 
- Vision API 활성화 확인
- 서비스 계정에 올바른 역할 부여 확인

#### 2. `401 Unauthorized` 오류
**원인**: 키 파일 경로 또는 내용 문제
**해결책**:
- 키 파일 경로 확인: `receipt-ocr-key.json`
- 키 파일 내용이 올바른 JSON 형식인지 확인

#### 3. `429 Too Many Requests` 오류
**원인**: 할당량 초과
**해결책**:
- Google Cloud Console에서 할당량 확인
- 사용량 대기 또는 유료 플랜 고려

#### 4. 파일을 찾을 수 없음
**원인**: 키 파일 경로 문제
**해결책**:
```python
# 올바른 파일 경로 확인
import os
print(os.path.exists('receipt-ocr-key.json'))  # True여야 함
```

#### 5. 한글 텍스트 인식 불가
**원인**: 이미지 품질 또는 폰트 문제
**해결책**:
- 고화질 이미지 사용
- 조명이 좋은 환경에서 촬영
- 텍스트가 명확하게 보이는 각도로 촬영

---

## 📞 **추가 도움이 필요한 경우**

### 공식 문서
- [Google Cloud Vision API 문서](https://cloud.google.com/vision/docs)
- [Python 클라이언트 라이브러리](https://cloud.google.com/vision/docs/libraries)

### 커뮤니티 지원
- [Stack Overflow - google-cloud-vision 태그](https://stackoverflow.com/questions/tagged/google-cloud-vision)
- [Google Cloud 커뮤니티](https://cloud.google.com/community)

---

## ✅ **완료 체크리스트**

- [ ] Google Cloud Console 계정 생성
- [ ] 결제 계정 설정 (신용카드 등록)
- [ ] 새 프로젝트 생성
- [ ] Vision API 활성화
- [ ] 서비스 계정 생성
- [ ] JSON 키 파일 다운로드
- [ ] 키 파일을 프로젝트 폴더에 배치
- [ ] 라이브러리 설치 (`pip install google-cloud-vision`)
- [ ] 설정 테스트 통과
- [ ] 웹사이트에서 영수증 업로드 테스트 성공

모든 체크리스트를 완료하면 영수증 OCR 기능을 사용할 수 있습니다! 🎉

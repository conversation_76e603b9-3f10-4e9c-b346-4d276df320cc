# -*- coding: utf-8 -*-
"""
Korean Food Nutrition API Integration
식품의약품안전처 식품영양성분 데이터베이스 API 연동
"""

import requests
import json
import logging
from typing import Dict, List, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class KoreanFoodNutritionAPI:
    """식품의약품안전처 식품영양성분 API 연동 클래스"""
    
    def __init__(self, api_key: str = None):
        # 공공데이터포털 API 설정
        self.base_url = "https://apis.data.go.kr/1471000/FoodNtrCpntDbInfo02"
        self.api_key = api_key or "etI0mN6xN9OPH9gYuyZEspVti%2Bm8UwO4uOrr%2Bdscc%2BnL1gc47dqdn3vyA7kvADLuURaRKu4OVikQ4xmmFRnfUQ%3D%3D"  # URL 인코딩된 키
        
        # 식품안전나라 API 설정 (백업)
        self.backup_url = "https://various.foodsafetykorea.go.kr/nutrient"
        
        # 로컬 캐시 (API 호출 최적화)
        self.nutrition_cache = {}
        
        # 한국 음식 영어-한국어 매핑
        self.food_name_mapping = {
            # 기존 매핑 확장
            'galbi_gui': '갈비구이',
            'galbitang': '갈비탕',
            'galchi_gui': '갈치구이',
            'gamjachae_bokkeum': '감자채볶음',
            'gyeran_jjim': '계란찜',
            'gimbap': '김밥',
            'kimchi_jjigae': '김치찌개',
            'kkaennip_jangajji': '깻잎장아찌',
            'kkomak_jjim': '꼬막찜',
            'doenjang_jjigae': '된장찌개',
            'tteokbokki': '떡볶이',
            'mandu': '만두',
            'mulhoe': '물회',
            'baechu_kimchi': '배추김치',
            'samgyetang': '삼계탕',
            'saeu_twigim': '새우튀김',
            'soseji_bokkeum': '소세지볶음',
            'sujeonggwa': '수정과',
            'sundae': '순대',
            'sikhye': '식혜',
            'yakgwa': '약과',
            'yukhoe': '육회',
            'jajangmyeon': '자장면',
            'jokbal': '족발',
            'jjimdak': '찜닭',
            'kongjaban': '콩자반',
            'pizza': '피자',
            'hangwa': '한과',
            'haemul_jjim': '해물찜',
            'fried_chicken': '후라이드치킨'
        }
        
        logger.info("Korean Food Nutrition API initialized")
    
    def get_nutrition_info(self, food_name: str, portion_grams: float = 100.0) -> Optional[Dict]:
        """
        음식 영양정보 조회
        
        Args:
            food_name: 음식명 (한국어 또는 영어)
            portion_grams: 분량 (그램)
            
        Returns:
            영양정보 딕셔너리 또는 None
        """
        try:
            # 영어명을 한국어로 변환
            korean_name = self.food_name_mapping.get(food_name.lower(), food_name)
            
            # 캐시 확인
            cache_key = f"{korean_name}_{portion_grams}"
            if cache_key in self.nutrition_cache:
                logger.info(f"✅ Cache hit for {korean_name}")
                return self.nutrition_cache[cache_key]
            
            # API 호출 시도
            nutrition_data = self._call_nutrition_api(korean_name)
            
            if nutrition_data:
                # 분량에 맞게 영양소 계산
                adjusted_nutrition = self._calculate_portion_nutrition(
                    nutrition_data, portion_grams
                )
                
                # 캐시 저장
                self.nutrition_cache[cache_key] = adjusted_nutrition
                
                logger.info(f"✅ API nutrition data retrieved for {korean_name}")
                return adjusted_nutrition
            
            else:
                # API 실패시 기본값 사용
                logger.warning(f"⚠️ API failed, using default nutrition for {korean_name}")
                return self._get_default_nutrition(korean_name, portion_grams)
                
        except Exception as e:
            logger.error(f"❌ Nutrition API error for {food_name}: {e}")
            return self._get_default_nutrition(food_name, portion_grams)
    
    def _call_nutrition_api(self, food_name: str) -> Optional[Dict]:
        """실제 API 호출"""
        try:
            # 식품의약품안전처 API 호출 (정확한 서비스명과 파라미터 사용)
            params = {
                'serviceKey': self.api_key,  # URL 인코딩된 키
                'desc_kor': food_name,       # 음식명 (한국어)
                'pageNo': '1',
                'numOfRows': '10',
                'type': 'json'
            }
            
            # 정확한 서비스명으로 API 호출
            response = requests.get(
                f"{self.base_url}/getFoodNtrCpntList",  # 정확한 서비스명
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"API Response structure: {list(data.keys())}")
                
                # 다양한 응답 구조에 대응
                items = []
                if 'body' in data:
                    # 기존 구조
                    items = data.get('body', {}).get('items', [])
                elif 'response' in data:
                    # 새로운 API 구조
                    body = data.get('response', {}).get('body', {})
                    items = body.get('items', []) if isinstance(body, dict) else []
                elif isinstance(data, list):
                    # 직접 리스트 형태
                    items = data
                
                if items:
                    # 첫 번째 결과 사용
                    item = items[0]
                    return self._parse_api_response(item)
                else:
                    logger.warning(f"No items found in API response: {data}")
            
            logger.warning(f"API call failed for {food_name}: {response.status_code}")
            return None
            
        except Exception as e:
            logger.error(f"API call error: {e}")
            return None
    
    def _parse_api_response(self, api_item: Dict) -> Dict:
        """API 응답 파싱 (유연한 필드명 처리)"""
        try:
            logger.info(f"Parsing API item: {list(api_item.keys())}")
            
            # 다양한 필드명에 대응 (새로운 API 필드명 우선)
            food_name = (
                api_item.get('DESC_KOR') or         # 새로운 API 필드
                api_item.get('FOOD_NM_KR') or      # 기존 API 필드
                api_item.get('foodName') or ''
            )
            
            calories = (
                api_item.get('NUTR_CONT1') or       # 에너지(kcal) - 새로운 API
                api_item.get('ENERC_KCAL') or       # 기존 API
                api_item.get('calories') or 0
            )
            
            protein = (
                api_item.get('NUTR_CONT2') or       # 단백질(g) - 새로운 API
                api_item.get('PROT_G') or           # 기존 API
                api_item.get('protein') or 0
            )
            
            fat = (
                api_item.get('NUTR_CONT3') or       # 지방(g) - 새로운 API
                api_item.get('FAT_G') or            # 기존 API
                api_item.get('fat') or 0
            )
            
            carbs = (
                api_item.get('NUTR_CONT4') or       # 탄수화물(g) - 새로운 API
                api_item.get('CHOCDF_G') or         # 기존 API
                api_item.get('carbs') or 0
            )
            
            return {
                'food_name': food_name,
                'calories_per_100g': float(calories or 0),
                'protein_per_100g': float(protein or 0),
                'carbs_per_100g': float(carbs or 0),
                'fat_per_100g': float(fat or 0),
                'fiber_per_100g': 0.0,  # 일단 기본값
                'sodium_per_100g': 0.0,  # 일단 기본값
                'source': 'mfds_api',
                'retrieved_at': datetime.now().isoformat(),
                'raw_data': api_item  # 디버깅용
            }
        except (ValueError, TypeError) as e:
            logger.error(f"API response parsing error: {e}")
            logger.error(f"Raw API item: {api_item}")
            return None
    
    def _calculate_portion_nutrition(self, nutrition_data: Dict, portion_grams: float) -> Dict:
        """분량에 맞게 영양소 계산"""
        multiplier = portion_grams / 100.0
        
        return {
            'calories': round(nutrition_data['calories_per_100g'] * multiplier, 1),
            'protein': round(nutrition_data['protein_per_100g'] * multiplier, 1),
            'carbs': round(nutrition_data['carbs_per_100g'] * multiplier, 1),
            'fat': round(nutrition_data['fat_per_100g'] * multiplier, 1),
            'fiber': round(nutrition_data.get('fiber_per_100g', 0) * multiplier, 1),
            'sodium': round(nutrition_data.get('sodium_per_100g', 0) * multiplier, 1),
            'weight_grams': portion_grams,
            'source': nutrition_data.get('source', 'api'),
            'food_name': nutrition_data.get('food_name', ''),
            'retrieved_at': nutrition_data.get('retrieved_at')
        }
    
    def _get_default_nutrition(self, food_name: str, portion_grams: float = 100.0) -> Dict:
        """API 실패시 사용할 기본 영양정보"""
        
        # 확장된 기본 영양정보 데이터베이스
        default_nutrition_db = {
            '갈비구이': {'calories': 300, 'protein': 20.0, 'carbs': 8.0, 'fat': 22.0},
            '갈비탕': {'calories': 120, 'protein': 12.0, 'carbs': 6.0, 'fat': 5.0},
            '김치찌개': {'calories': 85, 'protein': 6.0, 'carbs': 4.0, 'fat': 5.0},
            '된장찌개': {'calories': 70, 'protein': 5.0, 'carbs': 6.0, 'fat': 3.0},
            '삼계탕': {'calories': 200, 'protein': 18.0, 'carbs': 8.0, 'fat': 10.0},
            '떡볶이': {'calories': 120, 'protein': 2.5, 'carbs': 25.0, 'fat': 1.5},
            '김밥': {'calories': 150, 'protein': 6.0, 'carbs': 22.0, 'fat': 4.0},
            '자장면': {'calories': 180, 'protein': 8.0, 'carbs': 32.0, 'fat': 3.0},
            '후라이드치킨': {'calories': 250, 'protein': 20.0, 'carbs': 8.0, 'fat': 15.0},
            '피자': {'calories': 266, 'protein': 11.0, 'carbs': 33.0, 'fat': 10.0},
            '육회': {'calories': 140, 'protein': 22.0, 'carbs': 2.0, 'fat': 5.0},
            '갈치구이': {'calories': 180, 'protein': 18.0, 'carbs': 3.0, 'fat': 11.0},
            '감자채볶음': {'calories': 95, 'protein': 2.0, 'carbs': 18.0, 'fat': 2.5},
            '계란찜': {'calories': 110, 'protein': 8.0, 'carbs': 2.0, 'fat': 8.0},
            '깻잎장아찌': {'calories': 15, 'protein': 1.0, 'carbs': 2.0, 'fat': 0.5},
            '꼬막찜': {'calories': 75, 'protein': 12.0, 'carbs': 3.0, 'fat': 1.5},
            '만두': {'calories': 220, 'protein': 8.0, 'carbs': 28.0, 'fat': 9.0},
            '물회': {'calories': 90, 'protein': 15.0, 'carbs': 6.0, 'fat': 1.0},
            '배추김치': {'calories': 18, 'protein': 1.5, 'carbs': 3.0, 'fat': 0.2},
            '새우튀김': {'calories': 180, 'protein': 12.0, 'carbs': 12.0, 'fat': 10.0},
            '소세지볶음': {'calories': 280, 'protein': 15.0, 'carbs': 8.0, 'fat': 22.0},
            '수정과': {'calories': 45, 'protein': 0.1, 'carbs': 11.0, 'fat': 0.1},
            '순대': {'calories': 160, 'protein': 7.0, 'carbs': 15.0, 'fat': 8.0},
            '식혜': {'calories': 60, 'protein': 1.0, 'carbs': 14.0, 'fat': 0.2},
            '약과': {'calories': 350, 'protein': 4.0, 'carbs': 45.0, 'fat': 18.0},
            '족발': {'calories': 320, 'protein': 25.0, 'carbs': 5.0, 'fat': 22.0},
            '찜닭': {'calories': 180, 'protein': 20.0, 'carbs': 12.0, 'fat': 6.0},
            '콩자반': {'calories': 140, 'protein': 12.0, 'carbs': 8.0, 'fat': 6.0},
            '한과': {'calories': 320, 'protein': 3.0, 'carbs': 55.0, 'fat': 12.0},
            '해물찜': {'calories': 160, 'protein': 18.0, 'carbs': 8.0, 'fat': 6.0}
        }
        
        # 한국어 음식명으로 변환
        korean_name = self.food_name_mapping.get(food_name.lower(), food_name)
        
        # 기본 영양정보 조회
        base_nutrition = default_nutrition_db.get(korean_name, {
            'calories': 150, 'protein': 8.0, 'carbs': 20.0, 'fat': 5.0
        })
        
        # 분량에 맞게 계산
        multiplier = portion_grams / 100.0
        
        return {
            'calories': round(base_nutrition['calories'] * multiplier, 1),
            'protein': round(base_nutrition['protein'] * multiplier, 1),
            'carbs': round(base_nutrition['carbs'] * multiplier, 1),
            'fat': round(base_nutrition['fat'] * multiplier, 1),
            'fiber': 0.0,  # 기본값에는 섬유질 정보 없음
            'sodium': 0.0,  # 기본값에는 나트륨 정보 없음
            'weight_grams': portion_grams,
            'source': 'default_db',
            'food_name': korean_name,
            'retrieved_at': datetime.now().isoformat()
        }
    
    def search_food_by_name(self, search_term: str, limit: int = 10) -> List[Dict]:
        """음식명으로 검색 (자동완성용)"""
        try:
            params = {
                'serviceKey': self.api_key,
                'desc_kor': search_term,  # 정확한 파라미터명
                'pageNo': '1',
                'numOfRows': str(limit),
                'type': 'json'
            }
            
            response = requests.get(
                f"{self.base_url}/getFoodNtrCpntList",  # 정확한 서비스명
                params=params,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"Search API Response structure: {list(data.keys())}")
                
                # 다양한 응답 구조에 대응
                items = []
                if 'body' in data:
                    items = data.get('body', {}).get('items', [])
                elif 'response' in data:
                    body = data.get('response', {}).get('body', {})
                    items = body.get('items', []) if isinstance(body, dict) else []
                elif isinstance(data, list):
                    items = data
                
                results = []
                for item in items:
                    # 유연한 필드명 처리
                    food_name = (
                        item.get('DESC_KOR') or 
                        item.get('FOOD_NM_KR') or 
                        item.get('foodName') or ''
                    )
                    
                    calories = (
                        item.get('NUTR_CONT1') or 
                        item.get('ENERC_KCAL') or 
                        item.get('calories') or 0
                    )
                    
                    results.append({
                        'food_name': food_name,
                        'food_code': item.get('FOOD_CD', ''),
                        'calories_per_100g': float(calories or 0)
                    })
                
                return results
            
            return []
            
        except Exception as e:
            logger.error(f"Food search error: {e}")
            return []
    
    def get_api_status(self) -> Dict:
        """API 상태 확인"""
        try:
            # 테스트 호출
            test_result = self._call_nutrition_api('김치')
            
            return {
                'api_available': test_result is not None,
                'api_key_configured': self.api_key != "YOUR_API_KEY_HERE",
                'cache_size': len(self.nutrition_cache),
                'last_checked': datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                'api_available': False,
                'api_key_configured': False,
                'cache_size': len(self.nutrition_cache),
                'error': str(e),
                'last_checked': datetime.now().isoformat()
            }

# 전역 API 인스턴스
nutrition_api = KoreanFoodNutritionAPI()

# API 키 설정 함수
def set_api_key(api_key: str):
    """API 키 설정"""
    global nutrition_api
    nutrition_api.api_key = api_key
    logger.info("🔑 API key configured")

# 편의 함수들
def get_food_nutrition(food_name: str, portion_grams: float = 100.0) -> Dict:
    """음식 영양정보 조회 (편의 함수)"""
    return nutrition_api.get_nutrition_info(food_name, portion_grams)

def search_foods(search_term: str, limit: int = 10) -> List[Dict]:
    """음식 검색 (편의 함수)"""
    return nutrition_api.search_food_by_name(search_term, limit)

def check_api_status() -> Dict:
    """API 상태 확인 (편의 함수)"""
    return nutrition_api.get_api_status()

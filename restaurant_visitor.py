import time
from geopy.distance import geodesic
from plyer import gps
import json
import os
from datetime import datetime
import logging

# 로깅 설정
logger = logging.getLogger(__name__)

class RestaurantVisitor:
    """Enhanced Restaurant Visitor with configurable settings and improved logging"""
    
    def __init__(self, detection_radius=10.0, required_stay_time=5):
        """
        초기화
        Args:
            detection_radius (float): 감지 반경 (미터)
            required_stay_time (int): 필요한 체류 시간 (초)
        """
        self.restaurants = {}
        self.visit_times = {}
        self.notification_sent = set()
        
        # 설정 가능한 값들
        self.detection_radius = detection_radius
        self.required_stay_time = required_stay_time
        
        logger.info(f"🎯 RestaurantVisitor 초기화: 반경 {self.detection_radius}m, 체류시간 {self.required_stay_time}초")

    def geodesic(self, point1, point2):
        """두 지점 간 거리 계산 (사용자와 음식점)"""
        return geodesic(point1, point2)

    def update_settings(self, detection_radius=None, required_stay_time=None):
        """설정값 업데이트"""
        if detection_radius is not None:
            old_radius = self.detection_radius
            self.detection_radius = detection_radius
            logger.info(f"🎯 감지 반경 변경: {old_radius}m → {self.detection_radius}m")
        
        if required_stay_time is not None:
            old_time = self.required_stay_time
            self.required_stay_time = required_stay_time
            logger.info(f"⏱️ 체류 시간 변경: {old_time}초 → {self.required_stay_time}초")

    def check_restaurant_visit(self, current_location, current_time=None):
        """
        음식점 방문 체크 (서버용)
        Args:
            current_location (tuple): (위도, 경도)
            current_time (float): 현재 시간 (없으면 자동 생성)
        
        Returns:
            dict: 상태 변경 정보
        """
        if current_time is None:
            current_time = time.time()
        
        status_changes = {
            'entered': [],
            'left': [],
            'notifications': []
        }
        
        for restaurant_name, restaurant_location in self.restaurants.items():
            distance = self.geodesic(current_location, restaurant_location).meters
            
            logger.debug(f"📏 {restaurant_name}: {distance:.2f}m (반경: {self.detection_radius}m)")
            
            if distance <= self.detection_radius:  # 감지 반경 내
                if restaurant_name not in self.visit_times:
                    # 새로 입장
                    self.visit_times[restaurant_name] = current_time
                    status_changes['entered'].append({
                        'restaurant': restaurant_name,
                        'distance': round(distance, 2),
                        'timestamp': current_time
                    })
                    logger.info(f"🚶‍♂️ {restaurant_name} 입장: {distance:.2f}m")
                    
                elif (current_time - self.visit_times[restaurant_name] >= self.required_stay_time and 
                      restaurant_name not in self.notification_sent):
                    # 충분히 머물렀고 아직 알림을 보내지 않음
                    self.notification_sent.add(restaurant_name)
                    stay_duration = current_time - self.visit_times[restaurant_name]
                    
                    status_changes['notifications'].append({
                        'restaurant': restaurant_name,
                        'distance': round(distance, 2),
                        'stay_duration': round(stay_duration, 1),
                        'timestamp': current_time
                    })
                    logger.info(f"🔔 {restaurant_name} 알림 조건 만족: {stay_duration:.1f}초 체류")
                    
            else:
                # 감지 반경 밖
                if restaurant_name in self.visit_times:
                    stay_duration = current_time - self.visit_times[restaurant_name]
                    del self.visit_times[restaurant_name]
                    
                    status_changes['left'].append({
                        'restaurant': restaurant_name,
                        'distance': round(distance, 2),
                        'stay_duration': round(stay_duration, 1),
                        'timestamp': current_time
                    })
                    logger.info(f"👋 {restaurant_name} 퇴장: {stay_duration:.1f}초 체류")
                
                # 알림 기록 제거
                self.notification_sent.discard(restaurant_name)
        
        return status_changes

    def start_gps(self):
        """GPS 모듈 시작 및 위치 콜백 등록 (모바일 앱용)"""
        try:
            gps.configure(on_location=self.on_location)
            gps.start(minTime=1000, minDistance=1)
            logger.info("🛰️ GPS 모니터링 시작")
        except Exception as e:
            logger.error(f"❌ GPS 시작 실패: {e}")

    def on_location(self, **kwargs):
        """GPS 위치 업데이트 시 호출되는 콜백 함수 (모바일 앱용)"""
        try:
            current_location = (kwargs.get('lat'), kwargs.get('lon'))
            current_time = time.time()
            
            if None in current_location:
                logger.warning("⚠️ 유효하지 않은 GPS 좌표")
                return
            
            logger.debug(f"📍 GPS 위치 업데이트: {current_location[0]:.6f}, {current_location[1]:.6f}")
            
            # 음식점 방문 체크
            status_changes = self.check_restaurant_visit(current_location, current_time)
            
            # 여기서 알림이나 다른 액션을 처리할 수 있음
            for notification in status_changes['notifications']:
                self._handle_notification(notification)
                
        except Exception as e:
            logger.error(f"❌ 위치 처리 오류: {e}")

    def _handle_notification(self, notification_data):
        """알림 처리 (모바일 앱용)"""
        restaurant = notification_data['restaurant']
        stay_duration = notification_data['stay_duration']
        
        print(f"🔔 알림: {restaurant}에 {stay_duration}초간 머물고 있습니다. 음식을 등록하시겠습니까?")
        # 여기서 실제 알림 로직을 구현할 수 있음

    def stop_gps(self):
        """GPS 모듈 정지"""
        try:
            gps.stop()
            logger.info("🛰️ GPS 모니터링 정지")
        except Exception as e:
            logger.error(f"❌ GPS 정지 실패: {e}")

    def get_current_status(self):
        """현재 상태 반환"""
        current_time = time.time()
        status = {
            'visiting_restaurants': [],
            'notifications_sent': list(self.notification_sent),
            'total_restaurants': len(self.restaurants)
        }
        
        for restaurant_name, visit_time in self.visit_times.items():
            stay_duration = current_time - visit_time
            status['visiting_restaurants'].append({
                'name': restaurant_name,
                'stay_duration': round(stay_duration, 1),
                'notification_ready': stay_duration >= self.required_stay_time
            })
        
        return status

    def reset_notifications(self):
        """알림 상태 초기화"""
        old_count = len(self.notification_sent)
        self.notification_sent.clear()
        logger.info(f"🔄 알림 상태 초기화: {old_count}개 제거")

    def load_restaurants_from_file(self, filename='restaurants.json'):
        """파일에서 음식점 데이터 로드"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                restaurant_list = json.load(f)
            
            self.restaurants = {r['name']: (r['lat'], r['lon']) for r in restaurant_list}
            logger.info(f"✅ 음식점 데이터 로드 완료: {len(self.restaurants)}개")
            return True
            
        except FileNotFoundError:
            logger.error(f"❌ 파일을 찾을 수 없습니다: {filename}")
            return False
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON 파싱 오류: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 예상치 못한 오류: {e}")
            return False

if __name__ == "__main__":
    # 테스트용 실행
    visitor = RestaurantVisitor(detection_radius=10.0, required_stay_time=3)
    
    # 음식점 데이터 로드
    if visitor.load_restaurants_from_file():
        print(f"📍 로드된 음식점: {len(visitor.restaurants)}개")
        for name in list(visitor.restaurants.keys())[:5]:  # 처음 5개만 표시
            print(f"   - {name}")
        if len(visitor.restaurants) > 5:
            print(f"   ... 외 {len(visitor.restaurants) - 5}개")
    
    try:
        print("🛰️ GPS 모니터링을 시작합니다...")
        print("🎯 설정:")
        print(f"   - 감지 반경: {visitor.detection_radius}m")
        print(f"   - 체류 시간: {visitor.required_stay_time}초")
        print("📱 Ctrl+C로 종료하세요.")
        
        visitor.start_gps()
        
        while True:
            time.sleep(1)
            # 주기적으로 상태 출력 (10초마다)
            if int(time.time()) % 10 == 0:
                status = visitor.get_current_status()
                if status['visiting_restaurants']:
                    print(f"📍 현재 방문 중: {len(status['visiting_restaurants'])}곳")
                    for restaurant in status['visiting_restaurants']:
                        print(f"   - {restaurant['name']}: {restaurant['stay_duration']}초")
                        
    except KeyboardInterrupt:
        visitor.stop_gps()
        print("\n🔚 프로그램을 종료합니다.")

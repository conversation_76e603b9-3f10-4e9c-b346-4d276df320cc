# 만약 Decoding 키가 안되면 이 파일을 실행하세요
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from nutrition_api import set_api_key, check_api_status

# Encoding된 키로 변경
ENCODING_KEY = "etI0mN6xN9OPH9gYuyZEspVti%2Bm8UwO4uOrr%2Bdscc%2BnL1gc47dqdn3vyA7kvADLuURaRKu4OVikQ4xmmFRnfUQ%3D%3D"

print("🔑 Encoding된 키로 변경중...")
set_api_key(ENCODING_KEY)

print("📊 API 상태 확인...")
status = check_api_status()
print(f"결과: {status}")

if status['api_available']:
    print("✅ Encoding 키가 작동합니다!")
else:
    print("❌ Encoding 키도 안됩니다. API 문서를 다시 확인해주세요.")

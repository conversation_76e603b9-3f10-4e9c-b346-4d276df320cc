# -*- coding: utf-8 -*-
"""
File Handling Utilities
파일 업로드, 저장, 검증 관련 유틸리티 함수들
"""

import os
import base64
from datetime import datetime
from pathlib import Path
from werkzeug.utils import secure_filename
from typing import Optional, Union
import logging

logger = logging.getLogger(__name__)


def allowed_file(filename: str, allowed_extensions: set) -> bool:
    """허용된 파일 확장자 확인"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions


def generate_unique_filename(original_filename: str, prefix: str = "") -> str:
    """고유한 파일명 생성"""
    filename = secure_filename(original_filename)
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_%f')[:-3]  # 밀리초까지
    
    if prefix:
        return f"{prefix}_{timestamp}_{filename}"
    else:
        return f"{timestamp}_{filename}"


def save_uploaded_file(file, upload_folder: str, prefix: str = "") -> Optional[str]:
    """업로드된 파일을 안전하게 저장"""
    try:
        # 업로드 폴더 생성
        Path(upload_folder).mkdir(parents=True, exist_ok=True)
        
        # 파일명 생성
        filename = generate_unique_filename(file.filename, prefix)
        file_path = os.path.join(upload_folder, filename)
        
        # 파일 저장
        file.save(file_path)
        
        logger.info(f"파일 저장 성공: {file_path}")
        return file_path
        
    except Exception as e:
        logger.error(f"파일 저장 실패: {e}")
        return None


def encode_image_to_base64(image_path: str) -> Optional[str]:
    """이미지 파일을 base64로 인코딩"""
    try:
        with open(image_path, 'rb') as image_file:
            image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
            return f"data:image/jpeg;base64,{image_base64}"
    except Exception as e:
        logger.error(f"이미지 base64 인코딩 실패: {e}")
        return None


def get_file_info(file_path: str) -> dict:
    """파일 정보 반환"""
    try:
        file_path = Path(file_path)
        if not file_path.exists():
            return {'error': 'File not found'}
        
        stat = file_path.stat()
        return {
            'path': str(file_path),
            'name': file_path.name,
            'size': stat.st_size,
            'size_mb': round(stat.st_size / (1024 * 1024), 2),
            'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
            'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
            'extension': file_path.suffix.lower()
        }
    except Exception as e:
        logger.error(f"파일 정보 가져오기 실패: {e}")
        return {'error': str(e)}


def cleanup_old_files(directory: str, days: int = 7) -> int:
    """오래된 파일들 정리 (기본: 7일)"""
    try:
        directory = Path(directory)
        if not directory.exists():
            return 0
        
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        deleted_count = 0
        
        for file_path in directory.iterdir():
            if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                try:
                    file_path.unlink()
                    deleted_count += 1
                    logger.debug(f"삭제된 파일: {file_path}")
                except Exception as e:
                    logger.warning(f"파일 삭제 실패: {file_path}, 오류: {e}")
        
        logger.info(f"오래된 파일 정리 완료: {deleted_count}개 삭제")
        return deleted_count
        
    except Exception as e:
        logger.error(f"파일 정리 중 오류: {e}")
        return 0


def validate_file_size(file_path: str, max_size_mb: int = 10) -> bool:
    """파일 크기 검증"""
    try:
        file_size = Path(file_path).stat().st_size
        max_size_bytes = max_size_mb * 1024 * 1024
        return file_size <= max_size_bytes
    except Exception:
        return False


def get_image_dimensions(image_path: str) -> Optional[tuple]:
    """이미지 크기 반환 (PIL 필요)"""
    try:
        from PIL import Image
        with Image.open(image_path) as img:
            return img.size  # (width, height)
    except ImportError:
        logger.warning("PIL이 설치되지 않아 이미지 크기를 확인할 수 없습니다")
        return None
    except Exception as e:
        logger.error(f"이미지 크기 확인 실패: {e}")
        return None

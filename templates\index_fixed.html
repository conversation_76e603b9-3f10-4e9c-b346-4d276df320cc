        // === Enhanced Notification System ===
        
        function showNotification(data) {
            currentRestaurant = data.restaurant;
            
            const overlay = document.getElementById('notification-overlay');
            const notification = document.getElementById('notification');
            
            // 브라우저 기본 알람 제거 - 사용자 정의 알림창만 표시
            let autoRedirectHtml = '';
            autoRedirectHtml = `
                <div class="auto-redirect">
                    <span class="countdown" id="countdown">10</span>초 후 자동으로 음식 등록 페이지로 이동합니다
                </div>
            `;
            
            notification.innerHTML = `
                <h3>🍽️ 음식 등록 알림</h3>
                <p>${data.message}</p>
                <div class="notification-info">
                    📏 거리: ${data.distance}m | ⏱️ 체류: ${data.stay_duration}초
                </div>
                <div class="notification-buttons">
                    <button id="register-btn">📸 지금 등록하기</button>
                    <button id="cancel-btn">❌ 취소</button>
                </div>
                ${autoRedirectHtml}
            `;
            
            overlay.style.display = 'block';
            notification.style.display = 'block';

            // 버튼 이벤트 리스너
            document.getElementById('register-btn').onclick = function() {
                clearAutoRedirectTimer();
                hideNotification();
                window.location.href = data.upload_url;
            };

            document.getElementById('cancel-btn').onclick = function() {
                clearAutoRedirectTimer();
                hideNotification();
            };

            // 배경 클릭시 닫기
            overlay.onclick = function() {
                clearAutoRedirectTimer();
                hideNotification();
            };

            // 자동 리디렉션 타이머 (10초)
            startAutoRedirectTimer(data.upload_url, 10);

            // 브라우저 알림
            if (window.Notification && Notification.permission === "granted") {
                new Notification('🍽️ 음식 등록 알림', {
                    body: data.message,
                    icon: 'https://cdn-icons-png.flaticon.com/512/3514/3514491.png'
                });
            }

            addStatusMessage(`🔔 알림 표시: ${data.restaurant}`, 'notification');
        }
# 🚀 위치 추적 및 자동 알림 기능 - 설치 및 실행 가이드

## 📋 개선 사항 요약

### ✅ 해결된 문제들
1. **위치 추적 정확도 향상**
   - 감지 반경: 7m → 10m로 확대
   - 체류 시간: 10초 → 5초로 단축
   - 실시간 거리 계산 및 로깅

2. **자동 알림 및 리디렉션**
   - 알림 후 3초 내 자동으로 음식 등록 페이지 이동
   - 카운트다운 타이머 표시
   - 사용자가 직접 선택할 수 있는 옵션

3. **디버깅 및 모니터링**
   - 실시간 상태 표시
   - 상세 로그 출력
   - 디버그 모드 토글
   - 테스트 도구 제공

4. **설정 최적화**
   - 환경변수를 통한 유연한 설정
   - 테스트/운영 환경 분리
   - 성능 최적화 옵션

## 🛠️ 설치 방법

### 1단계: 기존 환경 확인
```bash
cd C:\2025_Project\ai-project-gps

# Python 환경 확인
python --version  # Python 3.8+ 필요

# 가상환경 활성화 (이미 설정되어 있다면)
venv\Scripts\activate  # Windows
# source venv/bin/activate  # Linux/Mac
```

### 2단계: 의존성 설치 확인
```bash
# 필요한 패키지 확인
pip list | findstr -i "flask geopy requests socketio"

# 누락된 패키지가 있다면 설치
pip install -r requirements.txt
```

### 3단계: 환경 설정
```bash
# 향상된 설정 파일 복사
copy .env.enhanced .env

# 설정 파일 편집 (메모장 또는 VS Code)
notepad .env
```

**중요 설정값 확인/수정:**
```env
# 빠른 테스트를 위한 설정
DETECTION_RADIUS=10.0
REQUIRED_STAY_TIME=5
AUTO_REDIRECT_ENABLED=true
AUTO_REDIRECT_DELAY=3
DEBUG_MODE=true
```

### 4단계: 테스트 실행
```bash
# 기능 테스트 (선택사항)
python test_location_tracking.py

# 대화형 테스트 (선택사항)
python test_location_tracking.py interactive
```

## 🚀 실행 방법

### 방법 1: 향상된 버전 실행 (권장)
```bash
python app_enhanced.py
```

### 방법 2: 기본 버전 실행
```bash
python app.py
```

### 실행 확인
서버가 시작되면 다음과 같은 메시지가 표시됩니다:
```
🚀 AI Restaurant Visit and Calorie Management System Starting (Enhanced Version)
================================================================================
📍 Enhanced Features:
   - Improved real-time location tracking with detailed logging
   - Automatic food registration page redirect after notification
   - Enhanced debugging and error handling
   - Configurable detection radius and stay time
   - Better status reporting and notifications
================================================================================
🌐 Main Server: http://localhost:5000
🎯 위치 추적 설정:
   - 감지 반경: 10.0m
   - 체류 시간: 5초
   - 업데이트 간격: 1000ms
   - 자동 리디렉션: ✅ 활성화
   - 리디렉션 지연: 3초
================================================================================
🚀 Starting enhanced server...
```

## 🧪 테스트 방법

### 1. 브라우저에서 접속
```
http://localhost:5000
```

### 2. 위치 설정 방법

#### 옵션 1: 드래그 모드 (기본, 권장)
1. "🎯 위치 설정" 버튼 클릭
2. "지도에서 드래그" 선택
3. 지도의 파란 마커를 음식점 근처로 드래그

#### 옵션 2: 직접 입력 모드
1. "🎯 위치 설정" 버튼 클릭
2. "직접 입력" 선택
3. 테스트용 좌표 입력:
   ```
   위도: 35.837030
   경도: 128.751693
   ```
   (동궁찜닭 위치)

#### 옵션 3: 클릭 모드
1. "🎯 위치 설정" 버튼 클릭
2. "지도 클릭" 선택
3. 지도에서 음식점 근처를 클릭

### 3. 알림 테스트
1. 음식점 마커 근처 (10m 이내)로 위치 이동
2. 5초 대기
3. 알림 팝업 확인
4. 자동 리디렉션 카운트다운 확인
5. "📸 지금 등록하기" 버튼 클릭 또는 자동 이동 대기

### 4. 디버그 모드 활용
1. "🔍 디버그 모드" 버튼 클릭
2. "실시간 상태" 패널에서 상세 로그 확인
3. 브라우저 F12 > 콘솔에서 추가 정보 확인

## 📊 모니터링 및 로그

### 실시간 로그 확인
```bash
# 실시간 로그 보기
tail -f app.log  # Linux/Mac
Get-Content app.log -Wait  # PowerShell
```

### 주요 로그 메시지
```
📍 위치 업데이트: 35.830569, 128.753993
🚶‍♂️ 동궁찜닭 입장: 8.50m
🔔 동궁찜닭 알림 조건 만족: 5.2초 체류
📸 음식 등록 페이지 접근: 동궁찜닭
```

## 🔧 문제 해결

### 일반적인 문제들

#### 1. 위치가 감지되지 않음
```bash
# 설정 확인
grep DETECTION_RADIUS .env

# 반경 증가
echo "DETECTION_RADIUS=15.0" >> .env
```

#### 2. 알림이 너무 늦음
```bash
# 체류 시간 단축
echo "REQUIRED_STAY_TIME=3" >> .env
```

#### 3. 자동 리디렉션 안됨
```bash
# 설정 확인 및 수정
echo "AUTO_REDIRECT_ENABLED=true" >> .env
echo "AUTO_REDIRECT_DELAY=2" >> .env
```

#### 4. 서버 연결 오류
```bash
# 포트 사용 확인
netstat -ano | findstr :5000

# 다른 포트로 실행
set PORT=5001 && python app_enhanced.py
```

### 자세한 문제 해결
더 자세한 문제 해결 방법은 `TROUBLESHOOTING.md` 파일을 참조하세요.

## 📱 브라우저 호환성

### 완전 지원
- ✅ Chrome 80+
- ✅ Edge 80+
- ✅ Firefox 75+

### 제한적 지원
- ⚠️ Safari 13+ (일부 기능 제한)
- ⚠️ 모바일 브라우저 (성능 제한)

### 권장 설정
- JavaScript 활성화
- 위치 권한 허용
- 팝업 차단 해제

## 🎯 성능 최적화 팁

### 테스트 환경
```env
DETECTION_RADIUS=8.0
REQUIRED_STAY_TIME=3
LOCATION_UPDATE_INTERVAL=500
FAST_TEST_MODE=true
```

### 운영 환경
```env
DETECTION_RADIUS=7.0
REQUIRED_STAY_TIME=10
LOCATION_UPDATE_INTERVAL=1000
DEBUG_MODE=false
```

### 메모리 사용량 확인
```bash
# Windows
tasklist | findstr python

# Linux/Mac
ps aux | grep python
```

## 📞 지원 및 문의

### 로그 파일 위치
- 메인 로그: `app.log`
- 설정 파일: `.env`
- 테스트 결과: 콘솔 출력

### 유용한 명령어
```bash
# 전체 테스트
python test_location_tracking.py

# 대화형 테스트
python test_location_tracking.py interactive

# 서버 상태 확인
curl http://localhost:5000/api/analyzer_status
```

### 개발자 도구 활용
```javascript
// 브라우저 콘솔에서 실행
console.log('현재 설정:', config);
console.log('현재 위치:', currentPosition);
toggleDebugMode();  // 디버그 모드 토글
```

---

## 🎉 성공적인 설정 확인

모든 것이 올바르게 설정되었다면:

1. **서버 시작**: 콘솔에 설정 정보와 함께 서버 시작 메시지 표시
2. **웹 페이지 로드**: http://localhost:5000에서 지도와 설정 패널 표시
3. **위치 설정**: 드래그나 클릭으로 위치 변경 가능
4. **실시간 추적**: "실시간 상태" 패널에서 위치 업데이트 확인
5. **알림 발생**: 음식점 근처에서 5초 체류시 알림 팝업
6. **자동 이동**: 3초 카운트다운 후 음식 등록 페이지로 자동 이동

모든 기능이 정상 작동한다면 위치 추적 및 자동 알림 시스템이 성공적으로 개선되었습니다! 🎊

# AI Restaurant Visit and Calorie Management System - Required Libraries
# Installation: pip install -r requirements.txt

# Web Framework
Flask>=2.3.0
Flask-SocketIO>=5.3.0

# Environment Variables
python-dotenv>=1.0.0

# Location Calculation
geopy>=2.3.0

# File Upload and Security
Werkzeug>=2.3.0

# HTTP Requests (API Communication)
requests>=2.31.0

# OCR for Receipt Analysis
google-cloud-vision>=3.0.0

# Image Processing
Pillow>=10.0.0

# Notifications (Optional - for mobile)
plyer>=2.1.0

# Basic libraries (usually built-in)
# json, time, datetime, pathlib, logging, base64, io

# IMPORTANT: analyzer-master must also be running!
# analyzer-master folder also needs separate requirements.txt installation

# Installation Steps:
# 1. pip install -r requirements.txt
# 2. Configure .env file with API keys
# 3. Set up Google Cloud Vision API (see GOOGLE_VISION_SETUP_GUIDE.md)

# How to Run:
# 1. In analyzer-master folder: python api_server.py (port 5001)
# 2. In ai-project-gps folder: python app.py (port 5000)

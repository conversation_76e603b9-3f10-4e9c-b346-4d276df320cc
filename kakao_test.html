<!DOCTYPE html>
<html>
<head>
    <title>Kakao Map Local Test</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>Kakao Map Local Test</h1>
    <div id="map" style="width:100%;height:400px;border:1px solid #ccc;"></div>
    
    <div style="margin-top: 20px;">
        <h3>디버그 정보:</h3>
        <div id="debug"></div>
    </div>
    
    <script>
        function log(message) {
            console.log(message);
            document.getElementById('debug').innerHTML += '<p>' + message + '</p>';
        }
        
        log("🚀 로컬 테스트 페이지 시작");
        
        // 1단계: Kakao SDK 동적 로드
        function loadKakaoSDK() {
            log("📦 Kakao SDK 동적 로드 시작");
            
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = 'https://dapi.kakao.com/v2/maps/sdk.js?appkey=b5c3aa43720c70475087f256859be76a&libraries=services';
            
            script.onload = function() {
                log("✅ Kakao SDK 로드 성공");
                log("🗺️ Kakao 객체: " + typeof kakao);
                if (typeof kakao !== 'undefined') {
                    log("🗺️ Kakao.maps 객체: " + typeof kakao.maps);
                    initMap();
                } else {
                    log("❌ Kakao 객체가 여전히 undefined");
                }
            };
            
            script.onerror = function() {
                log("❌ Kakao SDK 로드 실패");
                log("🔧 대안: 다른 CDN 시도");
                loadAlternativeSDK();
            };
            
            document.head.appendChild(script);
        }
        
        // 2단계: 대안 CDN 시도
        function loadAlternativeSDK() {
            log("🔄 대안 SDK 로드 시도");
            
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = '//dapi.kakao.com/v2/maps/sdk.js?appkey=b5c3aa43720c70475087f256859be76a';
            
            script.onload = function() {
                log("✅ 대안 SDK 로드 성공");
                if (typeof kakao !== 'undefined') {
                    initMap();
                } else {
                    log("❌ 대안 SDK도 실패");
                }
            };
            
            script.onerror = function() {
                log("❌ 대안 SDK도 실패");
                log("🚨 네트워크 또는 API 키 문제일 가능성");
            };
            
            document.head.appendChild(script);
        }
        
        // 3단계: 지도 초기화
        function initMap() {
            log("🗺️ 지도 초기화 시작");
            
            try {
                if (typeof kakao === 'undefined' || typeof kakao.maps === 'undefined') {
                    log("❌ Kakao Maps API가 준비되지 않음");
                    return;
                }
                
                kakao.maps.load(function() {
                    log("🎯 Kakao Maps 서비스 로드 완료");
                    
                    const container = document.getElementById('map');
                    const options = {
                        center: new kakao.maps.LatLng(35.830569788, 128.75399385),
                        level: 3
                    };
                    
                    const map = new kakao.maps.Map(container, options);
                    log("✅ 지도 생성 완료!");
                    
                    // 마커 추가 테스트
                    const marker = new kakao.maps.Marker({
                        position: new kakao.maps.LatLng(35.830569788, 128.75399385),
                        map: map
                    });
                    log("📍 마커 추가 완료!");
                });
                
            } catch (error) {
                log("❌ 지도 초기화 오류: " + error.message);
            }
        }
        
        // 페이지 로드 후 시작
        document.addEventListener('DOMContentLoaded', function() {
            log("📄 DOM 로드 완료");
            loadKakaoSDK();
        });
    </script>
</body>
</html>

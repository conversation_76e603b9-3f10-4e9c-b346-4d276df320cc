# -*- coding: utf-8 -*-
"""
Sample Data Generator for AI Restaurant Visit and Calorie Management System
샘플 데이터를 생성하여 대시보드와 기능들을 테스트할 수 있습니다.
"""

import sys
import os
import json
from datetime import datetime, date, timedelta
from food_database import db
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 샘플 음식 데이터 (한국 음식 위주)
SAMPLE_FOODS = [
    {
        'food_name_ko': '김치찌개',
        'food_name_en': 'kimchi_jjigae',
        'restaurant': '맘스터치 경북대점',
        'nutrition': {'calories': 150, 'protein': 8.0, 'carbs': 12.0, 'fat': 8.0, 'weight_grams': 200},
        'confidence': 0.92,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    },
    {
        'food_name_ko': '치킨텐더',
        'food_name_en': 'chicken_tender',
        'restaurant': '맘스터치 경북대점',
        'nutrition': {'calories': 280, 'protein': 22.0, 'carbs': 15.0, 'fat': 16.0, 'weight_grams': 150},
        'confidence': 0.88,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    },
    {
        'food_name_ko': '불고기덮밥',
        'food_name_en': 'bulgogi_rice_bowl',
        'restaurant': '교촌치킨 경북대점',
        'nutrition': {'calories': 520, 'protein': 28.0, 'carbs': 65.0, 'fat': 18.0, 'weight_grams': 350},
        'confidence': 0.85,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    },
    {
        'food_name_ko': '된장찌개',
        'food_name_en': 'doenjang_jjigae',
        'restaurant': '한솥도시락 경북대점',
        'nutrition': {'calories': 120, 'protein': 6.0, 'carbs': 8.0, 'fat': 7.0, 'weight_grams': 180},
        'confidence': 0.90,
        'method': 'enhanced_korean',
        'nutrition_source': 'default_db'
    },
    {
        'food_name_ko': '삼계탕',
        'food_name_en': 'samgyetang',
        'restaurant': '한솥도시락 경북대점',
        'nutrition': {'calories': 400, 'protein': 35.0, 'carbs': 20.0, 'fat': 22.0, 'weight_grams': 500},
        'confidence': 0.93,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    },
    {
        'food_name_ko': '비빔밥',
        'food_name_en': 'bibimbap',
        'restaurant': '서브웨이 경북대점',
        'nutrition': {'calories': 450, 'protein': 18.0, 'carbs': 70.0, 'fat': 12.0, 'weight_grams': 400},
        'confidence': 0.87,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    },
    {
        'food_name_ko': '떡볶이',
        'food_name_en': 'tteokbokki',
        'restaurant': '롯데리아 경북대점',
        'nutrition': {'calories': 320, 'protein': 6.0, 'carbs': 65.0, 'fat': 4.0, 'weight_grams': 250},
        'confidence': 0.91,
        'method': 'enhanced_korean',
        'nutrition_source': 'default_db'
    },
    {
        'food_name_ko': '김밥',
        'food_name_en': 'gimbap',
        'restaurant': '맥도날드 경북대점',
        'nutrition': {'calories': 380, 'protein': 12.0, 'carbs': 55.0, 'fat': 13.0, 'weight_grams': 300},
        'confidence': 0.89,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    },
    {
        'food_name_ko': '자장면',
        'food_name_en': 'jajangmyeon',
        'restaurant': '버거킹 경북대점',
        'nutrition': {'calories': 580, 'protein': 20.0, 'carbs': 85.0, 'fat': 18.0, 'weight_grams': 450},
        'confidence': 0.86,
        'method': 'enhanced_korean',
        'nutrition_source': 'default_db'
    },
    {
        'food_name_ko': '갈비탕',
        'food_name_en': 'galbitang',
        'restaurant': 'KFC 경북대점',
        'nutrition': {'calories': 350, 'protein': 25.0, 'carbs': 15.0, 'fat': 20.0, 'weight_grams': 400},
        'confidence': 0.84,
        'method': 'enhanced_korean',
        'nutrition_source': 'mfds_api'
    }
]

def generate_sample_data(days=7):
    """지난 N일간의 샘플 데이터 생성"""
    
    logger.info(f"🎯 Generating sample data for the last {days} days...")
    
    # 각 날짜별로 1-3개의 음식 기록 생성
    for day_offset in range(days):
        target_date = date.today() - timedelta(days=day_offset)
        
        # 하루에 몇 개의 음식을 먹었는지 (1-3개)
        import random
        meals_per_day = random.randint(1, 3)
        
        for meal_idx in range(meals_per_day):
            # 랜덤하게 음식 선택
            food_data = random.choice(SAMPLE_FOODS).copy()
            
            # 시간 설정 (아침: 8-10시, 점심: 12-14시, 저녁: 18-20시)
            if meal_idx == 0:
                hour = random.randint(8, 10)
            elif meal_idx == 1:
                hour = random.randint(12, 14)
            else:
                hour = random.randint(18, 20)
            
            minute = random.randint(0, 59)
            
            # 분석 결과 생성
            analysis_result = {
                'success': True,
                'data': {
                    'food_name_ko': food_data['food_name_ko'],
                    'food_name_en': food_data['food_name_en'],
                    'confidence': food_data['confidence'],
                    'method': food_data['method'],
                    'nutrition': food_data['nutrition'],
                    'nutrition_source': food_data['nutrition_source'],
                    'estimated_weight': food_data['nutrition']['weight_grams']
                },
                'generated_at': datetime.combine(target_date, datetime.min.time().replace(hour=hour, minute=minute)).isoformat()
            }
            
            # 데이터베이스에 추가
            try:
                record_id = db.add_food_record(
                    restaurant_name=food_data['restaurant'],
                    analysis_result=analysis_result,
                    image_path=f"sample_images/{food_data['food_name_en']}.jpg"
                )
                
                if record_id:
                    logger.info(f"✅ Added: {food_data['food_name_ko']} at {food_data['restaurant']} ({target_date})")
                else:
                    logger.error(f"❌ Failed to add: {food_data['food_name_ko']}")
                    
            except Exception as e:
                logger.error(f"❌ Error adding food record: {e}")

def generate_nutrition_cache():
    """영양정보 캐시 데이터 생성 (API 테스트용)"""
    
    logger.info("🎯 Generating nutrition cache data...")
    
    cache_foods = [
        {'name': '김치찌개', 'weight': 200, 'cal': 150, 'protein': 8.0, 'carbs': 12.0, 'fat': 8.0},
        {'name': '치킨텐더', 'weight': 150, 'cal': 280, 'protein': 22.0, 'carbs': 15.0, 'fat': 16.0},
        {'name': '불고기덮밥', 'weight': 350, 'cal': 520, 'protein': 28.0, 'carbs': 65.0, 'fat': 18.0},
        {'name': '된장찌개', 'weight': 180, 'cal': 120, 'protein': 6.0, 'carbs': 8.0, 'fat': 7.0},
        {'name': '삼계탕', 'weight': 500, 'cal': 400, 'protein': 35.0, 'carbs': 20.0, 'fat': 22.0},
    ]
    
    for food in cache_foods:
        nutrition_data = {
            'calories': food['cal'],
            'protein': food['protein'],
            'carbs': food['carbs'],
            'fat': food['fat'],
            'fiber': 2.0,
            'sodium': 800.0,
            'weight_grams': food['weight'],
            'source': 'mfds_api',
            'food_name': food['name']
        }
        
        try:
            db.cache_nutrition_data(
                food_name=food['name'],
                weight_grams=food['weight'],
                nutrition_data=nutrition_data,
                source='mfds_api',
                expires_hours=48
            )
            logger.info(f"✅ Cached: {food['name']} ({food['weight']}g)")
            
        except Exception as e:
            logger.error(f"❌ Cache error: {e}")

def generate_search_history():
    """검색 기록 생성 (자동완성 테스트용)"""
    
    logger.info("🎯 Generating search history...")
    
    popular_searches = [
        ('김치', '김치찌개'),
        ('치킨', '치킨텐더'),
        ('불고기', '불고기덮밥'),
        ('된장', '된장찌개'),
        ('삼계', '삼계탕'),
        ('비빔', '비빔밥'),
        ('떡볶', '떡볶이'),
        ('김밥', '김밥'),
        ('자장', '자장면'),
        ('갈비', '갈비탕')
    ]
    
    for search_term, selected_food in popular_searches:
        try:
            # 검색 횟수를 랜덤하게 시뮬레이션
            for _ in range(random.randint(1, 5)):
                db.add_search_history(search_term, selected_food)
            
            logger.info(f"✅ Search history: {search_term} -> {selected_food}")
            
        except Exception as e:
            logger.error(f"❌ Search history error: {e}")

def generate_sample_profile():
    """샘플 사용자 프로필 생성"""
    
    logger.info("👤 Generating sample user profile...")
    
    # 계절에 따른 다양한 프로필 예제
    profile_options = [
        {
            'current_weight': 68.5,
            'target_weight': 63.0,
            'height': 170.0,
            'age': 28,
            'gender': 'male',
            'activity_level': 'moderate',
            'target_date': (datetime.now() + timedelta(days=90)).strftime('%Y-%m-%d'),  # 3개월 후
            'daily_calorie_goal': None  # 자동 계산
        },
        {
            'current_weight': 55.2,
            'target_weight': 58.0,
            'height': 162.0,
            'age': 24,
            'gender': 'female',
            'activity_level': 'light',
            'target_date': (datetime.now() + timedelta(days=120)).strftime('%Y-%m-%d'),  # 4개월 후
            'daily_calorie_goal': None
        },
        {
            'current_weight': 72.8,
            'target_weight': 72.8,
            'height': 175.0,
            'age': 32,
            'gender': 'male',
            'activity_level': 'active',
            'target_date': (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d'),  # 1년 후 (유지)
            'daily_calorie_goal': None
        }
    ]
    
    # 랜덤하게 하나 선택
    import random
    selected_profile = random.choice(profile_options)
    
    try:
        from user_profile import profile_manager
        
        # 목표 칼로리 계산
        daily_calorie_goal = profile_manager.calculate_daily_calorie_goal(selected_profile)
        selected_profile['daily_calorie_goal'] = daily_calorie_goal
        
        # 프로필 저장
        success = profile_manager.create_or_update_profile(selected_profile)
        
        if success:
            logger.info(f"✅ Sample profile created: {selected_profile['gender']}, {selected_profile['age']}세, 목표: {selected_profile['target_weight']}kg")
            logger.info(f"   목표 칼로리: {daily_calorie_goal:.0f}kcal/일")
        else:
            logger.error("❌ Failed to create sample profile")
            
    except Exception as e:
        logger.error(f"❌ Sample profile generation error: {e}")

def clear_existing_data():
    """기존 데이터 삭제 (새로 시작하고 싶을 때)"""
    
    logger.warning("⚠️ Clearing existing data...")
    
    with db.get_connection() as conn:
        conn.execute("DELETE FROM food_records")
        conn.execute("DELETE FROM daily_summary")
        conn.execute("DELETE FROM nutrition_cache")
        conn.execute("DELETE FROM food_search_history")
        conn.execute("DELETE FROM user_profile")
        conn.commit()
    
    logger.info("🧹 All existing data cleared")

def show_current_stats():
    """현재 데이터베이스 상태 표시"""
    
    logger.info("📊 Current database statistics:")
    
    with db.get_connection() as conn:
        # Food records count
        food_count = conn.execute("SELECT COUNT(*) FROM food_records").fetchone()[0]
        logger.info(f"   📝 Food records: {food_count}")
        
        # Daily summaries count
        daily_count = conn.execute("SELECT COUNT(*) FROM daily_summary").fetchone()[0]
        logger.info(f"   📅 Daily summaries: {daily_count}")
        
        # Cache entries count
        cache_count = conn.execute("SELECT COUNT(*) FROM nutrition_cache").fetchone()[0]
        logger.info(f"   💾 Cache entries: {cache_count}")
        
        # Search history count
        search_count = conn.execute("SELECT COUNT(*) FROM food_search_history").fetchone()[0]
        logger.info(f"   🔍 Search history: {search_count}")
        
        # Recent foods
        recent = conn.execute("""
            SELECT food_name_ko, restaurant_name, calories, date 
            FROM food_records 
            ORDER BY timestamp DESC 
            LIMIT 3
        """).fetchall()
        
        logger.info("   🍽️ Recent foods:")
        for food in recent:
            logger.info(f"      - {food[0]} at {food[1]} ({food[2]:.0f} kcal, {food[3]})")

def main():
    """메인 실행 함수"""
    
    print("🍽️ AI 칼로리 관리 시스템 - 샘플 데이터 생성기")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'clear':
            clear_existing_data()
            print("✅ 모든 데이터가 삭제되었습니다.")
            return
            
        elif command == 'stats':
            show_current_stats()
            return
            
        elif command.startswith('days='):
            try:
                days = int(command.split('=')[1])
                generate_sample_data(days)
                generate_nutrition_cache()
                generate_search_history()
                generate_sample_profile()
                print(f"✅ {days}일간의 샘플 데이터가 생성되었습니다.")
                show_current_stats()
                return
            except ValueError:
                print("❌ 올바른 일수를 입력하세요. 예: python generate_sample_data.py days=7")
                return
    
    # 기본 실행: 7일간 데이터 생성
    try:
        # 기존 데이터가 있는지 확인
        with db.get_connection() as conn:
            existing_count = conn.execute("SELECT COUNT(*) FROM food_records").fetchone()[0]
        
        if existing_count > 0:
            response = input(f"⚠️ 기존 데이터 {existing_count}개가 있습니다. 삭제하고 새로 생성하시겠습니까? (y/N): ")
            if response.lower() == 'y':
                clear_existing_data()
            else:
                print("기존 데이터에 추가로 생성합니다.")
        
        # 샘플 데이터 생성
        generate_sample_data(7)
        generate_nutrition_cache()
        generate_search_history()
        generate_sample_profile()
        
        print("\n✅ 샘플 데이터 생성 완료!")
        print("🌐 웹 브라우저에서 http://localhost:5000/dashboard 로 접속하여 확인하세요.")
        
        show_current_stats()
        
    except Exception as e:
        logger.error(f"❌ 샘플 데이터 생성 실패: {e}")
        print(f"❌ 오류가 발생했습니다: {e}")

if __name__ == "__main__":
    # 사용법 출력
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help']:
        print("🍽️ AI 칼로리 관리 시스템 - 샘플 데이터 생성기")
        print("\n사용법:")
        print("  python generate_sample_data.py              # 7일간 샘플 데이터 생성")
        print("  python generate_sample_data.py days=14      # 14일간 샘플 데이터 생성")
        print("  python generate_sample_data.py clear        # 모든 데이터 삭제")
        print("  python generate_sample_data.py stats        # 현재 상태 확인")
        print("  python generate_sample_data.py -h           # 도움말")
        sys.exit(0)
    
    main()

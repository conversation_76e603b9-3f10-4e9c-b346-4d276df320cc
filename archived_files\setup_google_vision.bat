@echo off
echo 🚀 Google Cloud Vision API 자동 설정 스크립트
echo ================================================

echo 📦 1단계: 필요한 라이브러리 설치 중...
pip install google-cloud-vision
if %errorlevel% neq 0 (
    echo ❌ 라이브러리 설치 실패
    pause
    exit /b 1
)

echo ✅ google-cloud-vision 설치 완료

echo 📁 2단계: 키 파일 확인 중...
if exist "receipt-ocr-key.json" (
    echo ✅ receipt-ocr-key.json 파일 발견
) else (
    echo ❌ receipt-ocr-key.json 파일이 없습니다!
    echo.
    echo 💡 다음 단계를 수행하세요:
    echo    1. https://console.cloud.google.com/ 접속
    echo    2. 프로젝트 생성 및 Vision API 활성화
    echo    3. 서비스 계정 생성 및 JSON 키 다운로드
    echo    4. 다운로드한 파일을 'receipt-ocr-key.json'으로 이름 변경
    echo    5. 이 폴더에 복사
    echo.
    echo 📋 자세한 가이드: setup_google_vision.md 파일 참조
    pause
    exit /b 1
)

echo 🔍 3단계: 설정 테스트 중...
python test_google_vision.py

echo.
echo 🎉 설정 스크립트 완료!
echo 📱 이제 http://localhost:5000/receipt 에서 영수증 분석을 사용할 수 있습니다!
echo.
pause

# -*- coding: utf-8 -*-
"""
User Profile and Target Weight Management Module (for future expansion)
Currently only basic framework, to be fully developed in Phase 3
"""

import sqlite3
from datetime import datetime, date
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class UserProfile:
    """User profile and goal management"""
    
    def __init__(self, db_path="food_data.db"):
        self.db_path = Path(db_path)
    
    def get_connection(self):
        """Database connection"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def create_or_update_profile(self, profile_data):
        """Create/update user profile"""
        try:
            with self.get_connection() as conn:
                # Check existing profile
                existing = conn.execute("SELECT id FROM user_profile LIMIT 1").fetchone()
                
                if existing:
                    # Update
                    conn.execute("""
                        UPDATE user_profile SET
                            current_weight = ?,
                            target_weight = ?,
                            height = ?,
                            age = ?,
                            gender = ?,
                            activity_level = ?,
                            target_date = ?,
                            daily_calorie_goal = ?,
                            updated_at = CURRENT_TIMESTAMP
                        WHERE id = ?
                    """, (
                        profile_data.get('current_weight'),
                        profile_data.get('target_weight'),
                        profile_data.get('height'),
                        profile_data.get('age'),
                        profile_data.get('gender'),
                        profile_data.get('activity_level'),
                        profile_data.get('target_date'),
                        profile_data.get('daily_calorie_goal'),
                        existing['id']
                    ))
                    logger.info("✅ User profile update completed")
                else:
                    # Create new
                    conn.execute("""
                        INSERT INTO user_profile (
                            current_weight, target_weight, height, age, gender,
                            activity_level, target_date, daily_calorie_goal
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        profile_data.get('current_weight'),
                        profile_data.get('target_weight'),
                        profile_data.get('height'),
                        profile_data.get('age'),
                        profile_data.get('gender'),
                        profile_data.get('activity_level'),
                        profile_data.get('target_date'),
                        profile_data.get('daily_calorie_goal')
                    ))
                    logger.info("✅ User profile creation completed")
                
                return True
                
        except Exception as e:
            logger.error(f"❌ Profile save failed: {e}")
            return False
    
    def get_profile(self):
        """Get user profile"""
        try:
            with self.get_connection() as conn:
                profile = conn.execute(
                    "SELECT * FROM user_profile ORDER BY created_at DESC LIMIT 1"
                ).fetchone()
                
                return dict(profile) if profile else None
                
        except Exception as e:
            logger.error(f"❌ Profile query failed: {e}")
            return None
    
    def calculate_bmr(self, weight, height, age, gender):
        """Calculate BMR (Basal Metabolic Rate) - Harris-Benedict formula"""
        if gender.lower() == 'male':
            bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age)
        else:  # female
            bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age)
        
        return round(bmr, 1)
    
    def calculate_tdee(self, bmr, activity_level):
        """Calculate TDEE (Total Daily Energy Expenditure)"""
        activity_multipliers = {
            'sedentary': 1.2,      # Little to no exercise
            'light': 1.375,        # Light exercise (1-3 times/week)
            'moderate': 1.55,      # Moderate exercise (3-5 times/week)
            'active': 1.725,       # Heavy exercise (6-7 times/week)
            'very_active': 1.9     # Very heavy exercise (2x/day)
        }
        
        multiplier = activity_multipliers.get(activity_level, 1.2)
        return round(bmr * multiplier, 1)
    
    def calculate_daily_calorie_goal(self, profile_data):
        """Calculate daily calorie goal for target weight achievement"""
        try:
            # Calculate BMR
            bmr = self.calculate_bmr(
                profile_data['current_weight'],
                profile_data['height'],
                profile_data['age'],
                profile_data['gender']
            )
            
            # Calculate TDEE
            tdee = self.calculate_tdee(bmr, profile_data['activity_level'])
            
            # Weight change needed
            weight_change = profile_data['target_weight'] - profile_data['current_weight']
            
            # Target period (days)
            target_date = datetime.strptime(profile_data['target_date'], '%Y-%m-%d').date()
            days_to_target = (target_date - date.today()).days
            
            if days_to_target <= 0:
                return tdee  # Return maintenance calories if no time left
            
            # 1kg = 7700kcal calculation
            kcal_per_kg = 7700
            total_calorie_change = weight_change * kcal_per_kg
            daily_calorie_adjustment = total_calorie_change / days_to_target
            
            # Daily target calories
            daily_goal = tdee + daily_calorie_adjustment
            
            # Safety range check (above 80% of BMR, below 120% of TDEE)
            min_safe = bmr * 0.8
            max_safe = tdee * 1.2
            
            daily_goal = max(min_safe, min(max_safe, daily_goal))
            
            return round(daily_goal, 1)
            
        except Exception as e:
            logger.error(f"❌ Calorie goal calculation failed: {e}")
            return 2000  # Default value
    
    def get_progress_summary(self):
        """Target achievement progress summary"""
        profile = self.get_profile()
        if not profile:
            return None
        
        try:
            # Average calories up to today (recent 7 days)
            with self.get_connection() as conn:
                recent_avg = conn.execute("""
                    SELECT AVG(total_calories) as avg_calories
                    FROM daily_summary 
                    WHERE date >= date('now', '-7 days')
                    AND total_calories > 0
                """).fetchone()
            
            avg_calories = recent_avg['avg_calories'] if recent_avg['avg_calories'] else 0
            
            # Progress ratio vs goal
            goal_calories = profile['daily_calorie_goal']
            if goal_calories:
                progress_ratio = avg_calories / goal_calories
            else:
                progress_ratio = 0
            
            # Days remaining to target
            if profile['target_date']:
                target_date = datetime.strptime(profile['target_date'], '%Y-%m-%d').date()
                days_remaining = (target_date - date.today()).days
            else:
                days_remaining = 0
            
            return {
                'current_weight': profile['current_weight'],
                'target_weight': profile['target_weight'],
                'weight_change_needed': profile['target_weight'] - profile['current_weight'],
                'daily_calorie_goal': goal_calories,
                'recent_avg_calories': round(avg_calories, 1),
                'progress_ratio': round(progress_ratio, 2),
                'days_remaining': days_remaining,
                'on_track': 0.9 <= progress_ratio <= 1.1  # On track if within 90-110% of goal
            }
            
        except Exception as e:
            logger.error(f"❌ Progress calculation failed: {e}")
            return None

# Recommended profile data generation functions (for future UI use)
def get_recommended_profile_for_weight_loss():
    """Recommended profile for weight loss"""
    return {
        'current_weight': 70.0,
        'target_weight': 65.0,
        'height': 170.0,
        'age': 25,
        'gender': 'male',
        'activity_level': 'moderate',
        'target_date': '2025-03-01',  # 2 months later
        'daily_calorie_goal': None  # Auto-calculated
    }

def get_recommended_profile_for_weight_gain():
    """Recommended profile for weight gain"""
    return {
        'current_weight': 55.0,
        'target_weight': 60.0,
        'height': 165.0,
        'age': 22,
        'gender': 'female',
        'activity_level': 'light',
        'target_date': '2025-04-01',  # 3 months later
        'daily_calorie_goal': None  # Auto-calculated
    }

def get_recommended_profile_for_maintenance():
    """Recommended profile for weight maintenance"""
    return {
        'current_weight': 65.0,
        'target_weight': 65.0,
        'height': 168.0,
        'age': 30,
        'gender': 'male',
        'activity_level': 'moderate',
        'target_date': '2025-12-31',  # Long-term maintenance
        'daily_calorie_goal': None  # Auto-calculated
    }

# Global profile manager instance
profile_manager = UserProfile()

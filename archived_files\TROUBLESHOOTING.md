# 🔧 위치 추적 및 자동 알림 문제 해결 가이드

## 📋 문제 해결 체크리스트

### 1. 위치 추적이 작동하지 않는 경우

#### ✅ 기본 확인사항
- [ ] **브라우저에서 위치 권한 허용** - F12 > 콘솔에서 GPS 오류 확인
- [ ] **WebSocket 연결 상태** - 브라우저 개발자 도구에서 Network > WS 탭 확인
- [ ] **서버 로그 확인** - `app.log` 파일이나 콘솔에서 위치 수신 확인
- [ ] **JavaScript 오류** - F12 > 콘솔에서 에러 메시지 확인

#### 🎯 설정 확인
```bash
# 현재 설정 확인
grep -E "DETECTION_RADIUS|REQUIRED_STAY_TIME" .env

# 테스트용 빠른 설정
DETECTION_RADIUS=10.0
REQUIRED_STAY_TIME=5
```

#### 🧪 테스트 방법
```bash
# 위치 추적 테스트 실행
python test_location_tracking.py

# 대화형 테스트
python test_location_tracking.py interactive
```

### 2. 알림이 오지 않는 경우

#### 🔔 알림 조건 확인
1. **거리 조건**: 음식점 반경 내에 위치하는가?
2. **시간 조건**: 충분한 시간 동안 머물렀는가?
3. **중복 방지**: 이미 알림을 받았는가?

#### 📍 거리 확인 방법
```python
# 현재 위치와 음식점 거리 계산
from geopy.distance import geodesic

user_pos = (35.830569, 128.753993)  # 현재 위치
restaurant_pos = (35.837030, 128.751693)  # 동궁찜닭

distance = geodesic(user_pos, restaurant_pos).meters
print(f"거리: {distance:.2f}m")
```

#### ⏱️ 디버그 정보 활성화
```javascript
// 브라우저 콘솔에서 실행
toggleDebugMode();  // 디버그 모드 활성화
```

### 3. 자동 리디렉션이 작동하지 않는 경우

#### 🔄 설정 확인
```bash
# .env 파일에서 확인
AUTO_REDIRECT_ENABLED=true
AUTO_REDIRECT_DELAY=3
```

#### 🌐 브라우저 호환성
- Chrome/Edge: 완전 지원
- Firefox: 대부분 지원
- Safari: 일부 제한
- 모바일 브라우저: 제한적 지원

### 4. 서버 연결 문제

#### 🚀 서버 상태 확인
```bash
# 메인 서버 확인
curl -f http://localhost:5000

# Analyzer 서버 확인 (선택사항)
curl -f http://localhost:5001/health
```

#### 🔌 포트 충돌 확인
```bash
# Windows
netstat -ano | findstr :5000

# Linux/Mac
lsof -i :5000
```

### 5. 성능 최적화

#### ⚡ 빠른 테스트 설정
```env
# .env 파일에 추가
DETECTION_RADIUS=5.0
REQUIRED_STAY_TIME=3
AUTO_REDIRECT_DELAY=2
LOCATION_UPDATE_INTERVAL=500
FAST_TEST_MODE=true
```

#### 🎛️ 운영 환경 설정
```env
DETECTION_RADIUS=7.0
REQUIRED_STAY_TIME=10
AUTO_REDIRECT_DELAY=5
LOCATION_UPDATE_INTERVAL=1000
DEBUG_MODE=false
```

## 🐛 일반적인 문제들

### 문제 1: "위치를 감지할 수 없습니다"
**원인**: GPS 권한 없음 또는 실내 환경
**해결방법**:
1. 브라우저 설정에서 위치 권한 허용
2. 위치 설정 패널에서 "직접 입력" 모드 사용
3. "지도에서 드래그" 모드로 테스트

### 문제 2: "알림이 너무 늦게 옵니다"
**원인**: 체류 시간 설정이 너무 김
**해결방법**:
```env
REQUIRED_STAY_TIME=3  # 기본 10초를 3초로 단축
```

### 문제 3: "알림 후 페이지가 이동하지 않습니다"
**원인**: 자동 리디렉션 비활성화 또는 팝업 차단
**해결방법**:
1. 브라우저 팝업 차단 해제
2. 수동으로 "지금 등록하기" 버튼 클릭
3. `.env`에서 `AUTO_REDIRECT_ENABLED=true` 확인

### 문제 4: "음식점이 감지되지 않습니다"
**원인**: 감지 반경이 너무 작음
**해결방법**:
```env
DETECTION_RADIUS=15.0  # 반경을 15m로 증가
```

### 문제 5: "서버 연결 오류"
**원인**: 서버가 실행되지 않음
**해결방법**:
```bash
# 향상된 버전 실행
python app_enhanced.py

# 또는 기본 버전
python app.py
```

## 📊 디버깅 도구

### 1. 로그 레벨 조정
```env
LOG_LEVEL=DEBUG
DETAILED_LOCATION_LOGGING=true
```

### 2. 브라우저 개발자 도구 활용
```javascript
// 콘솔에서 실행
console.log('현재 위치:', currentPosition);
console.log('감지 반경:', config.detection_radius);
console.log('체류 시간:', config.required_stay_time);
```

### 3. 실시간 상태 모니터링
- 브라우저에서 F12 > Network > WS 탭으로 WebSocket 메시지 확인
- "실시간 상태" 패널에서 위치 업데이트 확인
- 디버그 모드에서 상세 로그 확인

## 🔧 고급 설정

### 1. 여러 환경 설정
```bash
# 개발 환경
cp .env.enhanced .env.dev

# 테스트 환경  
cp .env.enhanced .env.test

# 운영 환경
cp .env.enhanced .env.prod
```

### 2. 음식점 데이터 수정
```json
// restaurants.json에서 테스트용 위치 추가
{
    "name": "테스트 음식점",
    "lat": 35.830569,
    "lon": 128.753993,
    "category": "테스트"
}
```

### 3. 감지 반경 시각화
```javascript
// 브라우저 콘솔에서 실행하여 감지 반경 확인
userCircle.setRadius(config.detection_radius);
```

## 📞 추가 지원

### 로그 파일 위치
- 메인 로그: `app.log`
- 오류 로그: 콘솔 출력
- 디버그 로그: 브라우저 개발자 도구

### 테스트 명령어
```bash
# 기능 테스트
python test_location_tracking.py

# 서버 상태 확인
curl -f http://localhost:5000/api/analyzer_status

# 대화형 위치 테스트
python test_location_tracking.py interactive
```

### 성능 모니터링
```bash
# 실시간 로그 확인
tail -f app.log

# 메모리 사용량 확인
ps aux | grep python
```

---

## ⚠️ 주의사항

1. **개발 환경에서만 사용**: DEBUG_MODE는 운영 환경에서 비활성화
2. **API 키 보안**: 실제 API 키는 환경변수로 관리
3. **브라우저 호환성**: 최신 브라우저 사용 권장
4. **위치 정확도**: GPS는 실외에서 더 정확
5. **배터리 소모**: 지속적인 위치 추적은 배터리를 소모

## 🎯 최적 테스트 환경

```env
# 테스트에 최적화된 설정
DETECTION_RADIUS=8.0
REQUIRED_STAY_TIME=5
AUTO_REDIRECT_ENABLED=true
AUTO_REDIRECT_DELAY=3
DEBUG_MODE=true
DETAILED_LOCATION_LOGGING=true
FAST_TEST_MODE=true
```

이 설정을 사용하면 빠르고 정확한 테스트가 가능합니다!

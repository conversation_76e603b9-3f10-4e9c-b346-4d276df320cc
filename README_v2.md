# 🍽️ AI Restaurant Visit and Calorie Management System v2.0

## 📋 개요
실시간 위치 추적을 통한 스마트 음식점 방문 감지 및 AI 기반 칼로리 관리 시스템입니다. 모듈화된 구조로 개선되어 더욱 체계적이고 확장 가능한 시스템으로 발전했습니다.

## ✨ 주요 기능

### 🎯 실시간 위치 추적
- **Kakao Maps API 연동**: 실시간 음식점/카페 감지
- **자동 알림 시스템**: 설정된 시간 체류 시 음식 등록 알림
- **다양한 위치 설정 방법**: GPS, 드래그, 클릭, 수동 입력

### 🍔 스마트 음식 인식
- **AI 이미지 분석**: 음식 사진을 통한 자동 칼로리 계산
- **영수증 OCR**: Google Vision API를 통한 영수증 텍스트 인식
- **다양한 등록 방법**: 사진 촬영, 영수증 스캔, 직접 입력

### 🏃‍♂️ 운동 추천 시스템
- **개인화된 운동 추천**: 체중, 나이, 활동량에 따른 맞춤 운동
- **초과 칼로리 기반 운동 시간 계산**: 정확한 운동량 제안
- **과식 관리 기능**: 과식한 날 분석 및 대처 방법 제공

### 📊 상세한 분석 및 대시보드
- **영양소 분석**: 탄수화물, 단백질, 지방 비율 분석
- **주간/일간 트렌드**: 시각적 데이터 표시
- **목표 달성도 추적**: BMR, TDEE 기반 개인화된 목표

## 🏗️ 모듈화된 구조

```
ai-project-gps/
├── app.py                 # 메인 애플리케이션 (모듈화된 버전)
├── modules/               # 모듈화된 코드
│   ├── config/           # 설정 관리
│   │   ├── __init__.py
│   │   └── settings.py   # 환경변수, 설정 중앙 관리
│   ├── utils/            # 유틸리티 함수들
│   │   ├── __init__.py
│   │   ├── file_utils.py     # 파일 처리
│   │   ├── data_utils.py     # 데이터 변환/검증
│   │   └── location_utils.py # 위치/GPS 관련
│   ├── routes/           # Flask 라우트 모듈
│   │   ├── __init__.py
│   │   ├── main_routes.py    # 메인, 대시보드, 프로필
│   │   ├── food_routes.py    # 음식 등록, 영수증 OCR
│   │   ├── exercise_routes.py # 운동 추천, 과식 관리
│   │   └── api_routes.py     # API 엔드포인트
│   └── socketio_handlers.py  # 실시간 위치 추적
├── templates/            # HTML 템플릿
├── static/              # CSS, JS, 이미지
└── uploads/             # 업로드된 파일들
```

## 🚀 설치 및 실행

### 1. 요구사항 설치
```bash
pip install -r requirements.txt
```

### 2. 환경변수 설정
`.env` 파일을 생성하고 다음 내용을 입력:

```env
# Kakao API 설정
KAKAO_REST_API_KEY=your_kakao_rest_api_key
KAKAO_JAVASCRIPT_KEY=your_kakao_javascript_key  # 선택사항

# 위치 추적 설정
DETECTION_RADIUS=10.0        # 감지 반경 (미터)
REQUIRED_STAY_TIME=5         # 체류 시간 (초)

# API 설정
ANALYZER_API_URL=http://localhost:5001
MFDS_API_KEY=your_mfds_api_key

# Google Vision API (영수증 OCR용, 선택사항)
GOOGLE_APPLICATION_CREDENTIALS=receipt-ocr-key.json
```

### 3. 서버 실행
```bash
python app.py
```

### 4. 브라우저에서 접속
- **메인 페이지**: http://localhost:5000
- **대시보드**: http://localhost:5000/dashboard
- **운동 추천**: http://localhost:5000/exercise
- **과식 관리**: http://localhost:5000/binge_eating

## 🎮 사용법

### 📍 위치 추적 테스트
1. 메인 페이지에서 **위치 설정** 버튼 클릭
2. 원하는 위치 설정 방법 선택:
   - **드래그**: 지도의 파란 마커를 드래그
   - **클릭**: 지도를 클릭한 위치로 이동
   - **GPS**: 실제 GPS 위치 사용
   - **수동 입력**: 위도/경도 직접 입력

3. 음식점 근처로 이동 후 5초 대기
4. 자동 알림 확인 및 음식 등록

### 🍔 음식 등록
1. **음식 등록** 버튼 클릭
2. 음식 사진 촬영 또는 업로드
3. AI 분석 결과에서 음식 선택
4. 영양정보 확인 후 저장

### 📄 영수증 OCR
1. **영수증 등록** 버튼 클릭
2. 영수증 사진 업로드
3. 자동 텍스트 인식 및 음식 추출
4. 인식된 음식들 확인 후 저장

### 🏃‍♂️ 운동 추천
1. **프로필** 설정 (체중, 나이, 활동량 등)
2. **운동 추천** 페이지 방문
3. 개인화된 운동 계획 확인
4. 초과 칼로리 기반 운동 시간 계산

### 🍽️ 과식 관리
1. **과식한 날** 페이지 방문
2. 오늘의 칼로리 분석 확인
3. 과식 패턴 및 문제 음식 파악
4. 맞춤형 대처 전략 실행

## 🔧 개발자 정보

### 🧪 테스트 데이터 생성
**과식한 날** 페이지에서 **샘플 데이터 생성** 버튼을 클릭하면:
- 4150 kcal 고칼로리 샘플 음식 5개
- 테스트용 사용자 프로필
- 운동 추천 기능 테스트 가능

### 📡 API 엔드포인트
- `GET /api/health` - 서버 상태 확인
- `GET /api/system_status` - 전체 시스템 상태
- `GET /api/analyzer_status` - AI 분석기 상태
- `POST /api/add_sample_data` - 테스트 데이터 생성

### 🔌 실시간 통신
WebSocket을 통한 실시간 위치 추적:
- `update_location` - 위치 업데이트
- `notification` - 음식점 감지 알림
- `status_update` - 입장/퇴장 상태

## 🚨 트러블슈팅

### 지도가 표시되지 않는 경우
1. `.env` 파일에 Kakao API 키가 올바르게 설정되었는지 확인
2. 브라우저 콘솔에서 JavaScript 오류 확인
3. 네트워크 연결 상태 확인

### 음식 분석이 실패하는 경우
1. Analyzer API 서버 (localhost:5001) 실행 상태 확인
2. `/api/analyzer_status`에서 API 상태 확인
3. 이미지 파일 크기가 10MB 이하인지 확인

### 영수증 OCR이 작동하지 않는 경우
1. Google Cloud Vision API 키 파일 확인
2. `pip install google-cloud-vision` 설치 확인
3. 영수증 이미지가 선명하고 가독성이 좋은지 확인

## 📚 의존성 라이브러리

### 필수 라이브러리
- `Flask` - 웹 프레임워크
- `Flask-SocketIO` - 실시간 통신
- `requests` - HTTP 요청
- `Pillow` - 이미지 처리

### 선택적 라이브러리
- `google-cloud-vision` - 영수증 OCR
- `python-dotenv` - 환경변수 관리

## 🌟 새로운 기능 (v2.0)

### ✅ 완료된 개선사항
- **모듈화된 구조**: 체계적인 코드 분리 및 관리
- **과식 관리 시스템**: 과식 패턴 분석 및 대처 방법 제공
- **개선된 UI**: 직관적인 버튼 배치 및 디자인
- **환경변수 중앙 관리**: 설정의 일관성 및 보안 강화
- **향상된 오류 처리**: 더 안정적인 시스템 운영

### 🔄 업그레이드 가이드
기존 v1.0에서 v2.0으로 업그레이드:
1. 새로운 코드 다운로드
2. 기존 `.env` 파일 백업
3. `modules/` 폴더가 포함된 새 구조 확인
4. `app.py` 실행으로 모듈화된 버전 사용

## 📞 지원 및 문의

시스템 관련 문의사항이나 개선 제안은 개발팀에 연락해주세요.

---

**🎉 AI Restaurant Visit and Calorie Management System v2.0**  
*더 스마트하고, 더 건강한 식습관 관리의 시작*

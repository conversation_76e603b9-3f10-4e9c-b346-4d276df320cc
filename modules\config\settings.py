# -*- coding: utf-8 -*-
"""
Configuration Management Module
애플리케이션 설정을 중앙에서 관리하는 모듈
"""

import os
from pathlib import Path
from typing import Union, Any


def safe_getenv(key: str, default: Any, value_type=str) -> Union[str, int, float]:
    """환경변수를 안전하게 파싱하여 주석을 제거하고 적절한 타입으로 변환"""
    try:
        value = os.getenv(key, default)
        # 주석 제거 (# 이후 모든 내용 제거)
        if isinstance(value, str) and '#' in value:
            value = value.split('#')[0].strip()
        
        # 타입 변환
        if value_type == float:
            return float(value)
        elif value_type == int:
            return int(value)
        else:
            return str(value)
    except (ValueError, TypeError):
        return value_type(default) if value_type != str else default


class Config:
    """애플리케이션 설정 클래스"""
    
    # 프로젝트 루트 경로
    PROJECT_ROOT = Path(__file__).parent.parent.parent
    
    # === API 설정 ===
    KAKAO_REST_API_KEY = safe_getenv('KAKAO_REST_API_KEY', 'YOUR_KAKAO_REST_API_KEY')
    KAKAO_JAVASCRIPT_KEY = safe_getenv('KAKAO_JAVASCRIPT_KEY', 'YOUR_KAKAO_JAVASCRIPT_KEY')
    MFDS_API_KEY = safe_getenv('MFDS_API_KEY', 'YOUR_API_KEY_HERE')
    
    # === 위치 추적 설정 ===
    DETECTION_RADIUS = safe_getenv('DETECTION_RADIUS', 10.0, float)
    REQUIRED_STAY_TIME = safe_getenv('REQUIRED_STAY_TIME', 5, int)
    
    # === 서버 설정 ===
    SECRET_KEY = safe_getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = safe_getenv('DEBUG', 'True').lower() == 'true'
    PORT = safe_getenv('PORT', 5000, int)
    HOST = safe_getenv('HOST', '0.0.0.0')
    
    # === 파일 업로드 설정 ===
    UPLOAD_FOLDER = safe_getenv('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = set(safe_getenv('ALLOWED_EXTENSIONS', 'png,jpg,jpeg,gif,webp').split(','))
    MAX_FILE_SIZE = safe_getenv('MAX_FILE_SIZE', 10 * 1024 * 1024, int)  # 10MB
    
    # === API 연동 설정 ===
    ANALYZER_API_URL = safe_getenv('ANALYZER_API_URL', 'http://localhost:5001')
    API_TIMEOUT = safe_getenv('API_TIMEOUT', 30, int)
    
    # === 데이터베이스 설정 ===
    DATABASE_PATH = safe_getenv('DATABASE_PATH', 'food_data.db')
    
    # === 로깅 설정 ===
    LOG_LEVEL = safe_getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = safe_getenv('LOG_FILE', 'app.log')
    
    # === Google Vision API 설정 ===
    GOOGLE_CREDENTIALS_PATH = PROJECT_ROOT / 'receipt-ocr-key.json'
    
    # === Kakao Maps 설정 ===
    KAKAO_PLACES_API_URL = "https://dapi.kakao.com/v2/local/search/category.json"
    
    @classmethod
    def get_kakao_js_key(cls) -> str:
        """Kakao JavaScript API 키 반환 (우선순위: 환경변수 > REST API 키)"""
        if cls.KAKAO_JAVASCRIPT_KEY != 'YOUR_KAKAO_JAVASCRIPT_KEY':
            return cls.KAKAO_JAVASCRIPT_KEY
        elif cls.KAKAO_REST_API_KEY != 'YOUR_KAKAO_REST_API_KEY':
            # JavaScript Key가 없으면 REST API 키를 사용 (대부분의 경우 동일함)
            return cls.KAKAO_REST_API_KEY
        else:
            return 'YOUR_KAKAO_JAVASCRIPT_KEY'
    
    @classmethod
    def is_kakao_configured(cls) -> bool:
        """Kakao API 키가 제대로 설정되었는지 확인"""
        return cls.KAKAO_REST_API_KEY != 'YOUR_KAKAO_REST_API_KEY'
    
    @classmethod
    def get_template_config(cls) -> dict:
        """템플릿에서 사용할 설정 딕셔너리 반환"""
        return {
            'kakao_js_key': cls.get_kakao_js_key(),
            'detection_radius': cls.DETECTION_RADIUS,
            'required_stay_time': cls.REQUIRED_STAY_TIME,
            'kakao_configured': cls.is_kakao_configured(),
            'debug': cls.DEBUG
        }
    
    @classmethod
    def ensure_directories(cls):
        """필요한 디렉토리들을 생성"""
        directories = [
            cls.UPLOAD_FOLDER,
            'logs',
            'modules/routes',
            'modules/utils',
            'modules/config'
        ]
        
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    @classmethod
    def validate_config(cls) -> list:
        """설정 유효성 검사 및 문제점 반환"""
        issues = []
        
        # API 키 확인
        if not cls.is_kakao_configured():
            issues.append("Kakao API 키가 설정되지 않았습니다")
        
        # 디렉토리 확인
        if not Path(cls.UPLOAD_FOLDER).exists():
            issues.append(f"업로드 폴더가 존재하지 않습니다: {cls.UPLOAD_FOLDER}")
        
        # Google Vision 키 파일 확인
        if not cls.GOOGLE_CREDENTIALS_PATH.exists():
            issues.append(f"Google Vision API 키 파일이 없습니다: {cls.GOOGLE_CREDENTIALS_PATH}")
        
        return issues


# 기본 설정 인스턴스
config = Config()

# -*- coding: utf-8 -*-
"""
운동 추천 시스템
칼로리 기반 운동 시간 계산 및 영양소 분석
"""

from datetime import datetime, date, timedelta
import json
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

# 운동별 시간당 칼로리 소모량 (kcal/hour)
EXERCISE_DATA = {
    "걷기": 280,
    "달리기": 600, 
    "자전거": 500,
    "수영": 700,
    "요가": 200,
    "헬스(웨이트)": 400,
    "등산": 450,
    "농구": 550,
    "테니스": 480,
    "배드민턴": 350
}

class ExerciseRecommendationSystem:
    """운동 추천 시스템"""
    
    def __init__(self):
        self.exercise_data = EXERCISE_DATA
        logger.info("🏃‍♂️ 운동 추천 시스템 초기화 완료")
    
    def calculate_bmr(self, weight, height, age, gender):
        """기초대사율(BMR) 계산 - Harris-Benedict 공식"""
        try:
            if gender.lower() == 'male':
                bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age)
            else:  # female
                bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age)
            
            return round(bmr, 1)
        except Exception as e:
            logger.error(f"❌ BMR 계산 오류: {e}")
            return 1500  # 기본값
    
    def calculate_tdee(self, bmr, activity_level):
        """총 일일 에너지 소비량(TDEE) 계산"""
        activity_multipliers = {
            'sedentary': 1.2,      # 앉아서 생활
            'light': 1.375,       # 가벼운 활동
            'moderate': 1.55,     # 보통 활동
            'active': 1.725,      # 활발한 활동
            'very_active': 1.9    # 매우 활발한 활동
        }
        
        multiplier = activity_multipliers.get(activity_level, 1.55)
        return round(bmr * multiplier, 1)
    
    def calculate_excess_calories(self, consumed_calories, tdee):
        """초과 칼로리 계산"""
        excess = max(0, consumed_calories - tdee)
        return round(excess, 1)
    
    def get_exercise_recommendations(self, excess_calories):
        """초과 칼로리 기반 운동 추천"""
        if excess_calories <= 0:
            return {
                'message': '🎉 오늘 칼로리 목표를 달성했습니다!',
                'recommendations': {},
                'excess_calories': 0
            }
        
        recommendations = {}
        for exercise_name, calories_per_hour in self.exercise_data.items():
            # 필요한 운동 시간 계산 (분 단위)
            minutes_needed = round((excess_calories / calories_per_hour) * 60)
            
            # 시간과 분으로 변환
            if minutes_needed >= 60:
                hours = minutes_needed // 60
                remaining_minutes = minutes_needed % 60
                if remaining_minutes > 0:
                    time_str = f"{hours}시간 {remaining_minutes}분"
                else:
                    time_str = f"{hours}시간"
            else:
                time_str = f"{minutes_needed}분"
            
            recommendations[exercise_name] = {
                'time': time_str,
                'minutes': minutes_needed,
                'calories_per_hour': calories_per_hour
            }
        
        # 추천도 순으로 정렬 (짧은 시간 순)
        sorted_recommendations = dict(
            sorted(recommendations.items(), key=lambda x: x[1]['minutes'])
        )
        
        return {
            'message': f'💪 {excess_calories:.0f}kcal 초과! 운동으로 소모해보세요.',
            'recommendations': sorted_recommendations,
            'excess_calories': excess_calories
        }
    
    def analyze_weekly_nutrition(self, weekly_records):
        """주간 영양소 분석"""
        try:
            if not weekly_records:
                return {
                    'total_calories': 0,
                    'avg_daily_calories': 0,
                    'carb_percent': 0,
                    'protein_percent': 0,
                    'fat_percent': 0,
                    'alerts': ['📊 분석할 데이터가 없습니다.']
                }
            
            # 총 영양소 계산
            totals = {"calories": 0, "carbs": 0, "protein": 0, "fat": 0}
            for record in weekly_records:
                totals["calories"] += record.get('calories', 0)
                totals["carbs"] += record.get('carbs', 0)
                totals["protein"] += record.get('protein', 0)
                totals["fat"] += record.get('fat', 0)
            
            total_calories = totals["calories"]
            if total_calories == 0:
                return {
                    'total_calories': 0,
                    'avg_daily_calories': 0,
                    'carb_percent': 0,
                    'protein_percent': 0,
                    'fat_percent': 0,
                    'alerts': ['📊 칼로리 데이터가 없습니다.']
                }
            
            # 영양소 비율 계산
            carb_percent = round((totals["carbs"] * 4 / total_calories) * 100, 1)
            protein_percent = round((totals["protein"] * 4 / total_calories) * 100, 1)
            fat_percent = round((totals["fat"] * 9 / total_calories) * 100, 1)
            
            avg_daily_calories = round(total_calories / 7, 1)
            
            # 영양소 균형 평가
            alerts = []
            
            # 탄수화물 (권장: 55-65%)
            if carb_percent < 55:
                alerts.append(f"🍚 탄수화물이 {carb_percent}%로 부족합니다. 쌀, 고구마, 현미 등을 추가해보세요.")
            elif carb_percent > 65:
                alerts.append(f"🍚 탄수화물이 {carb_percent}%로 과다합니다. 밥/빵 섭취를 줄여보세요.")
            
            # 단백질 (권장: 7-20%)
            if protein_percent < 7:
                alerts.append(f"🥩 단백질이 {protein_percent}%로 부족합니다. 닭가슴살, 계란, 두부 등을 추가해보세요.")
            elif protein_percent > 20:
                alerts.append(f"🥩 단백질이 {protein_percent}%로 과다합니다. 단백질 섭취를 조절해보세요.")
            
            # 지방 (권장: 15-30%)
            if fat_percent < 15:
                alerts.append(f"🥑 지방이 {fat_percent}%로 부족합니다. 견과류나 아보카도를 추가해보세요.")
            elif fat_percent > 30:
                alerts.append(f"🥑 지방이 {fat_percent}%로 과다합니다. 튀김류, 기름진 음식을 줄여보세요.")
            
            if not alerts:
                alerts.append("✅ 모든 영양소가 권장 범위 내에 있습니다!")
            
            return {
                'total_calories': total_calories,
                'avg_daily_calories': avg_daily_calories,
                'carb_percent': carb_percent,
                'protein_percent': protein_percent,
                'fat_percent': fat_percent,
                'alerts': alerts,
                'analysis_date': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ 주간 영양소 분석 오류: {e}")
            return {
                'total_calories': 0,
                'avg_daily_calories': 0,
                'carb_percent': 0,
                'protein_percent': 0,
                'fat_percent': 0,
                'alerts': [f'📊 분석 중 오류 발생: {str(e)}']
            }
    
    def should_recommend_exercise(self, daily_meals, current_time=None):
        """운동 추천 시점 판단"""
        if current_time is None:
            current_time = datetime.now()
        
        current_hour = current_time.hour
        meal_count_today = len(daily_meals)
        
        # 조건 1: 하루에 세 번째 식사를 했을 때
        third_meal_trigger = meal_count_today >= 3
        
        # 조건 2: 저녁 8시 이후
        evening_trigger = current_hour >= 20
        
        trigger_reason = []
        if third_meal_trigger:
            trigger_reason.append("세 번째 식사 완료")
        if evening_trigger:
            trigger_reason.append("저녁 8시 이후")
        
        should_recommend = third_meal_trigger or evening_trigger
        
        return {
            'should_recommend': should_recommend,
            'meal_count': meal_count_today,
            'current_hour': current_hour,
            'trigger_reason': trigger_reason,
            'next_check_time': self._get_next_check_time(current_time)
        }
    
    def _get_next_check_time(self, current_time):
        """다음 체크 시간 계산"""
        # 저녁 8시가 지나지 않았다면 8시로 설정
        if current_time.hour < 20:
            next_check = current_time.replace(hour=20, minute=0, second=0, microsecond=0)
        else:
            # 다음날 8시
            next_day = current_time + timedelta(days=1)
            next_check = next_day.replace(hour=20, minute=0, second=0, microsecond=0)
        
        return next_check.isoformat()

# 전역 인스턴스
exercise_system = ExerciseRecommendationSystem()

def get_exercise_recommendations(user_profile, daily_calories):
    """간편한 운동 추천 함수 - 안전한 변환 적용"""
    try:
        # 안전한 숫자 변환 함수 import
        import re
        
        def safe_convert(value, default=0.0):
            try:
                if value is None or value == '' or value == 'None':
                    return default
                if isinstance(value, str):
                    value = value.strip().replace('칼', '').replace('kcal', '').replace('cal', '')
                    if not value:
                        return default
                    value = re.sub(r'[^0-9.-]', '', value)
                    if not value or value == '.' or value == '-':
                        return default
                if isinstance(value, (int, float)):
                    return float(value)
                return float(value)
            except (ValueError, TypeError):
                return default
        
        # BMR 계산 (안전한 변환)
        weight = safe_convert(user_profile.get('current_weight', 70))
        height = safe_convert(user_profile.get('height', 170))
        age = int(safe_convert(user_profile.get('age', 25)))
        gender = user_profile.get('gender', 'male')
        activity_level = user_profile.get('activity_level', 'moderate')
        
        bmr = exercise_system.calculate_bmr(
            weight=weight,
            height=height, 
            age=age,
            gender=gender
        )
        
        # TDEE 계산
        tdee = exercise_system.calculate_tdee(
            bmr=bmr,
            activity_level=activity_level
        )
        
        # 초과 칼로리 계산
        safe_daily_calories = safe_convert(daily_calories)
        excess_calories = exercise_system.calculate_excess_calories(
            consumed_calories=safe_daily_calories,
            tdee=tdee
        )
        
        # 운동 추천
        recommendations = exercise_system.get_exercise_recommendations(excess_calories)
        
        return {
            'success': True,
            'bmr': bmr,
            'tdee': tdee,
            'consumed_calories': safe_daily_calories,
            'excess_calories': excess_calories,
            'recommendations': recommendations
        }
        
    except Exception as e:
        logger.error(f"❌ 운동 추천 계산 오류: {e}")
        return {
            'success': False,
            'error': str(e),
            'bmr': 1500,
            'tdee': 2000,
            'consumed_calories': 0,
            'excess_calories': 0,
            'recommendations': {'message': '오류로 인해 추천을 생성할 수 없습니다.', 'recommendations': {}}
        }

def analyze_nutrition(weekly_records):
    """간편한 영양소 분석 함수"""
    return exercise_system.analyze_weekly_nutrition(weekly_records)

def check_exercise_reminder(daily_meals):
    """간편한 운동 알림 체크 함수"""
    return exercise_system.should_recommend_exercise(daily_meals)

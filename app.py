# -*- coding: utf-8 -*-
"""
AI Restaurant Visit and Calorie Management System
모듈화된 메인 애플리케이션 파일 - v2.0
"""

import os
import logging
from pathlib import Path
from flask import Flask
from flask_socketio import SocketIO

# 모듈 임포트
from modules import config, register_blueprints, register_socketio_events
from modules.utils import load_json_file


def load_env_file():
    """환경변수 파일을 강제로 로드"""
    env_loaded = False
    
    # 1. python-dotenv로 시도
    try:
        from dotenv import load_dotenv
        result = load_dotenv()
        if result:
            print("✅ python-dotenv로 .env 파일 로드 성공")
            env_loaded = True
        else:
            print("⚠️ python-dotenv는 있지만 .env 파일을 찾지 못함")
    except ImportError:
        print("⚠️ python-dotenv가 설치되지 않음. 수동 로드 시도...")
    
    # 2. 수동 로드 (python-dotenv가 없거나 실패한 경우)
    if not env_loaded:
        env_path = Path(__file__).parent / '.env'
        if env_path.exists():
            print(f"📁 .env 파일 경로: {env_path}")
            try:
                with open(env_path, 'r', encoding='utf-8') as f:
                    loaded_count = 0
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            
                            # 값에서 주석 제거
                            if '#' in value:
                                value = value.split('#')[0].strip()
                            
                            # 따옴표 제거
                            if value.startswith('"') and value.endswith('"'):
                                value = value[1:-1]
                            elif value.startswith("'") and value.endswith("'"):
                                value = value[1:-1]
                            
                            os.environ[key] = value
                            loaded_count += 1
                
                print(f"✅ .env 파일 수동 로드 성공 ({loaded_count}개 변수)")
                env_loaded = True
                
            except Exception as e:
                print(f"❌ .env 파일 읽기 오류: {e}")
        else:
            print(f"❌ .env 파일을 찾을 수 없음: {env_path}")
    
    return env_loaded


def setup_logging():
    """로깅 시스템 설정"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 로그 레벨 설정
    log_level = getattr(logging, config.LOG_LEVEL.upper(), logging.INFO)
    
    # 콘솔 및 파일 로깅
    logging.basicConfig(
        level=log_level,
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(config.LOG_FILE, encoding='utf-8')
        ]
    )
    
    # 외부 라이브러리 로깅 레벨 조정
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)


def create_app():
    """Flask 애플리케이션 팩토리"""
    # 환경변수 로드
    env_success = load_env_file()
    
    # Flask 앱 생성
    app = Flask(__name__)
    
    # 설정 적용
    app.config['SECRET_KEY'] = config.SECRET_KEY
    app.config['DEBUG'] = config.DEBUG
    app.config['MAX_CONTENT_LENGTH'] = config.MAX_FILE_SIZE
    
    # 로깅 설정
    setup_logging()
    logger = logging.getLogger(__name__)
    
    # 필요한 디렉토리 생성
    config.ensure_directories()
    
    # SocketIO 설정
    socketio = SocketIO(app, cors_allowed_origins='*')
    
    # Blueprint 등록
    app = register_blueprints(app)
    
    # SocketIO 이벤트 핸들러 등록
    socketio = register_socketio_events(socketio)
    
    # 설정 검증 및 로깅
    issues = config.validate_config()
    if issues:
        logger.warning("⚠️ 설정 문제점:")
        for issue in issues:
            logger.warning(f"   - {issue}")
    else:
        logger.info("✅ 모든 설정이 올바르게 구성되었습니다")
    
    # 음식점 데이터 로드
    restaurants = load_json_file('restaurants.json', [])
    logger.info(f"📍 로드된 음식점: {len(restaurants)}개")
    
    # 시작 메시지
    logger.info("🚀 AI Restaurant Visit and Calorie Management System v2.0 준비 완료")
    
    return app, socketio


def print_startup_info():
    """시작 정보 출력"""
    print("🚀 AI Restaurant Visit and Calorie Management System v2.0")
    print("=" * 70)
    print("📍 모듈화된 실시간 위치 추적 시스템:")
    print("   - ✅ Kakao Place Search API 통합")
    print("   - 🔍 실시간 음식점/카페 감지")
    print("   - 🎯 동적 위치 기반 알림")
    print("   - 📱 향상된 모바일 GPS 추적")
    print("   - 🏃‍♂️ 스마트 운동 추천")
    print("   - 🍽️ 과식 관리 시스템")
    print("=" * 70)
    print("🌐 메인 서버: http://localhost:5000")
    print("📊 대시보드: http://localhost:5000/dashboard")
    print("🏃‍♂️ 운동 추천: http://localhost:5000/exercise")
    print("🍽️ 과식 관리: http://localhost:5000/binge_eating")
    print("=" * 70)
    print("🎯 위치 추적 설정:")
    print(f"   - 감지 반경: {config.DETECTION_RADIUS}m")
    print(f"   - 체류 시간: {config.REQUIRED_STAY_TIME}초")
    print(f"   - Kakao API: {'✅ 연동완료' if config.is_kakao_configured() else '❌ 미설정'}")
    print("=" * 70)
    
    if config.is_kakao_configured():
        print("\n🧪 실시간 위치 감지 테스트 방법:")
        print("   1. 브라우저에서 http://localhost:5000 접속")
        print("   2. 위치 권한 허용")
        print("   3. 실제 음식점/카페에 이동 (또는 마커 드래그)")
        print(f"   4. {config.REQUIRED_STAY_TIME}초 대기 후 실시간 알림 확인")
    else:
        print("\n⚠️ Kakao API 키를 설정하면 실시간 위치 추적을 사용할 수 있습니다.")
        print("   .env 파일에서 KAKAO_REST_API_KEY를 설정해주세요.")
    
    print("\n🚀 모듈화된 서버 시작 중...")


if __name__ == '__main__':
    # 시작 정보 출력
    print_startup_info()
    
    # 앱 생성
    app, socketio = create_app()
    
    # 서버 실행
    socketio.run(
        app, 
        debug=config.DEBUG, 
        host=config.HOST, 
        port=config.PORT
    )

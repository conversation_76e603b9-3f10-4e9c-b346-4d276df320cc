<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>AI 음식점 GPS - 간단 지도</title>
    <script type="text/javascript" src="https://dapi.kakao.com/v2/maps/sdk.js?appkey={{ config.kakao_js_key }}&libraries=services"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .header h1 {
            color: #1d1d1f;
            margin: 0;
            font-size: 2em;
        }
        
        .map-container {
            width: 100%;
            height: 500px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        #map {
            width: 100%;
            height: 100%;
        }
        
        .controls {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            background: #007aff;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            background: #0056cc;
            transform: translateY(-2px);
        }
        
        .status {
            background: white;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .location-info {
            font-size: 14px;
            color: #666;
        }
        
        .debug-info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 10px;
            display: none;
        }
        
        .restaurant-info {
            padding: 10px;
            max-width: 200px;
        }
        
        .restaurant-info h4 {
            margin: 0 0 5px 0;
            color: #1d1d1f;
        }
        
        .restaurant-info p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🗺️ AI 음식점 GPS</h1>
        <p>{{ restaurants|length }}개 음식점 • 실시간 위치 추적</p>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="findMyLocation()">📍 내 위치 찾기</button>
        <button class="btn" onclick="toggleDebug()">🔧 디버그</button>
        <button class="btn" onclick="refreshMap()">🔄 새로고침</button>
    </div>
    
    <div class="map-container">
        <div id="map"></div>
    </div>
    
    <div class="status">
        <div id="location-info" class="location-info">위치 정보 로드 중...</div>
        <div id="debug-info" class="debug-info"></div>
    </div>

    <script>
        console.log("🚀 간단 지도 페이지 시작");
        
        // 데이터 초기화
        const socket = io();
        const restaurants = {{ restaurants|tojson|safe }};
        const config = {{ config|tojson|safe }};
        
        console.log("📊 데이터 로드:", {
            restaurants: restaurants.length + "개",
            config: config
        });
        
        // 지도 관련 변수
        let map = null;
        let userMarker = null;
        let userCircle = null;
        let restaurantMarkers = [];
        let debugMode = false;
        
        // 현재 위치
        let currentPosition = {
            lat: 35.830569788,
            lng: 128.75399385
        };
        
        // 지도 초기화
        function initMap() {
            console.log("🗺️ 지도 초기화 시작");
            
            try {
                // Kakao Maps API 확인
                if (typeof kakao === 'undefined' || typeof kakao.maps === 'undefined') {
                    throw new Error('Kakao Maps API가 로드되지 않았습니다');
                }
                
                // 지도 컨테이너 확인
                const container = document.getElementById('map');
                if (!container) {
                    throw new Error('지도 컨테이너를 찾을 수 없습니다');
                }
                
                // 지도 생성
                const options = {
                    center: new kakao.maps.LatLng(currentPosition.lat, currentPosition.lng),
                    level: 3
                };
                
                map = new kakao.maps.Map(container, options);
                console.log("✅ 지도 생성 완료");
                
                // 컨트롤 추가
                const mapTypeControl = new kakao.maps.MapTypeControl();
                map.addControl(mapTypeControl, kakao.maps.ControlPosition.TOPRIGHT);
                
                const zoomControl = new kakao.maps.ZoomControl();
                map.addControl(zoomControl, kakao.maps.ControlPosition.BOTTOMRIGHT);
                
                // 마커 생성
                createRestaurantMarkers();
                createUserMarker();
                
                // 위치 정보 업데이트
                updateLocationInfo();
                
                console.log("🎉 지도 초기화 완료!");
                
            } catch (error) {
                console.error("❌ 지도 초기화 실패:", error);
                document.getElementById('location-info').innerHTML = 
                    `❌ 지도 로드 실패: ${error.message}`;
            }
        }
        
        // 음식점 마커 생성
        function createRestaurantMarkers() {
            console.log("🍽️ 음식점 마커 생성 시작");
            
            restaurants.forEach(restaurant => {
                const markerImage = new kakao.maps.MarkerImage(
                    "https://cdn-icons-png.flaticon.com/512/3514/3514491.png",
                    new kakao.maps.Size(30, 30),
                    { offset: new kakao.maps.Point(15, 15) }
                );
                
                const marker = new kakao.maps.Marker({
                    position: new kakao.maps.LatLng(restaurant.lat, restaurant.lon),
                    map: map,
                    title: restaurant.name,
                    image: markerImage
                });
                
                const infowindow = new kakao.maps.InfoWindow({
                    content: `
                        <div class="restaurant-info">
                            <h4>${restaurant.name}</h4>
                            <p>${restaurant.category}</p>
                            <p>반경 ${config.detection_radius}m 감지</p>
                        </div>
                    `
                });
                
                kakao.maps.event.addListener(marker, 'click', function() {
                    infowindow.open(map, marker);
                });
                
                restaurantMarkers.push({ marker, infowindow, restaurant });
            });
            
            console.log(`✅ ${restaurants.length}개 음식점 마커 생성 완료`);
        }
        
        // 사용자 마커 생성
        function createUserMarker() {
            console.log("👤 사용자 마커 생성 시작");
            
            const userMarkerImage = new kakao.maps.MarkerImage(
                "https://t1.daumcdn.net/localimg/localimages/07/mapapidoc/markerStar.png",
                new kakao.maps.Size(24, 35)
            );
            
            const position = new kakao.maps.LatLng(currentPosition.lat, currentPosition.lng);
            
            userMarker = new kakao.maps.Marker({
                position: position,
                map: map,
                image: userMarkerImage,
                draggable: true
            });
            
            userCircle = new kakao.maps.Circle({
                center: position,
                radius: config.detection_radius,
                strokeWeight: 2,
                strokeColor: '#007aff',
                strokeOpacity: 0.6,
                fillColor: '#007aff',
                fillOpacity: 0.2,
                map: map
            });
            
            // 드래그 이벤트
            kakao.maps.event.addListener(userMarker, 'dragend', function() {
                const pos = userMarker.getPosition();
                updateUserPosition(pos.getLat(), pos.getLng());
            });
            
            console.log("✅ 사용자 마커 생성 완료");
        }
        
        // 사용자 위치 업데이트
        function updateUserPosition(lat, lng) {
            currentPosition = { lat, lng };
            
            const position = new kakao.maps.LatLng(lat, lng);
            userMarker.setPosition(position);
            userCircle.setPosition(position);
            
            updateLocationInfo();
            
            // 서버에 위치 전송
            socket.emit('update_location', {
                lat: lat,
                lon: lng
            });
            
            console.log(`📍 위치 업데이트: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
        }
        
        // 위치 정보 표시 업데이트
        function updateLocationInfo() {
            const info = document.getElementById('location-info');
            info.innerHTML = `
                📍 현재 위치: ${currentPosition.lat.toFixed(6)}, ${currentPosition.lng.toFixed(6)}<br>
                🍽️ 음식점: ${restaurants.length}개 | 감지 반경: ${config.detection_radius}m
            `;
            
            if (debugMode) {
                updateDebugInfo();
            }
        }
        
        // 디버그 정보 업데이트
        function updateDebugInfo() {
            const debugDiv = document.getElementById('debug-info');
            
            // 가까운 음식점 계산
            const nearbyRestaurants = restaurants.map(restaurant => {
                const distance = calculateDistance(
                    currentPosition.lat, currentPosition.lng,
                    restaurant.lat, restaurant.lon
                );
                return { ...restaurant, distance };
            }).sort((a, b) => a.distance - b.distance).slice(0, 5);
            
            let debugHtml = '<strong>🔧 디버그 정보:</strong><br>';
            debugHtml += `Kakao API: ${typeof kakao !== 'undefined' ? '✅' : '❌'}<br>`;
            debugHtml += `지도 객체: ${map ? '✅' : '❌'}<br>`;
            debugHtml += `가까운 음식점 (상위 5개):<br>`;
            
            nearbyRestaurants.forEach(restaurant => {
                const inRange = restaurant.distance <= config.detection_radius;
                const icon = inRange ? '🎯' : '📍';
                debugHtml += `${icon} ${restaurant.name}: ${restaurant.distance.toFixed(1)}m<br>`;
            });
            
            debugDiv.innerHTML = debugHtml;
        }
        
        // 거리 계산
        function calculateDistance(lat1, lon1, lat2, lon2) {
            const R = 6371000;
            const dLat = (lat2 - lat1) * Math.PI / 180;
            const dLon = (lon2 - lon1) * Math.PI / 180;
            const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                      Math.sin(dLon/2) * Math.sin(dLon/2);
            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            return R * c;
        }
        
        // 컨트롤 함수들
        function findMyLocation() {
            const pos = userMarker.getPosition();
            map.setCenter(pos);
            map.setLevel(3);
            console.log("🎯 내 위치로 이동");
        }
        
        function toggleDebug() {
            debugMode = !debugMode;
            const debugDiv = document.getElementById('debug-info');
            debugDiv.style.display = debugMode ? 'block' : 'none';
            
            if (debugMode) {
                updateDebugInfo();
                console.log("🔧 디버그 모드 활성화");
            } else {
                console.log("🔧 디버그 모드 비활성화");
            }
        }
        
        function refreshMap() {
            if (map) {
                map.relayout();
                updateLocationInfo();
                console.log("🔄 지도 새로고침");
            }
        }
        
        // Kakao Maps API 로드 대기 및 초기화
        function waitForKakao() {
            if (typeof kakao !== 'undefined' && kakao.maps) {
                console.log("✅ Kakao Maps API 로드 완료");
                kakao.maps.load(initMap);
            } else {
                console.log("⏳ Kakao Maps API 로드 대기 중...");
                setTimeout(waitForKakao, 100);
            }
        }
        
        // 페이지 로드 완료 후 시작
        document.addEventListener('DOMContentLoaded', function() {
            console.log("📄 DOM 로드 완료");
            waitForKakao();
        });
        
        // Socket.IO 이벤트
        socket.on('connect', function() {
            console.log('🔗 서버 연결됨');
        });
        
        socket.on('restaurant_notification', function(data) {
            console.log('🍽️ 음식점 알림:', data);
            alert(`🍽️ ${data.restaurant_name}에 도착했습니다!`);
        });
    </script>
</body>
</html>

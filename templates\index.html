<!DOCTYPE html>
<html>
  <head>
    <title>AI 음식점 방문 및 칼로리 관리</title>
    <script
      type="text/javascript"
      src="https://dapi.kakao.com/v2/maps/sdk.js?appkey={{ config.kakao_js_key }}&libraries=services"
    ></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display",
          "Segoe UI", Roboto, sans-serif;
        margin: 0;
        padding: 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        color: #1d1d1f;
        min-height: 100vh;
      }

      .header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        color: #1d1d1f;
        padding: 30px 20px;
        text-align: center;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      }

      .header h1 {
        margin: 0 0 12px 0;
        font-size: 36px;
        font-weight: 700;
        letter-spacing: -0.5px;
      }

      .header p {
        margin: 0;
        color: #86868b;
        font-size: 18px;
        font-weight: 400;
      }

      .nav-bar {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.3);
        display: flex;
        justify-content: center;
        gap: 16px;
        flex-wrap: wrap;
      }

      .nav-btn {
        background: #007aff;
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.3s ease;
        display: inline-block;
        cursor: pointer;
      }

      .nav-btn:hover {
        background: #0056cc;
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
      }

      .nav-btn.secondary {
        background: #34c759;
      }

      .nav-btn.secondary:hover {
        background: #28a745;
        box-shadow: 0 8px 24px rgba(52, 199, 89, 0.3);
      }

      .nav-btn.location {
        background: #ff9500;
      }

      .nav-btn.location:hover {
        background: #e6890a;
        box-shadow: 0 8px 24px rgba(255, 149, 0, 0.3);
      }

      .nav-btn.test {
        background: #ff3b30;
      }

      .nav-btn.test:hover {
        background: #d70015;
        box-shadow: 0 8px 24px rgba(255, 59, 48, 0.3);
      }

      #map {
        height: 500px;
        width: 100%;
        border-radius: 0;
      }

      .map-container {
        position: relative;
        margin: 24px;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
      }

      #customControl {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 10;
        display: flex;
        flex-direction: column;
        gap: 12px;
      }

      .control-btn {
        background: rgba(255, 255, 255, 0.95);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        width: 44px;
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
      }

      .control-btn:hover {
        background: rgba(255, 255, 255, 1);
        transform: scale(1.05);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      }

      .status-panel {
        margin: 24px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 24px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        max-height: 400px;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .status-panel::-webkit-scrollbar {
        display: none;
      }

      .status-panel h2 {
        margin: 0 0 20px 0;
        color: #1d1d1f;
        font-size: 22px;
        font-weight: 600;
      }

      .status-item {
        padding: 16px;
        margin: 12px 0;
        background: #f2f2f7;
        border-radius: 12px;
        border-left: 4px solid #007aff;
        font-size: 14px;
        transition: all 0.3s ease;
      }

      .status-item:hover {
        background: #e8e8ed;
        transform: translateX(4px);
      }

      .status-item.entered {
        border-left-color: #34c759;
        background: rgba(52, 199, 89, 0.1);
      }

      .status-item.left {
        border-left-color: #ff9500;
        background: rgba(255, 149, 0, 0.1);
      }

      .status-item.notification {
        border-left-color: #ff3b30;
        background: rgba(255, 59, 48, 0.1);
        font-weight: 600;
      }

      .status-item.error {
        border-left-color: #ff3b30;
        background: rgba(255, 59, 48, 0.1);
      }

      .status-item.debug {
        border-left-color: #af52de;
        background: rgba(175, 82, 222, 0.1);
        font-size: 12px;
        opacity: 0.8;
      }

      /* === Enhanced Notification with Alert-style === */
      .notification-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: none;
        backdrop-filter: blur(8px);
        -webkit-backdrop-filter: blur(8px);
      }

      .notification {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        color: #1d1d1f;
        padding: 40px;
        margin: 0;
        border-radius: 24px;
        display: none;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10001;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        min-width: 400px;
        max-width: 500px;
        text-align: center;
        animation: slideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translate(-50%, -60%) scale(0.9);
        }
        to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
        }
      }

      @keyframes slideInRight {
        from {
          opacity: 0;
          transform: translateX(100%);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      .notification h3 {
        margin: 0 0 16px 0;
        font-size: 24px;
        font-weight: 700;
        color: #1d1d1f;
      }

      .notification p {
        margin: 0 0 24px 0;
        color: #86868b;
        line-height: 1.6;
        font-size: 16px;
      }

      .notification-info {
        background: rgba(0, 122, 255, 0.1);
        border-radius: 12px;
        padding: 16px;
        margin: 20px 0;
        font-size: 14px;
        color: #1d1d1f;
      }

      .notification-buttons {
        display: flex;
        gap: 16px;
        justify-content: center;
        margin-top: 24px;
      }

      .notification button {
        padding: 14px 28px;
        border: none;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 140px;
      }

      .notification #register-btn,
      .notification #exercise-btn {
        background: #007aff;
        color: white;
      }

      .notification #register-btn:hover,
      .notification #exercise-btn:hover {
        background: #0056cc;
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0, 122, 255, 0.3);
      }

      .notification #cancel-btn {
        background: rgba(134, 134, 139, 0.1);
        color: #86868b;
        border: 1px solid rgba(134, 134, 139, 0.3);
      }

      .notification #cancel-btn:hover {
        background: rgba(134, 134, 139, 0.2);
      }

      .auto-redirect {
        font-size: 14px;
        color: #86868b;
        margin-top: 20px;
      }

      .countdown {
        display: inline-block;
        background: rgba(0, 122, 255, 0.1);
        padding: 4px 12px;
        border-radius: 8px;
        font-weight: 600;
        color: #007aff;
      }

      .restaurant-info {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 12px;
        padding: 20px;
        min-width: 220px;
        max-width: 300px;
        box-sizing: border-box;
        font-size: 15px;
        color: #1d1d1f;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.3);
      }

      .restaurant-info h4 {
        margin: 0 0 10px 0;
        color: #1d1d1f;
        font-size: 18px;
        font-weight: 600;
      }

      .restaurant-info p {
        margin: 6px 0;
        font-size: 14px;
        color: #86868b;
      }

      /* 오늘 요약 패널 */
      .today-summary-panel {
        margin: 24px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 30px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      .today-summary-panel h2 {
        margin: 0 0 24px 0;
        color: #1d1d1f;
        font-size: 22px;
        font-weight: 600;
        border-bottom: 1px solid #f2f2f7;
        padding-bottom: 12px;
      }

      .metric-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
      }

      .metric-item {
        text-align: center;
        padding: 24px;
        background: #f2f2f7;
        border-radius: 16px;
        border-left: 4px solid #007aff;
        transition: all 0.3s ease;
      }

      .metric-item:hover {
        background: #e8e8ed;
        transform: scale(1.02);
      }

      .metric-value {
        font-size: 32px;
        font-weight: 700;
        color: #1d1d1f;
        margin-bottom: 8px;
      }

      .metric-label {
        color: #86868b;
        font-size: 14px;
        font-weight: 500;
      }

      /* === Enhanced Location Control Panel === */
      .location-panel {
        margin: 24px;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 30px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      }

      .location-controls {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 16px;
        margin-top: 20px;
      }

      .location-method {
        padding: 20px;
        border: 2px solid #f2f2f7;
        border-radius: 16px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
        background: #f2f2f7;
      }

      .location-method:hover {
        border-color: #007aff;
        background: rgba(0, 122, 255, 0.05);
        transform: translateY(-2px);
      }

      .location-method.active {
        border-color: #007aff;
        background: rgba(0, 122, 255, 0.1);
        color: #007aff;
      }

      .location-method .icon {
        font-size: 28px;
        margin-bottom: 10px;
      }

      .location-method .title {
        font-weight: 600;
        margin-bottom: 6px;
        font-size: 16px;
      }

      .location-method .desc {
        font-size: 12px;
        color: #86868b;
        line-height: 1.4;
      }

      .coord-input {
        display: flex;
        gap: 12px;
        margin-top: 20px;
        align-items: center;
      }

      .coord-input input {
        flex: 1;
        padding: 12px 16px;
        border: 1px solid #d2d2d7;
        border-radius: 12px;
        font-size: 14px;
        background: rgba(255, 255, 255, 0.9);
      }

      .coord-input input:focus {
        outline: none;
        border-color: #007aff;
        box-shadow: 0 0 0 4px rgba(0, 122, 255, 0.1);
      }

      .coord-input button {
        padding: 12px 24px;
        background: #007aff;
        color: white;
        border: none;
        border-radius: 12px;
        cursor: pointer;
        font-weight: 600;
        transition: all 0.3s ease;
      }

      .coord-input button:hover {
        background: #0056cc;
        transform: translateY(-1px);
      }

      .current-location {
        margin-top: 20px;
        padding: 16px;
        background: rgba(0, 122, 255, 0.1);
        border-radius: 12px;
        font-size: 14px;
        color: #1d1d1f;
      }

      .distance-info {
        margin-top: 16px;
        padding: 16px;
        background: rgba(255, 204, 0, 0.1);
        border-radius: 12px;
        font-size: 12px;
        max-height: 200px;
        overflow-y: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
      }

      .distance-info::-webkit-scrollbar {
        display: none;
      }

      @media (max-width: 768px) {
        .header h1 {
          font-size: 28px;
        }

        .nav-bar {
          padding: 16px;
        }

        .nav-btn {
          padding: 10px 20px;
          font-size: 13px;
        }

        .map-container {
          margin: 16px;
        }

        #map {
          height: 400px;
        }

        .notification {
          min-width: 300px;
          margin: 20px;
          padding: 30px;
        }

        .info-panel,
        .status-panel,
        .location-panel {
          margin: 16px;
          padding: 20px;
        }

        .feature-grid {
          grid-template-columns: 1fr;
        }
      }
    </style>
  </head>
  <body>
    <div class="header">
      <h1>AI 음식점 방문 및 칼로리 관리</h1>
      <p>실시간 위치 추적 • 자동 음식 인식 • 스마트 칼로리 계산</p>
    </div>

    <div class="nav-bar">
      <a href="/profile" class="nav-btn secondary">프로필</a>
      <a href="/dashboard" class="nav-btn secondary">대시보드</a>
      <a href="/upload_food" class="nav-btn">음식 등록</a>
      <a href="/receipt" class="nav-btn">영수증 등록</a>
      <a href="/exercise" class="nav-btn">운동 추천</a>
      <button
        onclick="toggleLocationPanel()"
        class="nav-btn location"
        id="location-toggle-btn"
      >
        위치 설정
      </button>
      <button onclick="generateSampleData()" class="nav-btn test">
        샘플 데이터 생성
      </button>
    </div>

    <!-- === Enhanced Location Control Panel === -->
    <div class="location-panel" id="location-panel" style="display: none">
      <h2>위치 설정</h2>
      <p>
        테스트를 위해 현재 위치를 변경할 수 있습니다. 식당 근처로 이동하여 알림
        기능을 체험해보세요!
      </p>

      <div class="location-controls">
        <div
          class="location-method active"
          onclick="setLocationMethod('drag', event)"
        >
          <div class="icon">🖱️</div>
          <div class="title">지도에서 드래그</div>
          <div class="desc">지도의 파란 마커를 드래그하여 위치 변경</div>
        </div>
        <div
          class="location-method"
          onclick="setLocationMethod('click', event)"
        >
          <div class="icon">👆</div>
          <div class="title">지도 클릭</div>
          <div class="desc">지도를 클릭한 곳으로 즉시 이동</div>
        </div>
        <div class="location-method" onclick="setLocationMethod('gps', event)">
          <div class="icon">🛰️</div>
          <div class="title">실제 GPS</div>
          <div class="desc">브라우저의 위치 정보 사용</div>
        </div>
        <div
          class="location-method"
          onclick="setLocationMethod('manual', event)"
        >
          <div class="icon">⌨️</div>
          <div class="title">직접 입력</div>
          <div class="desc">위도, 경도를 직접 입력</div>
        </div>
      </div>

      <div class="coord-input" id="manual-input" style="display: none">
        <input
          type="number"
          id="lat-input"
          placeholder="위도 (예: 35.837030)"
          step="0.000001"
        />
        <input
          type="number"
          id="lng-input"
          placeholder="경도 (예: 128.751693)"
          step="0.000001"
        />
        <button onclick="setManualLocation()">위치 설정</button>
      </div>

      <div class="current-location" id="current-location">
        현재 위치: 위치를 설정해주세요
      </div>

      <div class="distance-info" id="distance-info" style="display: none">
        <strong>근처 음식점 거리:</strong>
        <div id="distance-list"></div>
      </div>
    </div>

    <div class="map-container">
      <div id="customControl">
        <button
          class="control-btn"
          id="find-location-btn"
          title="현재 위치로 이동"
        >
          <img
            src="https://cdn-icons-png.flaticon.com/512/5055/5055654.png"
            alt="현 위치"
            style="
              width: 24px;
              height: 24px;
              filter: brightness(0) opacity(0.7);
            "
          />
        </button>
      </div>
      <div id="map"></div>
    </div>

    <!-- 오늘 요약 패널 -->
    <div class="today-summary-panel">
      <h2>오늘 요약 ({{ today.date }})</h2>
      <div class="metric-grid">
        <div class="metric-item">
          <div class="metric-value">
            {{ "%.0f"|format(today.total_calories) }}
          </div>
          <div class="metric-label">칼로리 (kcal)</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">{{ today.meal_count }}</div>
          <div class="metric-label">식사 횟수</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">
            {{ "%.1f"|format(today.total_protein) }}
          </div>
          <div class="metric-label">단백질 (g)</div>
        </div>
        <div class="metric-item">
          <div class="metric-value">{{ "%.1f"|format(today.total_fat) }}</div>
          <div class="metric-label">지방 (g)</div>
        </div>
      </div>
    </div>

    <!-- === Enhanced Notification with Alert-style === -->
    <div class="notification-overlay" id="notification-overlay"></div>
    <div id="notification" class="notification"></div>

    <script>
      console.log("🚀 스크립트 시작");

      const socket = io();

      // 안전한 JSON 파싱
      let restaurants, config;
      try {
        restaurants = JSON.parse("{{restaurants|tojson|safe}}");
        console.log("✅ restaurants 파싱 성공:", restaurants.length + "개");
      } catch (e) {
        console.error("❌ restaurants 파싱 실패:", e);
        restaurants = [];
      }

      try {
        config = JSON.parse("{{config|tojson|safe}}");
        console.log("✅ config 파싱 성공:", config);
      } catch (e) {
        console.error("❌ config 파싱 실패:", e);
        config = {
          kakao_js_key: "65093059a1b8c894bd576e566c080e3d",
          detection_radius: 50,
          required_stay_time: 10,
        };
      }

      let map = null;
      let userMarker = null;
      let userCircle = null;
      let restaurantMarkers = {};
      let currentLocationMethod = "drag";
      let clickListener = null;
      let currentRestaurant = null;
      let debugMode = false;
      let autoRedirectTimer = null;
      let isConnected = false;

      // Location state
      let currentPosition = {
        lat: 35.830569788, // Default: IT관
        lng: 128.75399385,
      };

      function initMap() {
        try {
          // Kakao Maps API 로드 상태 확인
          if (
            typeof kakao === "undefined" ||
            typeof kakao.maps === "undefined"
          ) {
            console.error("Kakao Maps API가 로드되지 않았습니다.");
            addStatusMessage("Kakao Maps API 로드 실패", "error");
            return;
          }

          const container = document.getElementById("map");
          if (!container) {
            console.error("지도 컨테이너를 찾을 수 없습니다.");
            return;
          }

          const options = {
            center: new kakao.maps.LatLng(
              currentPosition.lat,
              currentPosition.lng
            ),
            level: 3,
          };

          map = new kakao.maps.Map(container, options);

          // 지도 컨트롤 추가
          var mapTypeControl = new kakao.maps.MapTypeControl();
          map.addControl(mapTypeControl, kakao.maps.ControlPosition.TOPRIGHT);

          var zoomControl = new kakao.maps.ZoomControl();
          map.addControl(zoomControl, kakao.maps.ControlPosition.BOTTOMRIGHT);

          // 음식점 마커 생성
          createRestaurantMarkers();

          // 사용자 마커 생성
          createUserMarker();

          // 초기 위치 전송
          sendLocationUpdate();
          updateLocationDisplay();

          addStatusMessage("지도 초기화 완료");
          console.log("지도 초기화 성공");
        } catch (error) {
          console.error("지도 초기화 오류:", error);
          addStatusMessage(`지도 초기화 실패: ${error.message}`, "error");
        }
      }

      function createRestaurantMarkers() {
        for (const restaurant of restaurants) {
          const markerImage = new kakao.maps.MarkerImage(
            "https://cdn-icons-png.flaticon.com/512/3514/3514491.png",
            new kakao.maps.Size(30, 30),
            { offset: new kakao.maps.Point(15, 15) }
          );

          const marker = new kakao.maps.Marker({
            position: new kakao.maps.LatLng(restaurant.lat, restaurant.lon),
            map: map,
            title: restaurant.name,
            image: markerImage,
          });

          const infowindow = new kakao.maps.InfoWindow({
            content: `
                      <div class="restaurant-info">
                          <h4>${restaurant.name}</h4>
                          <p>${restaurant.category}</p>
                          <p>반경 ${config.detection_radius}m 이내에서 ${config.required_stay_time}초 체류시 알림</p>
                      </div>
                  `,
          });

          let isOpen = false;
          kakao.maps.event.addListener(marker, "click", function () {
            if (isOpen) {
              infowindow.close();
              isOpen = false;
            } else {
              // 다른 정보창들 닫기
              Object.values(restaurantMarkers).forEach((rm) => {
                if (rm.infowindow) rm.infowindow.close();
                rm.isOpen = false;
              });

              infowindow.open(map, marker);
              isOpen = true;
            }
          });

          restaurantMarkers[restaurant.name] = {
            marker: marker,
            infowindow: infowindow,
            isOpen: isOpen,
          };
        }

        addStatusMessage(`${restaurants.length}개 음식점 마커 생성 완료`);
      }

      function createUserMarker() {
        const userMarkerImage = new kakao.maps.MarkerImage(
          "https://t1.daumcdn.net/localimg/localimages/07/mapapidoc/markerStar.png",
          new kakao.maps.Size(24, 35)
        );

        const position = new kakao.maps.LatLng(
          currentPosition.lat,
          currentPosition.lng
        );

        userMarker = new kakao.maps.Marker({
          position: position,
          map: map,
          image: userMarkerImage,
          draggable: true,
        });

        // 사용자 반경 표시
        userCircle = new kakao.maps.Circle({
          center: position,
          radius: config.detection_radius,
          strokeWeight: 2,
          strokeColor: "#007aff",
          strokeOpacity: 0.6,
          fillColor: "#007aff",
          fillOpacity: 0.2,
          map: map,
        });

        // 마커 드래그 이벤트
        kakao.maps.event.addListener(userMarker, "dragend", function () {
          if (currentLocationMethod === "drag") {
            const pos = userMarker.getPosition();
            updateUserPosition(pos.getLat(), pos.getLng());
          }
        });

        // 현 위치 찾기 버튼
        const findLocationBtn = document.getElementById("find-location-btn");
        findLocationBtn.addEventListener("click", function () {
          const pos = userMarker.getPosition();
          map.setCenter(pos);
          map.setLevel(3);
        });

        addStatusMessage("사용자 위치 마커 생성 완료");
      }

      function updateUserPosition(lat, lng) {
        currentPosition = { lat, lng };

        const position = new kakao.maps.LatLng(lat, lng);
        userMarker.setPosition(position);
        userCircle.setPosition(position);

        sendLocationUpdate();
        updateLocationDisplay();
        updateDistanceInfo();
      }

      function sendLocationUpdate() {
        if (isConnected) {
          socket.emit("update_location", {
            lat: currentPosition.lat,
            lon: currentPosition.lng,
          });

          if (debugMode) {
            addStatusMessage(
              `위치 전송: ${currentPosition.lat.toFixed(
                6
              )}, ${currentPosition.lng.toFixed(6)}`,
              "debug"
            );
          }
        }
      }

      function updateLocationDisplay() {
        const display = document.getElementById("current-location");
        display.innerHTML = `현재 위치: ${currentPosition.lat.toFixed(
          6
        )}, ${currentPosition.lng.toFixed(6)}`;
      }

      function updateDistanceInfo() {
        const distanceDiv = document.getElementById("distance-info");
        const distanceList = document.getElementById("distance-list");

        if (!debugMode) {
          distanceDiv.style.display = "none";
          return;
        }

        // 모든 음식점과의 거리 계산
        const distances = [];
        restaurants.forEach((restaurant) => {
          const distance = calculateDistance(
            currentPosition.lat,
            currentPosition.lng,
            restaurant.lat,
            restaurant.lon
          );
          distances.push({
            name: restaurant.name,
            distance: distance,
            inRange: distance <= config.detection_radius,
          });
        });

        // 거리순 정렬
        distances.sort((a, b) => a.distance - b.distance);

        // 상위 10개만 표시
        let html = "";
        distances.slice(0, 10).forEach((item) => {
          const icon = item.inRange ? "🎯" : "📍";
          const style = item.inRange
            ? "color: #007aff; font-weight: bold;"
            : "";
          html += `<div style="${style}">${icon} ${
            item.name
          }: ${item.distance.toFixed(1)}m</div>`;
        });

        distanceList.innerHTML = html;
        distanceDiv.style.display = "block";
      }

      function calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371000; // 지구 반지름 (미터)
        const dLat = ((lat2 - lat1) * Math.PI) / 180;
        const dLon = ((lon2 - lon1) * Math.PI) / 180;
        const a =
          Math.sin(dLat / 2) * Math.sin(dLat / 2) +
          Math.cos((lat1 * Math.PI) / 180) *
            Math.cos((lat2 * Math.PI) / 180) *
            Math.sin(dLon / 2) *
            Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
      }

      // === Enhanced Location Control Methods ===

      function toggleLocationPanel() {
        const panel = document.getElementById("location-panel");
        const btn = document.getElementById("location-toggle-btn");

        if (panel.style.display === "none" || !panel.style.display) {
          panel.style.display = "block";
          btn.textContent = "위치 설정 닫기";
        } else {
          panel.style.display = "none";
          btn.textContent = "위치 설정";
        }
      }

      function toggleDebugMode() {
        debugMode = !debugMode;
        const btn = document.getElementById("debug-toggle-btn");

        if (debugMode) {
          btn.textContent = "디버그 OFF";
          btn.className = "nav-btn test"; // 빨간색 스타일 사용
          addStatusMessage("디버그 모드 활성화");
          updateDistanceInfo();
          showSimpleToast("디버그 모드가 활성화되었습니다", "success");
        } else {
          btn.textContent = "디버그 모드";
          btn.className = "nav-btn"; // 기본 스타일
          addStatusMessage("디버그 모드 비활성화");
          document.getElementById("distance-info").style.display = "none";
          showSimpleToast("디버그 모드가 비활성화되었습니다", "warning");
        }
      }

      function testNotification() {
        addStatusMessage("테스트 알림 요청");
        socket.emit("test_notification", {
          restaurant: "테스트 음식점",
        });
        showSimpleToast("테스트 알림을 요청했습니다", "info");
      }

      function setLocationMethod(method, event) {
        currentLocationMethod = method;

        // UI 업데이트
        document.querySelectorAll(".location-method").forEach((el) => {
          el.classList.remove("active");
        });
        if (event && event.target) {
          event.target.closest(".location-method").classList.add("active");
        }

        // 입력 필드 표시/숨김
        const manualInput = document.getElementById("manual-input");
        manualInput.style.display = method === "manual" ? "flex" : "none";

        // 기존 클릭 리스너 제거
        if (clickListener) {
          kakao.maps.event.removeListener(map, "click", clickListener);
          clickListener = null;
        }

        // 방법별 설정
        switch (method) {
          case "drag":
            userMarker.setDraggable(true);
            addStatusMessage(
              "드래그 모드: 파란 마커를 드래그하여 위치를 변경하세요"
            );
            break;

          case "click":
            userMarker.setDraggable(false);
            clickListener = kakao.maps.event.addListener(
              map,
              "click",
              function (mouseEvent) {
                const latlng = mouseEvent.latLng;
                updateUserPosition(latlng.getLat(), latlng.getLng());
                addStatusMessage(
                  `클릭 위치로 이동: ${latlng.getLat().toFixed(6)}, ${latlng
                    .getLng()
                    .toFixed(6)}`
                );
              }
            );
            addStatusMessage("클릭 모드: 지도를 클릭하여 위치를 변경하세요");
            break;

          case "gps":
            userMarker.setDraggable(false);
            if (navigator.geolocation) {
              addStatusMessage("GPS 위치를 가져오는 중...");
              navigator.geolocation.getCurrentPosition(
                function (position) {
                  const lat = position.coords.latitude;
                  const lng = position.coords.longitude;
                  updateUserPosition(lat, lng);
                  map.setCenter(new kakao.maps.LatLng(lat, lng));
                  addStatusMessage(
                    `GPS 위치로 설정: ${lat.toFixed(6)}, ${lng.toFixed(6)}`
                  );
                },
                function (error) {
                  addStatusMessage(
                    "GPS 위치를 가져올 수 없습니다. 다른 방법을 사용해주세요.",
                    "error"
                  );
                  setLocationMethod("drag", null);
                }
              );
            } else {
              addStatusMessage(
                "이 브라우저는 GPS를 지원하지 않습니다. 다른 방법을 사용해주세요.",
                "error"
              );
              setLocationMethod("drag", null);
            }
            break;

          case "manual":
            userMarker.setDraggable(false);
            addStatusMessage("수동 입력 모드: 위도와 경도를 직접 입력하세요");
            break;
        }
      }

      function setManualLocation() {
        const latInput = document.getElementById("lat-input");
        const lngInput = document.getElementById("lng-input");

        const lat = parseFloat(latInput.value);
        const lng = parseFloat(lngInput.value);

        if (isNaN(lat) || isNaN(lng)) {
          alert("올바른 위도와 경도를 입력해주세요.");
          return;
        }

        if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
          alert("위도는 -90~90, 경도는 -180~180 범위 내에서 입력해주세요.");
          return;
        }

        updateUserPosition(lat, lng);
        map.setCenter(new kakao.maps.LatLng(lat, lng));
        addStatusMessage(
          `수동 위치 설정: ${lat.toFixed(6)}, ${lng.toFixed(6)}`
        );
      }

      function refreshLocation() {
        sendLocationUpdate();
        addStatusMessage(`위치 새로고침: ${new Date().toLocaleTimeString()}`);
      }

      function addStatusMessage(message, type = "info") {
        // 콘솔에 로그 출력
        const timestamp = new Date().toLocaleTimeString();
        console.log(`${timestamp} - ${type.toUpperCase()}: ${message}`);

        // 디버그 모드에서만 알림 표시
        if (debugMode || type === "notification" || type === "error") {
          const alertType =
            {
              info: "info",
              entered: "success",
              left: "warning",
              notification: "warning",
              error: "error",
              debug: "info",
            }[type] || "info";

          // 간단한 토스트 알림 생성
          if (type === "notification" || type === "error") {
            showSimpleToast(message, alertType);
          }
        }
      }

      function showSimpleToast(message, type = "info") {
        // 기존 토스트 컴테이너 찾기
        let toastContainer = document.getElementById("toast-container");
        if (!toastContainer) {
          toastContainer = document.createElement("div");
          toastContainer.id = "toast-container";
          toastContainer.style.cssText = `
                  position: fixed;
                  top: 20px;
                  right: 20px;
                  z-index: 10000;
                  max-width: 300px;
              `;
          document.body.appendChild(toastContainer);
        }

        // 토스트 요소 생성
        const toast = document.createElement("div");
        const bgColor =
          {
            info: "rgba(0, 122, 255, 0.9)",
            success: "rgba(52, 199, 89, 0.9)",
            warning: "rgba(255, 149, 0, 0.9)",
            error: "rgba(255, 59, 48, 0.9)",
          }[type] || "rgba(0, 122, 255, 0.9)";

        toast.style.cssText = `
              background: ${bgColor};
              color: white;
              padding: 12px 16px;
              border-radius: 12px;
              margin-bottom: 8px;
              font-size: 14px;
              font-weight: 500;
              backdrop-filter: blur(20px);
              -webkit-backdrop-filter: blur(20px);
              box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
              animation: slideInRight 0.3s ease;
              cursor: pointer;
          `;

        toast.textContent = message;

        // 클릭시 제거
        toast.addEventListener("click", () => {
          toast.remove();
        });

        // 3초 후 자동 제거
        setTimeout(() => {
          if (toast.parentNode) {
            toast.remove();
          }
        }, 3000);

        toastContainer.appendChild(toast);
      }

      // === Enhanced Notification System ===

      function showNotification(data) {
        currentRestaurant = data.restaurant;

        const overlay = document.getElementById("notification-overlay");
        const notification = document.getElementById("notification");

        let autoRedirectHtml = "";
        autoRedirectHtml = `
              <div class="auto-redirect">
                  <span class="countdown" id="countdown">10</span>초 후 자동으로 음식 등록 페이지로 이동합니다
              </div>
          `;

        notification.innerHTML = `
              <h3>음식 등록 알림</h3>
              <p>${data.message}</p>
              <div class="notification-info">
                  거리: ${data.distance}m | 체류: ${data.stay_duration}초
              </div>
              <div class="notification-buttons">
                  <button id="register-btn">지금 등록하기</button>
                  <button id="cancel-btn">취소</button>
              </div>
              ${autoRedirectHtml}
          `;

        overlay.style.display = "block";
        notification.style.display = "block";

        // 버튼 이벤트 리스너
        document.getElementById("register-btn").onclick = function () {
          clearAutoRedirectTimer();
          hideNotification();
          window.location.href = data.upload_url;
        };

        document.getElementById("cancel-btn").onclick = function () {
          clearAutoRedirectTimer();
          hideNotification();
        };

        // 배경 클릭시 닫기
        overlay.onclick = function () {
          clearAutoRedirectTimer();
          hideNotification();
        };

        // 자동 리디렉션 타이머 (10초)
        startAutoRedirectTimer(data.upload_url, 10);

        addStatusMessage(`알림 표시: ${data.restaurant}`, "notification");
      }

      function hideNotification() {
        document.getElementById("notification-overlay").style.display = "none";
        document.getElementById("notification").style.display = "none";
      }

      function startAutoRedirectTimer(url, seconds) {
        let timeLeft = seconds;
        const countdownElement = document.getElementById("countdown");

        autoRedirectTimer = setInterval(() => {
          timeLeft--;
          if (countdownElement) {
            countdownElement.textContent = timeLeft;
          }

          if (timeLeft <= 0) {
            clearAutoRedirectTimer();
            hideNotification();
            window.location.href = url;
          }
        }, 1000);
      }

      function clearAutoRedirectTimer() {
        if (autoRedirectTimer) {
          clearInterval(autoRedirectTimer);
          autoRedirectTimer = null;
        }
      }

      // === Enhanced SocketIO Event Handlers ===

      socket.on("connect", () => {
        isConnected = true;
        addStatusMessage("서버 연결됨");
      });

      socket.on("disconnect", () => {
        isConnected = false;
        addStatusMessage("서버 연결 해제됨", "error");
      });

      socket.on("status_update", (data) => {
        // 입장/퇴장 메시지는 표시하지 않음 (사용자 요청에 따라 제거)
        // 일정 시간 체류 시 알림만 표시
      });

      socket.on("notification", (data) => {
        addStatusMessage(
          `알림 발송: ${data.restaurant} (거리: ${data.distance}m, 체류: ${data.stay_duration}초)`,
          "notification"
        );
        showNotification(data);
      });

      socket.on("nearby_restaurants", (data) => {
        if (debugMode && data.restaurants) {
          const nearbyList = data.restaurants
            .map(
              (r) =>
                `${r.in_range ? "🎯" : "📍"} ${r.name}: ${r.distance.toFixed(
                  1
                )}m`
            )
            .join(", ");
          addStatusMessage(`근처 음식점: ${nearbyList}`, "debug");
        }
      });

      socket.on("config_info", (data) => {
        // 서버에서 설정 정보 수신
        Object.assign(config, data);

        // 사용자 반경 업데이트
        if (userCircle) {
          userCircle.setRadius(config.detection_radius);
        }

        addStatusMessage(
          `설정 수신: 반경 ${config.detection_radius}m, 체류시간 ${config.required_stay_time}초`
        );
      });

      socket.on("error", (data) => {
        addStatusMessage(`오류: ${data.message}`, "error");
      });

      // === 운동 추천 알림 시스템 ===

      socket.on("exercise_notification", (data) => {
        addStatusMessage(`운동 알림: ${data.trigger_reason}`, "notification");
        showExerciseNotification(data);
      });

      socket.on("exercise_status_update", (data) => {
        if (debugMode) {
          addStatusMessage(
            `식사: ${data.meal_count}번, 시간: ${data.current_hour}시, 알림: ${
              data.should_recommend ? "✅" : "❌"
            }`,
            "debug"
          );
        }
      });

      function showExerciseNotification(data) {
        const overlay = document.getElementById("notification-overlay");
        const notification = document.getElementById("notification");

        let autoRedirectHtml = "";
        if (!data.test) {
          autoRedirectHtml = `
                  <div class="auto-redirect">
                      <span class="countdown" id="countdown">8</span>초 후 자동으로 운동 추천 페이지로 이동합니다
                  </div>
              `;
        }

        notification.innerHTML = `
              <h3>운동 추천 알림</h3>
              <p>${data.message}</p>
              <div class="notification-info">
                  식사: ${
                    data.daily_calories || 0
                  }캠 | 운동으로 건강을 챙겨보세요!
              </div>
              <div class="notification-buttons">
                  <button id="exercise-btn">운동 추천 보기</button>
                  <button id="cancel-btn">다음에</button>
              </div>
              ${autoRedirectHtml}
          `;

        overlay.style.display = "block";
        notification.style.display = "block";

        // 버튼 이벤트 리스너
        document.getElementById("exercise-btn").onclick = function () {
          clearAutoRedirectTimer();
          hideNotification();
          window.location.href = data.exercise_url || "/exercise";
        };

        document.getElementById("cancel-btn").onclick = function () {
          clearAutoRedirectTimer();
          hideNotification();
        };

        // 배경 클릭시 닫기
        overlay.onclick = function () {
          clearAutoRedirectTimer();
          hideNotification();
        };

        // 자동 리디렉션 타이머 (8초)
        if (!data.test) {
          startAutoRedirectTimer(data.exercise_url || "/exercise", 8);
        }

        addStatusMessage(
          `운동 알림 표시: ${data.trigger_reason}`,
          "notification"
        );
      }

      function testExerciseNotification() {
        addStatusMessage("운동 알림 테스트 요청");
        socket.emit("force_exercise_notification", {
          test: true,
        });
      }

      // 샘플 데이터 생성 함수
      async function generateSampleData() {
        if (
          confirm(
            "운동 기능 테스트를 위한 고칼로리 샘플 데이터를 생성합니다.\n(4150 kcal 샘플 음식 + 프로필)\n\n계속하시겠습니까?"
          )
        ) {
          try {
            showSimpleToast("샘플 데이터 생성 중...", "info");

            const response = await fetch("/api/add_sample_data", {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
            });

            const result = await response.json();

            if (result.success) {
              showSimpleToast(
                `✅ 샘플 데이터 생성 완료! (${result.data.total_calories} kcal)`,
                "success"
              );

              // 상세 정보 표시
              alert(
                `🎉 샘플 데이터 생성 완료!\n\n` +
                  `🍔 음식: ${result.data.food_count}개\n` +
                  `🔥 총 칼로리: ${result.data.total_calories} kcal\n` +
                  `⚡ TDEE: ${result.data.tdee} kcal\n` +
                  `🏃‍♂️ 초과 칼로리: ${Math.round(
                    result.data.excess_calories
                  )} kcal\n\n` +
                  `📝 테스트 방법:\n` +
                  `1. 대시보드에서 칼로리 확인\n` +
                  `2. 운동 추천 페이지에서 초과 칼로리 확인\n` +
                  `3. 운동 시간 계산 확인`
              );

              // 3초 후 대시보드로 이동
              setTimeout(() => {
                window.location.href = "/dashboard";
              }, 3000);
            } else {
              showSimpleToast(
                `❌ 샘플 데이터 생성 실패: ${result.error}`,
                "error"
              );
              console.error("샘플 데이터 오류:", result.error);
            }
          } catch (error) {
            showSimpleToast("❌ 샘플 데이터 생성 실패", "error");
            console.error("샘플 데이터 오류:", error);
          }
        }
      }

      // 1초마다 위치 전송
      setInterval(() => {
        if (isConnected) {
          sendLocationUpdate();
        }
      }, 1000);

      // Kakao Maps API 로드 상태 확인 및 초기화
      function waitForKakaoMaps(callback, maxAttempts = 50, attempt = 1) {
        if (typeof kakao !== "undefined" && kakao.maps) {
          console.log(`Kakao Maps API 로드 완료 (시도 ${attempt}회)`);
          callback();
        } else if (attempt < maxAttempts) {
          console.log(
            `Kakao Maps API 로드 대기 중... (${attempt}/${maxAttempts})`
          );
          setTimeout(() => {
            waitForKakaoMaps(callback, maxAttempts, attempt + 1);
          }, 100);
        } else {
          console.error("Kakao Maps API 로드 실패 - 최대 시도 횟수 초과");
          addStatusMessage(
            "지도 서비스 로드 실패. 페이지를 새로고침해주세요.",
            "error"
          );
        }
      }

      // Kakao Maps API 초기화
      function initializeKakaoMaps() {
        try {
          console.log("Kakao Maps API 초기화 시작");

          // API 키 확인
          if (
            !config.kakao_js_key ||
            config.kakao_js_key === "YOUR_KAKAO_JAVASCRIPT_KEY"
          ) {
            console.error("Kakao JavaScript API 키가 설정되지 않았습니다.");
            addStatusMessage("Kakao API 키 오류", "error");
            return;
          }

          // Kakao Maps API 로드 대기 후 초기화
          waitForKakaoMaps(() => {
            try {
              // kakao.maps.load로 안전하게 초기화
              kakao.maps.load(() => {
                console.log("Kakao Maps 서비스 초기화 완료");
                initMap();
                // 기본 드래그 모드로 설정
                setLocationMethod("drag", null);
                addStatusMessage(
                  "시스템 준비 완료! 음식점 근처로 이동하면 자동 알림이 나타납니다."
                );
              });
            } catch (error) {
              console.error("Kakao Maps 서비스 초기화 오류:", error);
              addStatusMessage(`지도 서비스 오류: ${error.message}`, "error");
            }
          });
        } catch (error) {
          console.error("Kakao Maps 초기화 오류:", error);
          addStatusMessage(`지도 서비스 오류: ${error.message}`, "error");
        }
      }

      // 페이지 로드 완료 후 실행
      document.addEventListener("DOMContentLoaded", function () {
        console.log("🚀 페이지 로드 완료");

        // 기본 요소들 확인
        console.log("📍 지도 컨테이너 확인:", document.getElementById("map"));
        console.log("🔧 config 객체:", config);
        console.log("🍽️ restaurants 배열:", restaurants.length + "개");

        // Kakao SDK 로드 상태 확인
        console.log("🗺️ Kakao 객체:", typeof kakao);
        if (typeof kakao !== "undefined") {
          console.log("🗺️ Kakao.maps 객체:", typeof kakao.maps);
        }

        // 즉시 Kakao Maps 초기화 시도
        initializeKakaoMaps();
      });
    </script>
  </body>
</html>

@echo off
echo ============================================================
echo    🚀 AI 음식점 방문 및 칼로리 관리 시스템 - Enhanced
echo ============================================================
echo.

:: 환경 확인
echo 📋 환경 확인 중...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python이 설치되지 않았습니다.
    echo    Python 3.8 이상을 설치해주세요.
    pause
    exit /b 1
)

:: 가상환경 활성화 (존재하는 경우)
if exist "venv\Scripts\activate.bat" (
    echo 🔧 가상환경 활성화 중...
    call venv\Scripts\activate.bat
)

:: 설정 파일 확인 및 생성
if not exist ".env" (
    echo ⚙️ 설정 파일 생성 중...
    if exist ".env.enhanced" (
        copy ".env.enhanced" ".env" >nul 2>&1
        echo ✅ .env 파일이 생성되었습니다.
    ) else (
        echo ⚠️ .env.enhanced 파일이 없습니다. 기본 설정을 사용합니다.
    )
) else (
    echo ✅ 설정 파일 확인됨
)

:: 의존성 확인
echo 📦 필수 패키지 확인 중...
python -c "import flask, geopy, requests; print('✅ 필수 패키지 확인 완료')" 2>nul
if errorlevel 1 (
    echo 📥 필수 패키지 설치 중...
    pip install flask geopy requests flask-socketio
)

:: 사용자 선택
echo.
echo 실행할 버전을 선택하세요:
echo   1. Enhanced 버전 (권장) - 개선된 위치 추적 및 자동 알림
echo   2. 기본 버전 - 원본 기능
echo   3. 테스트만 실행
echo   4. 대화형 테스트
echo.
set /p choice="선택 (1-4): "

if "%choice%"=="1" goto enhanced
if "%choice%"=="2" goto basic
if "%choice%"=="3" goto test_only
if "%choice%"=="4" goto interactive_test

:enhanced
echo.
echo 🚀 Enhanced 버전 시작 중...
echo ============================================================
echo 📍 주요 개선사항:
echo   • 감지 반경: 10m (기존 7m)
echo   • 체류 시간: 5초 (기존 10초)
echo   • 자동 리디렉션: 활성화 (3초 지연)
echo   • 실시간 디버깅: 활성화
echo   • 상세 로그: 활성화
echo ============================================================
echo.
echo 🌐 브라우저에서 http://localhost:5000 접속
echo 🎯 "위치 설정" 버튼으로 테스트 위치 변경 가능
echo 🔍 "디버그 모드" 버튼으로 상세 정보 확인 가능
echo.
python app_enhanced.py
goto end

:basic
echo.
echo 🚀 기본 버전 시작 중...
echo ============================================================
python app.py
goto end

:test_only
echo.
echo 🧪 기능 테스트 실행 중...
echo ============================================================
python test_location_tracking.py
echo.
echo 테스트 완료! 아무 키나 누르면 종료됩니다.
pause >nul
goto end

:interactive_test
echo.
echo 🎮 대화형 테스트 모드 시작...
echo ============================================================
echo 💡 팁: 음식점 근처 좌표를 입력해보세요
echo    예시: 35.837030,128.751693 (동궁찜닭)
echo          35.8365835,128.753000 (서브웨이)
echo ============================================================
python test_location_tracking.py interactive
goto end

:end
echo.
echo 👋 프로그램을 종료합니다.
if exist "venv\Scripts\deactivate.bat" (
    call venv\Scripts\deactivate.bat
)
pause

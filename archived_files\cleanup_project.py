#!/usr/bin/env python3
"""
프로젝트 파일 정리 스크립트
불필요한 테스트 파일, .bat 스크립트, 기타 개발용 파일들을 archived_files로 이동
"""

import os
import shutil
from pathlib import Path

def cleanup_project():
    """프로젝트 파일 정리"""
    
    project_root = Path(__file__).parent
    archive_dir = project_root / "archived_files"
    
    print("🧹 프로젝트 파일 정리 시작")
    print(f"📁 프로젝트 경로: {project_root}")
    print(f"📦 아카이브 경로: {archive_dir}")
    print("=" * 60)
    
    # 아카이브 디렉토리 생성
    archive_dir.mkdir(exist_ok=True)
    
    # 이동할 파일 패턴들
    files_to_archive = [
        # 테스트 파일들
        "test_*.py",
        "*_test.py", 
        "*test*.html",
        
        # .bat 스크립트들 (venv 제외)
        "*.bat",
        
        # 개발/디버깅 파일들
        "*debug*.py",
        "*temp*.py",
        "check_*.py",
        "fix_*.py",
        "verify_*.py",
        "try_*.py",
        
        # 설명서/가이드 (중복)
        "*GUIDE*.md",
        "*SETUP*.md",
        "*INSTALLATION*.md",
        "*TROUBLESHOOTING*.md",
        "*USAGE*.md",
        
        # 기타 개발용
        "generate_*.py",
        "keyword_*.py",
        
        # Enhanced 버전 (기본 app.py가 있으므로)
        "app_enhanced.py",
        "*_enhanced.*",
    ]
    
    moved_files = []
    skipped_files = []
    
    # 각 패턴에 대해 파일 찾아서 이동
    for pattern in files_to_archive:
        for file_path in project_root.glob(pattern):
            # 건드리면 안 되는 파일들 제외
            if should_skip_file(file_path, project_root):
                skipped_files.append(file_path.name)
                continue
                
            try:
                destination = archive_dir / file_path.name
                
                # 이미 존재하면 덮어쓰기
                if destination.exists():
                    destination.unlink()
                
                shutil.move(str(file_path), str(destination))
                moved_files.append(file_path.name)
                print(f"✅ 이동: {file_path.name}")
                
            except Exception as e:
                print(f"❌ 실패: {file_path.name} - {e}")
                skipped_files.append(file_path.name)
    
    # 빈 폴더들 정리 (logs 제외)
    empty_dirs = []
    for item in project_root.iterdir():
        if item.is_dir() and item.name not in ['venv', 'templates', 'uploads', 'logs', 'archived_files', '__pycache__']:
            try:
                if not any(item.iterdir()):  # 빈 폴더인지 확인
                    empty_dirs.append(item.name)
            except:
                pass
    
    # 요약 출력
    print("\n" + "=" * 60)
    print("📊 정리 완료 요약")
    print("=" * 60)
    print(f"📦 이동된 파일: {len(moved_files)}개")
    if moved_files:
        for file in sorted(moved_files):
            print(f"   📄 {file}")
    
    print(f"\n⏭️ 건너뛴 파일: {len(skipped_files)}개")
    if skipped_files:
        for file in sorted(skipped_files):
            print(f"   📄 {file}")
    
    if empty_dirs:
        print(f"\n📁 발견된 빈 폴더: {len(empty_dirs)}개")
        for dir_name in sorted(empty_dirs):
            print(f"   📁 {dir_name}")
        print("   (수동으로 삭제 검토 필요)")
    
    print(f"\n🎯 정리된 파일들은 archived_files 폴더에 보관되었습니다.")
    print("🚮 필요없다면 archived_files 폴더 전체를 삭제할 수 있습니다.")
    
    return len(moved_files)

def should_skip_file(file_path, project_root):
    """건드리면 안 되는 파일인지 확인"""
    
    # venv 폴더 내부 파일들
    if 'venv' in file_path.parts:
        return True
    
    # 핵심 실행 파일들
    essential_files = [
        'app.py',
        'config.py', 
        'exercise_data.py',
        'food_database.py',
        'nutrition_api.py',
        'restaurant_visitor.py',
        'user_profile.py',
    ]
    
    if file_path.name in essential_files:
        return True
    
    # 핵심 데이터 파일들
    important_data = [
        'receipt-ocr-key.json',
        'restaurants.json',
        'food_records.json',
        'user_profile.json',
        'food_data.db',
        '.env',
        '.env.example',
        'requirements.txt',
        'README.md',
    ]
    
    if file_path.name in important_data:
        return True
    
    return False

def show_final_structure():
    """정리 후 최종 구조 표시"""
    
    project_root = Path(__file__).parent
    
    print("\n" + "=" * 60)
    print("📁 정리 후 프로젝트 구조")
    print("=" * 60)
    
    essential_files = []
    for item in sorted(project_root.iterdir()):
        if item.is_file() and not item.name.startswith('.') and not item.name.endswith('.pyc'):
            essential_files.append(item.name)
        elif item.is_dir() and item.name not in ['__pycache__', 'venv']:
            essential_files.append(f"{item.name}/")
    
    for item in essential_files:
        if item.endswith('/'):
            print(f"📁 {item}")
        else:
            print(f"📄 {item}")
    
    print("\n✨ 프로젝트가 깔끔하게 정리되었습니다!")

if __name__ == "__main__":
    try:
        moved_count = cleanup_project()
        show_final_structure()
        
        print(f"\n🎉 총 {moved_count}개 파일이 정리되었습니다.")
        print("🚀 이제 python app.py로 서버를 실행할 수 있습니다!")
        
    except KeyboardInterrupt:
        print("\n⚠️ 사용자가 중단했습니다.")
    except Exception as e:
        print(f"\n❌ 오류 발생: {e}")
        import traceback
        traceback.print_exc()

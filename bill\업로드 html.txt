<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <title>영수증 OCR 칼로리/영양소 분석기</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        table { border-collapse: collapse; width: 100%; margin-top: 20px; }
        th, td { border: 1px solid #ccc; padding: 10px; text-align: center; }
        th { background-color: #f5f5f5; }
        .total { font-weight: bold; color: darkred; }
        .gray-box { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h2>📸 영수증 이미지 업로드</h2>
    <form action="/upload" method="post" enctype="multipart/form-data">
        <input name="file" type="file" required>
        <input type="submit" value="분석하기">
    </form>

    {% if result %}
        <hr>
        <h3>🔍 OCR 인식된 전체 텍스트:</h3>
        <pre class="gray-box">{{ result }}</pre>
    {% endif %}

    {% if food_infos and food_infos|length > 0 %}
        <hr>
        <h3>🍱 추출된 음식 및 영양소 정보</h3>
        <table>
            <tr>
                <th>음식명</th>
                <th>칼로리 (kcal)</th>
                <th>탄수화물 (g)</th>
                <th>단백질 (g)</th>
                <th>지방 (g)</th>
            </tr>
            {% for item in food_infos %}
            <tr>
                <td>{{ item.food }}</td>
                <td>{{ item.calories }}</td>
                <td>{{ item.carbs }}</td>
                <td>{{ item.protein }}</td>
                <td>{{ item.fat }}</td>
            </tr>
            {% endfor %}
        </table>
        <p class="total">🔥 총 칼로리: {{ total_calories }} kcal</p>

    {% elif foods and foods|length > 0 %}
        <hr>
        <h3>🍽️ 추출된 음식 목록 (영양 정보 없음)</h3>
        <ul>
            {% for food in foods %}
                <li>{{ food }}</li>
            {% endfor %}
        </ul>
    {% endif %}

    {% if result and (not foods or foods|length == 0) %}
        <hr>
        <p style="color: red;">🚫 음식명이 인식되지 않았습니다. OCR 결과를 확인하세요.</p>
    {% endif %}
</body>
</html>

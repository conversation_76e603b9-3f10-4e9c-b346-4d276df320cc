# 🍽️ AI 칼로리 관리 시스템 (완전 통합 버전)

## 🎯 프로젝트 개요

**실시간 GPS 기반 음식점 감지 + AI 음식 인식 + 스마트 운동 추천**이 통합된 차세대 칼로리 관리 시스템입니다.

### ✨ 주요 기능

- 🗺️ **실시간 위치 추적**: Kakao API 기반 음식점 자동 감지
- 🔔 **스마트 알림**: 음식점 체류 시 자동 알림 및 리디렉션
- 🤖 **AI 음식 인식**: 95%+ 정확도의 한국 음식 인식 시스템
- 📄 **영수증 OCR**: Google Vision API 기반 자동 음식 추출
- 🏃‍♂️ **운동 추천**: BMR/TDEE 기반 개인맞춤 운동 추천
- 📊 **영양 분석**: 주간 영양소 균형 분석 및 식단 제안
- ⏰ **자동 운동 알림**: 3번째 식사 또는 저녁 8시 이후 자동 알림

---

## 🚀 빠른 시작

### 1. 환경 설정

```bash
cd C:/2025_Project/ai-project-gps
pip install -r requirements.txt
```

### 2. 필수 API 키 설정

**`.env` 파일 생성:**

```env
# Kakao 장소 검색 API
KAKAO_REST_API_KEY=your_kakao_rest_api_key_here

# 식품의약품안전처 영양 API (선택)
MFDS_API_KEY=your_mfds_api_key_here

# AI 분석 서버 (analyzer-master)
ANALYZER_API_URL=http://localhost:5001

# 앱 설정
DETECTION_RADIUS=10.0
REQUIRED_STAY_TIME=5
```

### 3. Google Vision API 설정 (영수증 OCR용)

1. `receipt-ocr-key.json` 파일을 프로젝트 루트에 배치
2. Google Cloud Console에서 Vision API 활성화
3. 서비스 계정 키 다운로드

### 4. 서버 실행

```bash
# AI 분석 서버 (포트 5001)
cd C:/2025_Project/analyzer-master
python api_server.py

# 메인 앱 서버 (포트 5000)
cd C:/2025_Project/ai-project-gps
python app.py
```

### 5. 접속

- **메인 앱**: http://localhost:5000
- **운동 추천**: http://localhost:5000/exercise
- **대시보드**: http://localhost:5000/dashboard
- **프로필 설정**: http://localhost:5000/profile

---

## 🏗️ 시스템 아키텍처

```
[사용자] ←→ [메인앱:5000] ←→ [AI서버:5001]
   ↓              ↓                ↓
GPS/카메라    위치감지/OCR      AI음식인식
 ↓              ↓                ↓
자동알림      칼로리계산        95%정확도
 ↓              ↓                ↓
운동추천      영양분석         실시간분석
```

### 🔄 작동 흐름

1. **위치 감지**: 음식점 근처 도착 → 자동 알림
2. **음식 등록**: 사진 촬영 → AI 인식 → 영양정보 자동 계산
3. **운동 추천**: 3번째 식사 또는 저녁 8시 → 맞춤 운동 제안
4. **영양 분석**: 주간 데이터 → 식단 개선 제안

---

## 📁 프로젝트 구조

```
ai-project-gps/
├── 🔧 핵심 실행 파일
│   ├── app.py                 # 메인 Flask 서버
│   ├── exercise_data.py       # 운동 추천 시스템
│   ├── food_database.py       # 음식 데이터베이스
│   ├── nutrition_api.py       # 영양 API 연동
│   ├── restaurant_visitor.py  # 위치 추적 로직
│   └── user_profile.py        # 사용자 프로필 관리
│
├── 🎨 프론트엔드
│   └── templates/
│       ├── index.html         # 메인 지도 페이지
│       ├── exercise_recommendations.html  # 운동 추천
│       ├── dashboard.html     # 칼로리 대시보드
│       └── upload_food.html   # 음식 등록
│
├── 💾 데이터 파일
│   ├── receipt-ocr-key.json  # Google Vision API 키
│   ├── restaurants.json      # 음식점 데이터
│   ├── food_records.json     # 사용자 음식 기록
│   └── food_data.db          # SQLite 데이터베이스
│
├── 📦 정리된 파일들
│   └── archived_files/       # 테스트/개발용 파일들
│
└── 📄 설정 파일
    ├── .env                  # 환경변수
    ├── requirements.txt      # Python 의존성
    └── README.md            # 이 파일
```

---

## 🎯 기능별 상세 설명

### 🗺️ 실시간 위치 추적
- **Kakao Places API** 연동
- 10m 반경 내 음식점 자동 감지
- 5초 체류 시 자동 알림

### 🤖 AI 음식 인식
- **analyzer-master** 연동 (95%+ 정확도)
- 한국 음식 특화 CLIP 모델
- 실시간 영양정보 계산

### 🏃‍♂️ 스마트 운동 추천
- **BMR/TDEE 기반** 개인 맞춤 계산
- 10가지 운동별 필요 시간 제공
- 자동 알림: 3번째 식사 완료 시 또는 저녁 8시

### 📊 영양 분석
- 주간 탄수화물/단백질/지방 비율 분석
- AI 기반 식단 개선 제안
- 일일 목표 칼로리 추적

### 📄 영수증 OCR
- **Google Vision API** 기반
- 한국어 텍스트 인식 최적화
- 50+ 음식명 자동 추출

---

## ⚙️ 환경 설정 가이드

### API 키 발급 방법

**1. Kakao REST API**
1. https://developers.kakao.com 접속
2. 내 애플리케이션 → 애플리케이션 추가하기
3. 플랫폼 설정 → Web 플랫폼 추가
4. REST API 키 복사

**2. Google Vision API**
1. https://console.cloud.google.com 접속
2. 프로젝트 생성 → Vision API 활성화
3. 서비스 계정 생성 → 키 다운로드
4. `receipt-ocr-key.json`으로 저장

**3. 식품의약품안전처 API (선택)**
1. https://www.foodsafetykorea.go.kr/api 접속
2. 회원가입 → 인증키 신청
3. 영양성분 정보 API 키 발급

---

## 📱 사용법

### 1️⃣ 프로필 설정
1. `/profile` 접속
2. 신체정보, 목표, 활동량 입력
3. 자동 BMR/TDEE 계산

### 2️⃣ 위치 기반 음식 등록
1. 메인 페이지에서 GPS 권한 허용
2. 음식점 근처 이동 (5초 체류)
3. 자동 알림 → 음식 사진 촬영
4. AI 인식 결과 확인 → 저장

### 3️⃣ 수동 음식 등록
1. `/upload_food` 접속
2. 음식점명 입력 → 사진 업로드
3. AI 분석 결과 선택 → 저장

### 4️⃣ 영수증 분석
1. `/receipt` 접속
2. 영수증 사진 업로드
3. OCR 텍스트 인식 → 음식 추출
4. 영양정보 자동 계산 → 저장

### 5️⃣ 운동 추천
1. 3번째 식사 후 자동 알림
2. 또는 `/exercise` 직접 접속
3. 초과 칼로리 기반 운동 시간 확인
4. 주간 영양소 분석 결과 확인

### 6️⃣ 대시보드 확인
1. `/dashboard` 접속
2. 일일/주간 칼로리 추적
3. 목표 달성도 모니터링

---

## 🔧 문제 해결

### 일반적인 문제들

**❌ 위치 감지 안됨**
- GPS 권한 허용 확인
- Kakao API 키 설정 확인
- 음식점 반경 10m 내 위치 확인

**❌ AI 음식 인식 실패**
- analyzer-master 서버 실행 확인 (포트 5001)
- 사진 해상도 및 조명 확인
- 한국 음식인지 확인

**❌ 영수증 OCR 안됨**
- Google Vision API 키 설정 확인
- `pip install google-cloud-vision` 설치
- 영수증 이미지 선명도 확인

**❌ 운동 알림 안옴**
- 프로필 설정 완료 확인
- 3번째 식사 등록 또는 저녁 8시 이후 확인
- 브라우저 알림 권한 확인

---

## 🎯 성능 지표

- **AI 음식 인식**: 95%+ Top-3 정확도 (한국 음식)
- **위치 감지**: 10m 반경, 5초 지연시간
- **OCR 인식**: 50+ 음식명 자동 추출
- **응답 시간**: 평균 2-3초 (AI 분석 포함)

---

## 🚀 향후 계획

- [ ] 모바일 앱 버전 개발
- [ ] 음성 인식 음식 등록
- [ ] 소셜 기능 (친구 챌린지)
- [ ] 영양사 상담 연동
- [ ] 다국어 지원 확장

---

## 📞 개발자 정보

**주요 기술 스택**
- Backend: Python Flask, SocketIO
- Frontend: HTML5, JavaScript, Kakao Maps
- AI: PyTorch, CLIP, Transformers
- API: Kakao Places, Google Vision, 식약처 영양
- Database: SQLite, JSON

**개발 기간**: 2025.06.08 기준 완성
**버전**: 2.0.0 (운동 추천 통합)

---

## 📜 라이선스

이 프로젝트는 개인 학습 및 연구 목적으로 개발되었습니다.

**API 사용 주의사항**
- Kakao API: 일일 호출 제한 확인
- Google Vision API: 월간 무료 한도 확인
- 식약처 API: 호출 속도 제한 준수

---

**🎉 모든 기능이 통합된 완전한 AI 칼로리 관리 시스템입니다!**

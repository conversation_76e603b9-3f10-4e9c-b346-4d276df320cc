"""
Google Cloud Vision API 키 파일 위치 확인 스크립트
"""

import os
from pathlib import Path

def check_key_file_location():
    """키 파일 위치 확인"""
    
    print("🔍 Google Vision API 키 파일 위치 확인")
    print("=" * 50)
    
    # 현재 스크립트 위치
    current_dir = Path(__file__).parent
    print(f"📂 현재 폴더: {current_dir}")
    
    # 예상 키 파일 위치
    key_file_path = current_dir / 'receipt-ocr-key.json'
    print(f"🔑 예상 키 파일 위치: {key_file_path}")
    
    # 파일 존재 확인
    if key_file_path.exists():
        print("✅ receipt-ocr-key.json 파일이 올바른 위치에 있습니다!")
        
        # 파일 크기 확인
        file_size = key_file_path.stat().st_size
        print(f"📏 파일 크기: {file_size} bytes")
        
        if file_size > 100:  # JSON 파일은 보통 100바이트 이상
            print("✅ 파일 크기가 정상적입니다")
        else:
            print("⚠️ 파일 크기가 너무 작습니다. 파일이 제대로 다운로드되었는지 확인하세요")
            
        # 파일 내용 간단 검증
        try:
            import json
            with open(key_file_path, 'r', encoding='utf-8') as f:
                key_data = json.load(f)
                
            if 'type' in key_data and key_data['type'] == 'service_account':
                print("✅ 올바른 서비스 계정 키 파일입니다")
                print(f"📧 서비스 계정: {key_data.get('client_email', 'N/A')}")
                print(f"🏗️ 프로젝트 ID: {key_data.get('project_id', 'N/A')}")
            else:
                print("❌ 올바르지 않은 키 파일 형식입니다")
                
        except json.JSONDecodeError:
            print("❌ JSON 파일 형식이 올바르지 않습니다")
        except Exception as e:
            print(f"⚠️ 파일 검증 중 오류: {e}")
            
    else:
        print("❌ receipt-ocr-key.json 파일을 찾을 수 없습니다!")
        print("\n💡 해결 방법:")
        print("1. Google Cloud Console에서 서비스 계정 키를 다운로드하세요")
        print("2. 다운로드한 파일 이름을 'receipt-ocr-key.json'으로 변경하세요")
        print(f"3. 파일을 다음 위치로 이동하세요: {key_file_path}")
        
        # 현재 폴더의 다른 JSON 파일들 확인
        json_files = list(current_dir.glob('*.json'))
        if json_files:
            print(f"\n📁 현재 폴더의 JSON 파일들:")
            for json_file in json_files:
                print(f"   - {json_file.name}")
            print("   위 파일 중 하나가 Google Vision API 키 파일인가요?")
    
    print("\n" + "=" * 50)
    
    # 환경변수 확인
    env_key = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
    if env_key:
        print(f"🌍 환경변수 GOOGLE_APPLICATION_CREDENTIALS: {env_key}")
    else:
        print("🌍 환경변수 GOOGLE_APPLICATION_CREDENTIALS: 설정되지 않음")
    
    return key_file_path.exists()

if __name__ == "__main__":
    check_key_file_location()

"""
프론트엔드 알림 테스트용 JavaScript 코드
templates/index.html에 추가할 수 있는 코드입니다.
"""

# 프론트엔드 JavaScript 코드 (index.html에 추가)
test_notification_script = """
<script>
// 알림 테스트 버튼 추가
function addTestButtons() {
    const controlPanel = document.querySelector('#control-panel') || document.body;
    
    const testButtonsHTML = `
    <div id="notification-test-panel" style="margin: 10px; padding: 10px; border: 2px solid #007bff; border-radius: 5px; background: #f8f9fa;">
        <h5>🧪 알림 테스트</h5>
        <button onclick="testBasicNotification()" class="btn btn-primary btn-sm">기본 알림 테스트</button>
        <button onclick="testForceNotification()" class="btn btn-warning btn-sm">강제 알림 테스트</button>
        <button onclick="testBrowserNotification()" class="btn btn-info btn-sm">브라우저 알림 테스트</button>
        <div id="test-status" style="margin-top: 5px; font-size: 12px;"></div>
    </div>
    `;
    
    controlPanel.insertAdjacentHTML('beforeend', testButtonsHTML);
}

// 기본 알림 테스트
function testBasicNotification() {
    console.log('🧪 기본 알림 테스트 시작');
    updateTestStatus('기본 알림 테스트 중...');
    
    socket.emit('test_notification', {
        restaurant: '테스트 음식점'
    });
}

// 강제 알림 테스트
function testForceNotification() {
    console.log('🚨 강제 알림 테스트 시작');
    updateTestStatus('강제 알림 테스트 중...');
    
    socket.emit('force_notification_test', {
        restaurant: '강제 테스트 레스토랑'
    });
}

// 브라우저 알림 테스트
function testBrowserNotification() {
    console.log('🔔 브라우저 알림 테스트');
    updateTestStatus('브라우저 알림 테스트 중...');
    
    if (Notification.permission === 'granted') {
        new Notification('테스트 알림', {
            body: '브라우저 알림이 정상 작동합니다!',
            icon: '/static/icon-192x192.png'
        });
        updateTestStatus('브라우저 알림 전송 완료');
    } else {
        Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
                new Notification('테스트 알림', {
                    body: '브라우저 알림 권한이 허용되었습니다!',
                    icon: '/static/icon-192x192.png'
                });
                updateTestStatus('브라우저 알림 권한 허용됨');
            } else {
                updateTestStatus('브라우저 알림 권한이 거부됨');
            }
        });
    }
}

// 테스트 상태 업데이트
function updateTestStatus(message) {
    const statusDiv = document.getElementById('test-status');
    if (statusDiv) {
        statusDiv.innerHTML = `<span style="color: #007bff;">${new Date().toLocaleTimeString()}: ${message}</span>`;
    }
}

// 알림 이벤트 핸들러 강화
socket.on('notification', function(data) {
    console.log('🔔 알림 수신:', data);
    updateTestStatus(`알림 수신: ${data.restaurant}`);
    
    // 기존 알림 표시 로직
    showNotificationModal(data);
    
    // 추가: 브라우저 알림도 표시
    if (Notification.permission === 'granted') {
        new Notification(data.title || `${data.restaurant} 알림`, {
            body: data.message || `${data.restaurant}에서 음식을 등록하시겠습니까?`,
            icon: '/static/icon-192x192.png'
        });
    }
});

// 브라우저 알림 이벤트 핸들러
socket.on('browser_notification', function(data) {
    console.log('🔔 브라우저 알림 수신:', data);
    
    if (Notification.permission === 'granted') {
        new Notification(data.title, {
            body: data.body,
            icon: data.icon || '/static/icon-192x192.png'
        });
    }
});

// 알림 모달 표시 함수 (기존 함수 개선)
function showNotificationModal(data) {
    // 모달이 이미 있으면 제거
    const existingModal = document.getElementById('notification-modal');
    if (existingModal) {
        existingModal.remove();
    }
    
    // 새 모달 생성
    const modalHTML = `
    <div id="notification-modal" class="modal fade show" style="display: block; z-index: 9999;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning">
                    <h5 class="modal-title">🍽️ ${data.title || data.restaurant + ' 알림'}</h5>
                    <button type="button" class="btn-close" onclick="closeNotificationModal()"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center">
                        <h6>${data.restaurant}</h6>
                        <p class="mb-2">${data.category || ''}</p>
                        <div class="alert alert-info">
                            ${data.message.replace(/\\n/g, '<br>')}
                        </div>
                        <div class="row text-muted small">
                            <div class="col-6">거리: ${data.distance}m</div>
                            <div class="col-6">체류: ${data.stay_duration}초</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeNotificationModal()">나중에</button>
                    <button type="button" class="btn btn-primary" onclick="goToUpload('${data.restaurant}', '${data.upload_url || '/upload'}')">음식 등록</button>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-backdrop fade show" style="z-index: 9998;"></div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHTML);
    
    // 10초 후 자동 닫기
    setTimeout(() => {
        closeNotificationModal();
    }, 10000);
}

// 모달 닫기
function closeNotificationModal() {
    const modal = document.getElementById('notification-modal');
    const backdrop = document.querySelector('.modal-backdrop');
    
    if (modal) modal.remove();
    if (backdrop) backdrop.remove();
}

// 음식 등록 페이지로 이동
function goToUpload(restaurant, url) {
    closeNotificationModal();
    window.location.href = url || `/upload?restaurant=${encodeURIComponent(restaurant)}`;
}

// 페이지 로드 시 테스트 버튼 추가
document.addEventListener('DOMContentLoaded', function() {
    addTestButtons();
    
    // 브라우저 알림 권한 요청
    if (Notification.permission === 'default') {
        Notification.requestPermission();
    }
});

// 위치 상태 업데이트 핸들러 개선
socket.on('location_status', function(data) {
    console.log('📍 위치 상태 업데이트:', data);
    
    const statusDiv = document.getElementById('location-status') || document.getElementById('test-status');
    if (statusDiv && data.current_place) {
        const place = data.current_place;
        statusDiv.innerHTML = `
            <div style="color: green;">
                📍 현재 위치: ${place.name}<br>
                🏷️ 카테고리: ${place.category}<br>
                📏 거리: ${place.distance}m<br>
                ⏱️ 체류시간: ${place.stay_duration}초<br>
                🔔 알림전송: ${place.notification_sent ? '완료' : '대기중'}
            </div>
        `;
    }
});

console.log('🎯 알림 테스트 시스템 로드 완료');
</script>
""";

print("✅ 프론트엔드 알림 테스트 코드가 준비되었습니다!");
print("이 코드를 templates/index.html 파일의 </body> 태그 바로 앞에 추가하세요.")

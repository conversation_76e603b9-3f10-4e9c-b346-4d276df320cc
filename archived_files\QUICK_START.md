# 🚀 AI 음식점 방문 및 칼로리 관리 시스템 - 빠른 시작 가이드

## 📋 준비사항 체크리스트

### ✅ 완료된 구현사항
- [x] GPS 위치 조절 기능 (드래그, 클릭, GPS, 수동입력)
- [x] 식품의약품안전처 API 연동
- [x] AI 음식 인식 시스템
- [x] 영양정보 자동 계산
- [x] 칼로리 관리 대시보드
- [x] 실시간 위치 추적
- [x] 오류 처리 및 로깅
- [x] 데이터베이스 시스템
- [x] 샘플 데이터 생성기

## 🎯 5분 만에 시작하기

### 1단계: 환경설정
```bash
cd C:\2025_Project\ai-project-gps
```

### 2단계: 샘플 데이터 생성 (선택사항)
```bash
python generate_sample_data.py
```

### 3단계: 메인 서버 실행
```bash
python app.py
```

### 4단계: 웹 브라우저에서 테스트
- 메인 페이지: http://localhost:5000
- 대시보드: http://localhost:5000/dashboard

## 🔧 AI 분석 기능 사용 (고급)

### Analyzer 서버 실행 (음식 인식용)
```bash
# 별도 터미널에서 실행
cd C:\2025_Project\analyzer-master
python api_server.py
```

## 🍽️ 식품의약품안전처 API 설정 (고급)

### API 키 발급 및 설정
1. https://www.data.go.kr/ 회원가입
2. "식품의약품안전처_식품영양성분정보서비스" 검색 → 신청
3. 승인 후 API 키 설정:

```bash
# .env 파일 생성
copy .env.example .env
# .env 파일에서 MFDS_API_KEY=실제_API_키 입력
```

## 🧪 테스트 시나리오

### 기본 기능 테스트
1. **위치 추적 테스트**
   - "🎯 위치 설정" → "드래그 모드" 선택
   - 파란 마커를 음식점으로 드래그
   - 10초 후 알림 확인

2. **대시보드 확인**
   - 샘플 데이터로 차트 및 통계 확인
   - API 상태 모니터링

3. **음식 등록 테스트**
   - 음식 사진 업로드
   - AI 분석 결과 확인 (Analyzer API 필요)
   - 영양정보 저장

## 🔍 문제 해결

### 일반적인 문제
1. **모듈을 찾을 수 없음**: `pip install -r requirements.txt`
2. **데이터베이스 오류**: 앱 재시작으로 자동 초기화
3. **API 연결 실패**: 인터넷 연결 및 API 키 확인

### 로그 확인
```bash
tail -f app.log  # 실시간 로그 확인
```

## 📊 데이터 관리

### 샘플 데이터 관리
```bash
python generate_sample_data.py clear     # 모든 데이터 삭제
python generate_sample_data.py days=14   # 14일간 데이터 생성
python generate_sample_data.py stats     # 현재 상태 확인
```

## 🎯 핵심 기능 요약

### 1. 스마트 위치 추적
- 음식점 7m 이내 10초 체류시 자동 알림
- 다양한 위치 설정 방법 (테스트용)

### 2. AI 음식 인식
- 한국 음식 특화 + 일반 모델
- 상위 3개 후보 제시
- 신뢰도 표시

### 3. 정확한 영양정보
- 식품의약품안전처 공식 데이터
- 자동 칼로리 계산
- 영양소 분석

### 4. 통합 대시보드
- 일일/주간 통계
- 시각적 차트
- 실시간 상태 모니터링

## 🎉 프로젝트 완성!

이제 모든 기능이 구현되었습니다:
- ✅ GPS 위치 조절 기능
- ✅ 식품의약품안전처 API 연동
- ✅ 향상된 사용자 인터페이스
- ✅ 오류 처리 및 로깅
- ✅ 테스트 데이터 및 가이드

**즐거운 칼로리 관리 되세요! 🍽️📊**

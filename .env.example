# AI 음식점 방문 및 칼로리 관리 시스템 - 환경변수 설정 예시

# =============================================================================
# 식품의약품안전처 API 설정
# =============================================================================
# 공공데이터포털에서 발급받은 API 키를 입력하세요
# 신청 방법: https://www.data.go.kr/ → "식품의약품안전처_식품영양성분정보서비스" 검색
MFDS_API_KEY=여기에_실제_API_키를_입력하세요

# =============================================================================
# Flask 애플리케이션 설정
# =============================================================================
# 개발/운영 모드 설정
FLASK_ENV=development
FLASK_DEBUG=True

# 보안 키 (실제 운영시에는 랜덤한 문자열로 변경)
SECRET_KEY=your-secret-key-here

# =============================================================================
# 데이터베이스 설정
# =============================================================================
# SQLite 데이터베이스 파일 경로
DATABASE_PATH=food_data.db

# =============================================================================
# 외부 API 서버 설정
# =============================================================================
# Food Analyzer API 서버 주소
ANALYZER_API_URL=http://localhost:5001

# API 요청 타임아웃 (초)
API_TIMEOUT=30

# =============================================================================
# 파일 업로드 설정
# =============================================================================
# 업로드 폴더 경로
UPLOAD_FOLDER=uploads

# 최대 파일 크기 (바이트) - 현재: 10MB
MAX_FILE_SIZE=10485760

# 허용 파일 확장자
ALLOWED_EXTENSIONS=png,jpg,jpeg,gif,webp

# =============================================================================
# 위치 추적 설정
# =============================================================================
# 음식점 감지 거리 (미터)
DETECTION_RADIUS=7

# 음식점 체류 시간 (초)
REQUIRED_STAY_TIME=10

# =============================================================================
# 사용 방법
# =============================================================================
# 1. 이 파일을 .env로 복사하세요: cp .env.example .env
# 2. 실제 API 키와 설정값으로 수정하세요
# 3. .env 파일은 Git에 커밋하지 마세요 (.gitignore에 포함됨)

# =============================================================================
# API 키 발급 가이드
# =============================================================================
# 1. 공공데이터포털 (https://www.data.go.kr/) 회원가입
# 2. 로그인 후 "식품의약품안전처_식품영양성분정보서비스" 검색
# 3. "활용신청" 클릭 → 목적: 교육/연구 → 신청
# 4. 승인 후 "마이페이지" → "개발계정"에서 인증키 확인
# 5. 위의 MFDS_API_KEY에 인증키 입력

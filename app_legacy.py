# -*- coding: utf-8 -*-
"""
AI Restaurant Visit and Calorie Management System
Enhanced version with Korean Food Nutrition API integration and improved error handling
"""

from flask import Flask, render_template, request, jsonify, redirect, url_for
from flask_socketio import SocketIO
import json
import time
import requests
import base64
import os
from datetime import datetime, date, timedelta
from restaurant_visitor import RestaurantVisitor
from food_database import db
from nutrition_api import nutrition_api, get_food_nutrition, search_foods, check_api_status
from user_profile import profile_manager
from exercise_data import exercise_system, get_exercise_recommendations, analyze_nutrition, check_exercise_reminder
import logging
from werkzeug.utils import secure_filename
from pathlib import Path
from urllib.parse import urlencode

# 영수증 OCR을 위한 라이브러리 (optional)
try:
    from google.cloud import vision
    GOOGLE_VISION_AVAILABLE = True
    # Google Cloud Vision API 키 설정
    google_key_path = Path(__file__).parent / 'receipt-ocr-key.json'
    if google_key_path.exists():
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = str(google_key_path)
        print(f"✅ Google Cloud Vision API 키 파일 설정: {google_key_path}")
    else:
        print(f"⚠️ Google Cloud Vision API 키 파일을 찾을 수 없음: {google_key_path}")
except ImportError:
    GOOGLE_VISION_AVAILABLE = False
    print("⚠️ Google Cloud Vision API가 설치되지 않아 영수증 OCR 기능을 사용할 수 없습니다")
    print("💡 설치 방법: pip install google-cloud-vision")

# .env 파일 로드 (강화된 버전)
def load_env_file():
    """환경변수 파일을 강제로 로드"""
    env_loaded = False
    
    # 1. python-dotenv로 시도
    try:
        from dotenv import load_dotenv
        result = load_dotenv()
        if result:
            print("✅ python-dotenv로 .env 파일 로드 성공")
            env_loaded = True
        else:
            print("⚠️ python-dotenv는 있지만 .env 파일을 찾지 못함")
    except ImportError:
        print("⚠️ python-dotenv가 설치되지 않음. 수동 로드 시도...")
    
    # 2. 수동 로드 (python-dotenv가 없거나 실패한 경우)
    if not env_loaded:
        env_path = Path(__file__).parent / '.env'
        if env_path.exists():
            print(f"📁 .env 파일 경로: {env_path}")
            try:
                with open(env_path, 'r', encoding='utf-8') as f:
                    loaded_count = 0
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            # 키=값 분리 (값에 =가 있을 수 있음)
                            key, value = line.split('=', 1)
                            key = key.strip()
                            value = value.strip()
                            
                            # 값에서 주석 제거
                            if '#' in value:
                                value = value.split('#')[0].strip()
                            
                            # 따옴표 제거 (있는 경우)
                            if value.startswith('"') and value.endswith('"'):
                                value = value[1:-1]
                            elif value.startswith("'") and value.endswith("'"):
                                value = value[1:-1]
                            
                            # 환경변수 설정
                            os.environ[key] = value
                            loaded_count += 1
                            print(f"  {key} = {value[:20]}{'...' if len(value) > 20 else ''}")
                
                print(f"✅ .env 파일 수동 로드 성공 ({loaded_count}개 변수)")
                env_loaded = True
                
            except Exception as e:
                print(f"❌ .env 파일 읽기 오류: {e}")
        else:
            print(f"❌ .env 파일을 찾을 수 없음: {env_path}")
    
    return env_loaded

# 환경변수 로드 실행
env_success = load_env_file()

# Enhanced logging setup
def setup_logging():
    """로깅 시스템 설정"""
    log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    
    # 콘솔 로깅
    logging.basicConfig(
        level=logging.DEBUG,  # INFO -> DEBUG로 변경하여 더 상세한 로그
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('app.log', encoding='utf-8')
        ]
    )
    
    # 외부 라이브러리 로깅 레벨 조정
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)

setup_logging()
logger = logging.getLogger(__name__)

app = Flask(__name__)
socketio = SocketIO(app, cors_allowed_origins='*')

# 환경변수 안전 파싱 함수
def safe_getenv(key, default, value_type=str):
    """환경변수를 안전하게 파싱하여 주석을 제거하고 적절한 타입으로 변환"""
    try:
        value = os.getenv(key, default)
        # 주석 제거 (# 이후 모든 내용 제거)
        if isinstance(value, str) and '#' in value:
            value = value.split('#')[0].strip()
        
        # 타입 변환
        if value_type == float:
            return float(value)
        elif value_type == int:
            return int(value)
        else:
            return str(value)
    except (ValueError, TypeError) as e:
        logger.warning(f"⚠️ 환경변수 '{key}' 파싱 실패, 기본값 사용: {default} (오류: {e})")
        return value_type(default) if value_type != str else default

# Kakao 장소 검색 API 설정
KAKAO_REST_API_KEY = safe_getenv('KAKAO_REST_API_KEY', 'YOUR_KAKAO_REST_API_KEY')
KAKAO_PLACES_API_URL = "https://dapi.kakao.com/v2/local/search/category.json"

# API 키 디버깅 정보
print(f"🔑 Kakao API 키 디버깅:")
print(f"   - 환경변수에서 읽은 값: {os.getenv('KAKAO_REST_API_KEY', 'NOT_SET')}")
print(f"   - safe_getenv로 처리된 값: {KAKAO_REST_API_KEY}")
print(f"   - API 키 설정 상태: {'✅ 설정완료' if KAKAO_REST_API_KEY != 'YOUR_KAKAO_REST_API_KEY' else '❌ 미설정'}")

def check_current_location_is_restaurant(lat, lon, radius=10):
    """
    현재 위치가 음식점/카페인지 Kakao 장소 검색 API로 확인
    
    Args:
        lat (float): 위도
        lon (float): 경도  
        radius (int): 검색 반경 (미터)
    
    Returns:
        dict: {
            'is_restaurant': bool,
            'place_name': str,
            'category': str,
            'distance': float,
            'confidence': float
        }
    """
    try:
        if not KAKAO_REST_API_KEY or KAKAO_REST_API_KEY == 'YOUR_KAKAO_REST_API_KEY':
            logger.warning("⚠️ Kakao REST API 키가 설정되지 않았습니다")
            return {
                'is_restaurant': False,
                'error': 'API key not configured'
            }
        
        # Kakao 장소 검색 API 파라미터
        # CE7: 카페, FD6: 음식점 카테고리
        categories = ['CE7', 'FD6']  # 카페, 음식점
        
        found_places = []
        
        for category in categories:
            params = {
                'category_group_code': category,
                'x': lon,  # 경도
                'y': lat,  # 위도 
                'radius': radius,  # 검색 반경 (미터)
                'sort': 'distance'  # 거리순 정렬
            }
            
            headers = {
                'Authorization': f'KakaoAK {KAKAO_REST_API_KEY}'
            }
            
            logger.debug(f"🔍 Kakao API 호출: {category} 카테고리, 반경 {radius}m")
            
            response = requests.get(
                KAKAO_PLACES_API_URL,
                params=params,
                headers=headers,
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                places = data.get('documents', [])
                
                logger.debug(f"📍 {category} 검색 결과: {len(places)}개 장소")
                
                for place in places:
                    distance = float(place.get('distance', 999))
                    if distance <= radius:  # 설정된 반경 내에 있는 장소만
                        found_places.append({
                            'place_name': place.get('place_name', ''),
                            'category_name': place.get('category_name', ''),
                            'distance': distance,
                            'category_code': category,
                            'address': place.get('address_name', ''),
                            'road_address': place.get('road_address_name', ''),
                            'phone': place.get('phone', ''),
                            'place_url': place.get('place_url', '')
                        })
            else:
                logger.error(f"❌ Kakao API 오류: {response.status_code} - {response.text}")
        
        if found_places:
            # 가장 가까운 장소 선택
            closest_place = min(found_places, key=lambda x: x['distance'])
            
            # 신뢰도 계산 (거리가 가까울수록 높음)
            confidence = max(0.1, min(1.0, 1.0 - (closest_place['distance'] / radius)))
            
            logger.info(f"✅ 음식점/카페 감지: {closest_place['place_name']} ({closest_place['distance']}m)")
            
            return {
                'is_restaurant': True,
                'place_name': closest_place['place_name'],
                'category': closest_place['category_name'],
                'distance': closest_place['distance'],
                'confidence': confidence,
                'address': closest_place['address'],
                'road_address': closest_place['road_address'],
                'phone': closest_place['phone'],
                'place_url': closest_place['place_url'],
                'all_places': found_places  # 모든 감지된 장소들
            }
        else:
            logger.debug(f"📍 주변 {radius}m 내에 음식점/카페 없음")
            return {
                'is_restaurant': False,
                'message': f'주변 {radius}m 내에 음식점/카페가 없습니다'
            }
            
    except requests.exceptions.Timeout:
        logger.error("❌ Kakao API 타임아웃")
        return {
            'is_restaurant': False,
            'error': 'API timeout'
        }
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Kakao API 요청 오류: {e}")
        return {
            'is_restaurant': False,
            'error': f'API request failed: {str(e)}'
        }
    except Exception as e:
        logger.error(f"❌ 장소 검색 중 예상치 못한 오류: {e}")
        return {
            'is_restaurant': False,
            'error': f'Unexpected error: {str(e)}'
        }

# Configuration management - 문제 해결을 위한 설정 개선
class Config:
    """애플리케이션 설정 관리"""
    
    # File upload settings
    UPLOAD_FOLDER = safe_getenv('UPLOAD_FOLDER', 'uploads')
    ALLOWED_EXTENSIONS = set(safe_getenv('ALLOWED_EXTENSIONS', 'png,jpg,jpeg,gif,webp').split(','))
    MAX_FILE_SIZE = safe_getenv('MAX_FILE_SIZE', 10 * 1024 * 1024, int)  # 10MB
    
    # API settings
    ANALYZER_API_URL = safe_getenv('ANALYZER_API_URL', 'http://localhost:5001')
    API_TIMEOUT = safe_getenv('API_TIMEOUT', 30, int)
    
    # Security
    SECRET_KEY = safe_getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    
    # Database
    DATABASE_PATH = safe_getenv('DATABASE_PATH', 'food_data.db')
    
    # Location tracking - 설정 개선
    DETECTION_RADIUS = safe_getenv('DETECTION_RADIUS', 10.0, float)  # 10m (더 정확한 감지)
    REQUIRED_STAY_TIME = safe_getenv('REQUIRED_STAY_TIME', 5, int)   # 5초 체류

# Apply configuration
app.config.update(vars(Config))

# Create necessary directories
def ensure_directories():
    """필요한 디렉토리 생성"""
    directories = [Config.UPLOAD_FOLDER, 'logs']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.debug(f"📁 Directory ensured: {directory}")

ensure_directories()

# 식품의약품안전처 API 키 설정
def setup_nutrition_api():
    """영양정보 API 설정"""
    api_key = safe_getenv('MFDS_API_KEY', 'YOUR_API_KEY_HERE')
    if api_key and api_key != 'YOUR_API_KEY_HERE':
        nutrition_api.api_key = api_key
        logger.info("🔑 식품의약품안전처 API 키가 설정되었습니다")
        return True
    else:
        logger.warning("⚠️ 식품의약품안전처 API 키가 설정되지 않았습니다. 기본 데이터베이스를 사용합니다.")
        return False

api_key_configured = setup_nutrition_api()

# Visitor logic and restaurant data loading
def load_restaurant_data():
    """음식점 데이터 로드"""
    try:
        restaurant_file = Path(__file__).parent / 'restaurants.json'
        with open(restaurant_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        logger.info(f"✅ 음식점 데이터 로드 완료: {len(data)}개 음식점")
        return data
    except FileNotFoundError:
        logger.error("❌ restaurants.json 파일을 찾을 수 없습니다")
        return []
    except json.JSONDecodeError as e:
        logger.error(f"❌ restaurants.json 파일 파싱 오류: {e}")
        return []

visitor = RestaurantVisitor()
restaurant_list = load_restaurant_data()
visitor.restaurants = {r['name']: (r['lat'], r['lon']) for r in restaurant_list}

# 위치 추적 설정 로그
logger.info(f"🎯 위치 추적 설정: 감지 반경 {Config.DETECTION_RADIUS}m, 체류 시간 {Config.REQUIRED_STAY_TIME}초")
logger.info(f"📍 로드된 음식점: {len(visitor.restaurants)}개")

# === Enhanced SocketIO Event Handlers for Location Tracking ===

@socketio.on('update_location')
def handle_location(data):
    """사용자 위치 업데이트 처리 - 개선된 알림 시스템"""
    try:
        lat = float(data['lat'])
        lon = float(data['lon'])
        current_time = time.time()
        
        logger.debug(f"📍 위치 업데이트 수신: {lat:.6f}, {lon:.6f}")
        
        # 현재 위치가 음식점/카페인지 실시간 검색
        place_info = check_current_location_is_restaurant(
            lat, lon, 
            radius=Config.DETECTION_RADIUS
        )
        
        if place_info.get('is_restaurant', False):
            # 음식점/카페 감지됨!
            place_name = place_info['place_name']
            category = place_info['category']
            distance = place_info['distance']
            confidence = place_info['confidence']
            
            logger.info(f"✅ 음식점/카페 감지: {place_name} ({category}, {distance:.1f}m, 신뢰도: {confidence:.2f})")
            
            # 방문 상태 확인 및 처리
            is_new_visit = place_name not in visitor.visit_times
            stay_duration = 0
            
            if is_new_visit:
                # 🆕 새로 감지된 장소
                visitor.visit_times[place_name] = current_time
                logger.info(f"🚶‍♂️ {place_name} 입장 감지: {distance:.1f}m")
                
                # 입장 상태 업데이트 전송
                socketio.emit('status_update', {
                    'restaurant': place_name,
                    'category': category,
                    'status': 'entered',
                    'distance': round(distance, 1),
                    'confidence': round(confidence, 2),
                    'timestamp': datetime.now().strftime('%H:%M:%S'),
                    'address': place_info.get('address', ''),
                    'detection_method': 'real_time_api'
                })
            else:
                # 기존 방문 장소 - 체류 시간 계산
                stay_duration = current_time - visitor.visit_times[place_name]
                logger.debug(f"🕰️ {place_name} 체류 시간: {stay_duration:.1f}초")
                
                # 알림 조건 확인
                notification_needed = (
                    stay_duration >= Config.REQUIRED_STAY_TIME and 
                    place_name not in visitor.notification_sent
                )
                
                if notification_needed:
                    # 🔔 알림 전송 (한 번만)
                    visitor.notification_sent.add(place_name)
                    
                    logger.info(f"🔔 {place_name} 알림 전송! 체류 시간: {stay_duration:.1f}초")
                    
                    # 사용자 정의 알림 메시지 생성
                    notification_message = {
                        'type': 'restaurant_notification',
                        'restaurant': place_name,
                        'category': category,
                        'title': f'{place_name} 알림',
                        'message': f'{place_name}({category})에 {Config.REQUIRED_STAY_TIME}초 이상 머물고 있습니다.\n\n음식을 등록하시겠습니까?',
                        'upload_url': url_for('upload_food', restaurant=place_name),
                        'distance': round(distance, 1),
                        'stay_duration': round(stay_duration, 1),
                        'confidence': round(confidence, 2),
                        'address': place_info.get('address', ''),
                        'phone': place_info.get('phone', ''),
                        'detection_method': 'real_time_api',
                        'timestamp': datetime.now().isoformat()
                    }
                    
                    # 알림 전송 (한 번만)
                    socketio.emit('notification', notification_message)
                    
                    logger.info(f"📤 알림 전송 완료: {place_name}")
                else:
                    # 아직 알림 시간 다 안됨
                    remaining_time = Config.REQUIRED_STAY_TIME - stay_duration
                    if remaining_time > 0:
                        logger.debug(f"⏰ {place_name} 알림까지 {remaining_time:.1f}초 남음")
            
            # 현재 위치 상태 업데이트 전송
            current_stay_duration = current_time - visitor.visit_times.get(place_name, current_time)
            
            socketio.emit('location_status', {
                'is_in_restaurant': True,
                'current_place': {
                    'name': place_name,
                    'category': category,
                    'distance': round(distance, 1),
                    'confidence': round(confidence, 2),
                    'address': place_info.get('address', ''),
                    'stay_duration': round(current_stay_duration, 1),
                    'notification_sent': place_name in visitor.notification_sent
                },
                'all_nearby_places': place_info.get('all_places', [])[:3],
                'detection_radius': Config.DETECTION_RADIUS,
                'required_stay_time': Config.REQUIRED_STAY_TIME,
                'current_position': {'lat': lat, 'lon': lon}
            })
        
        else:
            # 음식점/카페가 아닌 곳
            logger.debug(f"📍 음식점/카페 외부 위치: {place_info.get('message', '주변에 음식점 없음')}")
            
            # 방문 기록 정리 및 퇴장 처리
            left_places = []
            for place_name in list(visitor.visit_times.keys()):
                stay_duration = current_time - visitor.visit_times[place_name]
                del visitor.visit_times[place_name]
                left_places.append({
                    'name': place_name,
                    'stay_duration': round(stay_duration, 1)
                })
                
                logger.info(f"👋 {place_name} 퇴장: {stay_duration:.1f}초 체류")
                
                # 퇴장 상태 업데이트 전송
                socketio.emit('status_update', {
                    'restaurant': place_name,
                    'status': 'left',
                    'stay_duration': round(stay_duration, 1),
                    'timestamp': datetime.now().strftime('%H:%M:%S')
                })
            
            # 알림 상태 초기화
            visitor.notification_sent.clear()
            
            # 외부 위치 상태 업데이트 전송
            socketio.emit('location_status', {
                'is_in_restaurant': False,
                'current_place': None,
                'left_places': left_places,
                'message': place_info.get('message', '주변에 음식점/카페가 없습니다'),
                'detection_radius': Config.DETECTION_RADIUS,
                'current_position': {'lat': lat, 'lon': lon},
                'error': place_info.get('error')
            })
                    
    except (ValueError, KeyError) as e:
        logger.error(f"❌ 위치 데이터 처리 오류: {e}")
        socketio.emit('error', {'message': '위치 데이터가 올바르지 않습니다'})
    except Exception as e:
        logger.error(f"❌ 위치 처리 중 예상치 못한 오류: {e}")
        socketio.emit('error', {'message': f'위치 처리 오류: {str(e)}'})

@socketio.on('connect')
def handle_connect():
    """클라이언트 연결"""
    logger.info(f"🔌 클라이언트 연결: {request.sid}")
    
    # 연결 시 현재 설정 정보 전송
    socketio.emit('config_info', {
        'detection_radius': Config.DETECTION_RADIUS,
        'required_stay_time': Config.REQUIRED_STAY_TIME,
        'restaurant_count': len(visitor.restaurants)
    })

@socketio.on('disconnect')
def handle_disconnect():
    """클라이언트 연결 해제"""
    logger.info(f"🔌 클라이언트 연결 해제: {request.sid}")

# 강제 알림 테스트 버튼 추가
@socketio.on('force_notification_test')
def handle_force_notification_test(data):
    """강제 알림 테스트 (디버깅용) - 한 번만"""
    try:
        restaurant_name = data.get('restaurant', '테스트 음식점')
        logger.info(f"🚨 강제 알림 테스트: {restaurant_name}")
        
        # 알림 전송 (한 번만)
        socketio.emit('notification', {
            'type': 'restaurant_notification',
            'restaurant': restaurant_name,
            'category': '테스트 카테고리',
            'title': f'{restaurant_name} 테스트 알림',
            'message': f'{restaurant_name}에서 테스트 알림입니다!\n\n음식을 등록하시겠습니까?',
            'upload_url': url_for('upload_food', restaurant=restaurant_name),
            'distance': 5.0,
            'stay_duration': 10.0,
            'confidence': 1.0,
            'address': '테스트 주소',
            'phone': '010-0000-0000',
            'detection_method': 'force_test',
            'timestamp': datetime.now().isoformat(),
            'test': True
        })
        
        logger.info(f"📤 강제 알림 전송 완료: {restaurant_name}")
        
    except Exception as e:
        logger.error(f"❌ 강제 알림 테스트 오류: {e}")

@socketio.on('test_notification')
def handle_test_notification(data):
    """기존 테스트 알림 강화"""
    try:
        restaurant_name = data.get('restaurant', '테스트 음식점')
        logger.info(f"🧪 테스트 알림 요청: {restaurant_name}")
        
        # 알림 전송
        socketio.emit('notification', {
            'type': 'restaurant_notification',
            'restaurant': restaurant_name,
            'category': '테스트',
            'title': f'{restaurant_name} 테스트',
            'message': f'{restaurant_name}에서 테스트 알림입니다.',
            'upload_url': url_for('upload_food', restaurant=restaurant_name),
            'distance': 5.0,
            'stay_duration': 10.0,
            'test': True,
            'timestamp': datetime.now().isoformat()
        })
        
        logger.info(f"📤 테스트 알림 전송 완료: {restaurant_name}")
        
    except Exception as e:
        logger.error(f"❌ 테스트 알림 처리 오류: {e}")

# === Route Definitions (간소화) ===

@app.route('/')
def index():
    """메인 페이지 - 지도 및 위치 추적 + 오늘 요약"""
    try:
        # 음식 기록 불러오기
        records = load_food_records()
        
        # 날짜별 분석
        today = datetime.now().date()
        today_records = [r for r in records if datetime.fromisoformat(r['timestamp']).date() == today]
        
        # 오늘 요약 (안전한 숫자 변환 적용)
        today_summary = {
            'date': today.strftime('%Y-%m-%d'),
            'total_calories': sum(safe_float_conversion(r.get('calories', 0)) for r in today_records),
            'total_protein': sum(safe_float_conversion(r.get('protein', 0)) for r in today_records),
            'total_carbs': sum(safe_float_conversion(r.get('carbs', 0)) for r in today_records),
            'total_fat': sum(safe_float_conversion(r.get('fat', 0)) for r in today_records),
            'meal_count': len(today_records)
        }
        
        # 설정 정보를 템플릿에 전달
        config_info = {
            'detection_radius': Config.DETECTION_RADIUS,
            'required_stay_time': Config.REQUIRED_STAY_TIME,
            'restaurant_count': len(restaurant_list)
        }
        return render_template('index.html', 
                             restaurants=restaurant_list, 
                             config=config_info,
                             today=today_summary)
    except Exception as e:
        logger.error(f"❌ 메인 페이지 로드 오류: {e}")
        return f"페이지 로드 중 오류가 발생했습니다: {e}", 500

@app.route('/upload')
@app.route('/upload_food', methods=['GET', 'POST'])
def upload_food():
    """음식 등록 페이지 및 처리"""
    if request.method == 'GET':
        # 음식 등록 페이지 표시
        restaurant_name = request.args.get('restaurant', None)
        return render_template('upload_food.html', restaurant_name=restaurant_name)
    
    # POST: 이미지 업로드 및 분석
    try:
        logger.info("📝 음식 등록 요청 수신")
        
        # 요청 데이터 확인
        restaurant_name = request.form.get('restaurant_name', '알 수 없는 음식점')
        
        if 'food_image' not in request.files:
            return jsonify({
                'success': False,
                'error': '이미지 파일이 업로드되지 않았습니다.'
            }), 400
        
        file = request.files['food_image']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '파일이 선택되지 않았습니다.'
            }), 400
        
        # 파일 저장
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"{timestamp}_{filename}"
            
            upload_folder = Config.UPLOAD_FOLDER
            os.makedirs(upload_folder, exist_ok=True)
            
            file_path = os.path.join(upload_folder, filename)
            file.save(file_path)
            
            logger.info(f"💾 이미지 저장: {file_path}")
            
            # Analyzer API로 분석 요청
            try:
                analysis_result = analyze_food_image(file_path)
                
                if analysis_result.get('success'):
                    # 분석 성공 - 선택 UI 표시
                    logger.info(f"✅ 음식 분석 성공: {analysis_result.get('method', 'unknown')}")
                    
                    return jsonify({
                        'success': True,
                        'requires_selection': True,
                        'analysis_result': analysis_result,
                        'image_path': file_path,
                        'restaurant_name': restaurant_name
                    })
                else:
                    # 분석 실패
                    logger.warning(f"⚠️ 음식 분석 실패: {analysis_result.get('error', 'Unknown error')}")
                    
                    return jsonify({
                        'success': False,
                        'error': '음식 분석에 실패했습니다. 다른 사진으로 다시 시도해주세요.',
                        'debug_info': analysis_result
                    }), 400
                    
            except Exception as api_error:
                logger.error(f"❌ Analyzer API 오류: {api_error}")
                return jsonify({
                    'success': False,
                    'error': 'AI 분석 서비스에 연결할 수 없습니다. 서버 상태를 확인해주세요.',
                    'debug_info': {
                        'api_url': Config.ANALYZER_API_URL,
                        'error': str(api_error)
                    }
                }), 500
        else:
            return jsonify({
                'success': False,
                'error': '지원되지 않는 파일 형식입니다. (JPG, PNG, GIF, WebP 지원)'
            }), 400
            
    except Exception as e:
        logger.error(f"❌ 음식 등록 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'서버 오류가 발생했습니다: {str(e)}'
        }), 500

@app.route('/confirm_food', methods=['POST'])
def confirm_food():
    """선택된 음식 확인 및 저장"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'JSON 데이터가 필요합니다.'
            }), 400
        
        restaurant_name = data.get('restaurant_name', '알 수 없는 음식점')
        selected_food = data.get('selected_food')
        custom_food_name = data.get('custom_food_name', '').strip()
        custom_weight = data.get('custom_weight')
        
        # 음식 정보 결정
        if custom_food_name:
            # 사용자 직접 입력
            food_name = custom_food_name
            food_name_ko = custom_food_name  # 한글 이름 별도 저장
            food_name_en = None
            weight = float(custom_weight) if custom_weight else 150.0
            
            # 기본 영양정보 추정
            nutrition = {
                'calories': weight * 1.5,  # 추정 칼로리
                'protein': weight * 0.1,
                'carbs': weight * 0.2,
                'fat': weight * 0.05,
                'weight_grams': weight
            }
            nutrition_source = 'user_input'
            
        elif selected_food:
            # AI 분석 결과 사용
            food_name_ko = selected_food.get('food_name_ko')
            food_name_en = selected_food.get('food_name_en')
            # 한글 이름이 있으면 우선 사용, 없으면 영문 이름 사용
            food_name = food_name_ko if food_name_ko else (food_name_en if food_name_en else '알 수 없는 음식')
            nutrition = selected_food.get('nutrition', {})
            nutrition_source = selected_food.get('nutrition_source', 'ai_analysis')
            
        else:
            return jsonify({
                'success': False,
                'error': '음식을 선택하거나 직접 입력해주세요.'
            }), 400
        
        # 데이터베이스에 저장
        food_record = {
            'restaurant_name': restaurant_name,
            'food_name': food_name,  # 표시용 이름
            'food_name_ko': food_name_ko,  # 한글 이름 별도 저장
            'food_name_en': food_name_en,  # 영문 이름 별도 저장
            'calories': nutrition.get('calories', 0),
            'protein': nutrition.get('protein', 0),
            'carbs': nutrition.get('carbs', 0),
            'fat': nutrition.get('fat', 0),
            'weight_grams': nutrition.get('weight_grams', 150),
            'nutrition_source': nutrition_source,
            'confidence': selected_food.get('confidence', 0.0) if selected_food else 0.0,  # confidence 추가
            'timestamp': datetime.now().isoformat(),
            'image_path': data.get('image_path')
        }
        
        # DB 저장 (실제 데이터베이스 구현 필요)
        record_id = save_food_record(food_record)
        
        logger.info(f"✅ 음식 등록 완료: {food_name} ({nutrition.get('calories', 0):.0f} kcal)")
        
        # 🏃‍♂️ 운동 알림 자동 체크
        try:
            # 운동 알림 시스템이 필요한 경우 여기에 구현
            pass
        except Exception as ex:
            logger.warning(f"⚠️ 운동 알림 체크 실패: {ex}")
        
        return jsonify({
            'success': True,
            'message': '음식이 성공적으로 등록되었습니다!',
            'record_id': record_id,
            'food_name': food_name,
            'calories': nutrition.get('calories', 0),
            'nutrition_source': nutrition_source,
            'redirect': '/dashboard'
        })
        
    except Exception as e:
        logger.error(f"❌ 음식 확인 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'저장 중 오류가 발생했습니다: {str(e)}'
        }), 500

# 도우미 함수들
def allowed_file(filename):
    """허용된 파일 확장자 확인"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def analyze_food_image(image_path):
    """이미지를 Analyzer API로 분석"""
    try:
        # 이미지를 base64로 인코딩
        with open(image_path, 'rb') as image_file:
            image_base64 = base64.b64encode(image_file.read()).decode('utf-8')
        
        # Analyzer API 호출
        api_url = f"{Config.ANALYZER_API_URL}/analyze"
        
        payload = {
            'image': f"data:image/jpeg;base64,{image_base64}"
        }
        
        response = requests.post(
            api_url,
            json=payload,
            timeout=Config.API_TIMEOUT
        )
        
        if response.status_code == 200:
            result = response.json()
            
            # 결과를 우리 형식에 맞게 변환
            if result.get('success'):
                predictions = []
                
                # 메인 결과 추가
                main_result = {
                    'food_name_ko': result['data'].get('food_name_ko'),
                    'food_name_en': result['data'].get('food_name_en'),
                    'confidence': result['data'].get('confidence', 0),
                    'nutrition': result['data'].get('nutrition', {}),
                    'nutrition_source': 'analyzer_api'
                }
                predictions.append(main_result)
                
                # 대체 옵션들 추가
                for alt in result.get('korean_alternatives', [])[:2]:
                    alt_result = {
                        'food_name_ko': alt.get('food_name_ko'),
                        'food_name_en': alt.get('food_name_en'),
                        'confidence': alt.get('confidence', 0),
                        'nutrition': alt.get('nutrition', {}),
                        'nutrition_source': 'analyzer_api'
                    }
                    predictions.append(alt_result)
                
                for alt in result.get('general_alternatives', [])[:2]:
                    alt_result = {
                        'food_name_ko': None,
                        'food_name_en': alt.get('food_name'),
                        'confidence': alt.get('confidence', 0),
                        'nutrition': alt.get('nutrition', {}),
                        'nutrition_source': 'analyzer_api'
                    }
                    predictions.append(alt_result)
                
                return {
                    'success': True,
                    'method': result['data'].get('method', 'unknown'),
                    'predictions': predictions,
                    'caption': result['data'].get('caption', '')
                }
            else:
                return {
                    'success': False,
                    'error': result.get('error', 'API analysis failed'),
                    'raw_result': result
                }
        else:
            return {
                'success': False,
                'error': f'API request failed: {response.status_code}',
                'response_text': response.text
            }
            
    except Exception as e:
        return {
            'success': False,
            'error': f'Analysis request failed: {str(e)}'
        }

def save_food_record(food_record):
    """음식 기록을 데이터베이스에 저장"""
    try:
        # 실제 데이터베이스 저장 구현 필요
        # 여기서는 간단한 파일 저장으로 대체
        
        records_file = Path('food_records.json')
        
        # 기존 기록 로드
        if records_file.exists():
            with open(records_file, 'r', encoding='utf-8') as f:
                records = json.load(f)
        else:
            records = []
        
        # 새 기록 추가
        record_id = len(records) + 1
        food_record['id'] = record_id
        records.append(food_record)
        
        # 파일 저장
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(records, f, ensure_ascii=False, indent=2)
        
        return record_id
        
    except Exception as e:
        logger.error(f"❌ 데이터베이스 저장 오류: {e}")
        return f"error_{int(time.time())}"

def load_food_records():
    """음식 기록 불러오기"""
    try:
        records_file = Path('food_records.json')
        if records_file.exists():
            with open(records_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []
    except Exception as e:
        logger.error(f"❌ 음식 기록 로드 오류: {e}")
        return []

def save_user_profile(profile_data):
    """사용자 프로필 저장"""
    try:
        profile_file = Path('user_profile.json')
        
        # BMR 및 TDEE 계산 (안전한 변환 사용)
        weight = safe_float_conversion(profile_data.get('current_weight', 70))
        height = safe_float_conversion(profile_data.get('height', 170))
        age = int(safe_float_conversion(profile_data.get('age', 30)))
        gender = profile_data.get('gender', 'male')
        activity_level = profile_data.get('activity_level', 'moderate')
        target_weight = safe_float_conversion(profile_data.get('target_weight', 65))
        target_date = datetime.strptime(profile_data['target_date'], '%Y-%m-%d').date()
        
        # BMR 계산 (Harris-Benedict equation)
        if gender == 'male':
            bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age)
        else:
            bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age)
        
        # TDEE 계산
        activity_multipliers = {
            'sedentary': 1.2,
            'light': 1.375,
            'moderate': 1.55,
            'active': 1.725,
            'very_active': 1.9
        }
        tdee = bmr * activity_multipliers.get(activity_level, 1.2)
        
        # 목표 칼로리 계산
        today = datetime.now().date()
        days_to_target = (target_date - today).days
        weight_change = target_weight - weight
        
        # 1kg = 7700kcal
        total_calorie_change = weight_change * 7700
        daily_calorie_adjustment = total_calorie_change / days_to_target if days_to_target > 0 else 0
        daily_calorie_goal = tdee + daily_calorie_adjustment
        
        # 안전 범위 체크
        min_safe = bmr * 0.8
        max_safe = tdee * 1.2
        daily_calorie_goal = max(min_safe, min(max_safe, daily_calorie_goal))
        
        # 프로필 데이터 준비
        profile = {
            **profile_data,
            'bmr': round(bmr, 1),
            'tdee': round(tdee, 1),
            'daily_calorie_goal': round(daily_calorie_goal, 1),
            'days_remaining': days_to_target,
            'weight_change_needed': round(weight_change, 1),
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 파일 저장
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(profile, f, ensure_ascii=False, indent=2)
        
        logger.info(f"✅ 프로필 저장 완료: {daily_calorie_goal:.0f} kcal/일")
        
        return profile
        
    except Exception as e:
        logger.error(f"❌ 프로필 저장 오류: {e}")
        raise e

def safe_float_conversion(value, default=0.0):
    """안전한 float 변환 함수 - 강화된 버전"""
    try:
        if value is None or value == '' or value == 'None' or value == 'null':
            return default
        
        # 문자열인 경우 처리
        if isinstance(value, str):
            # 공백과 '칼' 등의 단위 제거
            value = value.strip().replace('칼', '').replace('kcal', '').replace('cal', '')
            
            # 비어있으면 기본값 리턴
            if not value:
                return default
            
            # 숫자가 아닌 문자 제거 (소수점과 마이너스 제외)
            import re
            value = re.sub(r'[^0-9.-]', '', value)
            if not value or value == '.' or value == '-':
                return default
        
        # 이미 숫자 타입인 경우
        if isinstance(value, (int, float)):
            return float(value)
            
        return float(value)
    except (ValueError, TypeError) as e:
        logger.debug(f"float 변환 실패: {value} -> {default} (error: {e})")
        return default

def load_user_profile():
    """사용자 프로필 불러오기"""
    try:
        profile_file = Path('user_profile.json')
        if profile_file.exists():
            with open(profile_file, 'r', encoding='utf-8') as f:
                profile = json.load(f)
                
                # 목표 달성도 계산
                if 'target_date' in profile and 'current_weight' in profile and 'target_weight' in profile:
                    target_date = datetime.strptime(profile['target_date'], '%Y-%m-%d').date()
                    today = datetime.now().date()
                    days_remaining = (target_date - today).days
                    
                    current_weight = safe_float_conversion(profile.get('current_weight', 70))
                    target_weight = safe_float_conversion(profile.get('target_weight', 65))
                    
                    # 최근 7일 평균 칼로리 계산
                    records = load_food_records()
                    recent_records = [r for r in records if 
                                    (datetime.now() - datetime.fromisoformat(r['timestamp'])).days <= 7]
                    recent_avg_calories = sum(r.get('calories', 0) for r in recent_records) / 7 if recent_records else 0
                    
                    # 목표 달성 여부
                    daily_goal = profile.get('daily_calorie_goal', 2000)
                    on_track = abs(recent_avg_calories - daily_goal) <= daily_goal * 0.1  # 10% 오차 허용
                    
                    profile.update({
                        'days_remaining': days_remaining,
                        'recent_avg_calories': round(recent_avg_calories, 1),
                        'on_track': on_track
                    })
                
                return profile
        return None
    except Exception as e:
        logger.error(f"❌ 프로필 로드 오류: {e}")
        return None

# === 영수증 OCR 기능 ===

def google_ocr_text_from_image(image_path):
    """영수증 이미지에서 텍스트 추출 (향상된 버전)"""
    if not GOOGLE_VISION_AVAILABLE:
        logger.error("❌ Google Cloud Vision API가 설치되지 않음")
        return "❌ Google Cloud Vision API가 설치되지 않음"
    
    try:
        client = vision.ImageAnnotatorClient()
        
        # 이미지 파일 읽기
        with open(image_path, "rb") as image_file:
            content = image_file.read()
        
        image = vision.Image(content=content)
        
        # OCR 수행 (한국어 텍스트 인식 최적화)
        response = client.text_detection(image=image)
        
        # 에러 체크
        if response.error.message:
            raise Exception(f"Google Vision API 오류: {response.error.message}")
        
        texts = response.text_annotations
        
        if texts:
            # 첫 번째 요소가 전체 텍스트
            full_text = texts[0].description
            logger.info(f"✅ OCR 텍스트 추출 성공: {len(full_text)}자")
            return full_text
        else:
            logger.warning("⚠️ OCR에서 텍스트를 찾지 못함")
            return ""
            
    except Exception as e:
        logger.error(f"❌ OCR 오류: {e}")
        return f"❌ OCR 오류: {str(e)}"

# 인식 가능한 음식 목록 (확장된 버전)
RECEIPT_FOODS = [
    # 한식
    "김밥", "야채김밥", "참치김밥", "계란말이김밥", "떡볶이", "라면", "비빔밥", 
    "된장찌개", "순두부찌개", "김치찌개", "볶음밥", "제육볶음", "삼겹살", "갈비탕",
    "냉면", "물냉면", "비빔냉면", "삼계탕", "갈비구이", "불고기", "닭갈비",
    "오므라이스", "치킨마요덮밥", "돈까스", "생선까스", "우동", "칼국수",
    
    # 양식
    "스파게티", "파스타", "피자", "햄버거", "치즈버거", "불고기버거", "치킨버거",
    "샌드위치", "토스트", "샐러드", "스테이크", "리조또",
    
    # 패스트푸드
    "빅맥", "후렌치후라이", "치킨너겟", "맥너겟", "치킨텐더", "양념치킨", "후라이드치킨",
    
    # 일식/중식
    "짜장면", "짬뽕", "탕수육", "초밥", "우동", "라멘", "돈카츠", "가츠동", "규동",
    
    # 음료
    "콜라", "사이다", "커피", "아메리카노", "카페라떼", "카푸치노", "에스프레소",
    "녹차", "홍차", "우유", "주스", "오렌지주스", "사과주스"
]

def extract_food_names_from_receipt(text):
    """영수증 텍스트에서 음식명 추출 (향상된 버전)"""
    if not text or text.startswith("❌"):
        return []
        
    lines = text.split('\n')
    detected_foods = set()
    
    # 음식명을 길이 순으로 정렬 (긴 것부터 매칭하여 "치킨버거" > "치킨" 우선)
    sorted_foods = sorted(RECEIPT_FOODS, key=len, reverse=True)
    
    logger.info(f"🔍 영수증 텍스트에서 음식명 추출 시작 ({len(lines)}줄)")
    
    for line_num, line in enumerate(lines, 1):
        line = line.strip()
        if not line:  # 빈 줄 건너뛰기
            continue
            
        # 각 줄에서 음식명 찾기
        for food in sorted_foods:
            if food in line:
                detected_foods.add(food)
                logger.debug(f"  발견: '{food}' in line {line_num}: {line[:50]}...")
                break  # 한 줄에서 첫 번째 매칭되는 음식만 추가
    
    # 중복 제거 (예: "치킨버거"가 있으면 "치킨" 제거)
    final_foods = set(detected_foods)
    removed_foods = set()
    
    for food in detected_foods:
        for other in detected_foods:
            if food != other and food in other and len(food) < len(other):
                final_foods.discard(food)
                removed_foods.add(food)
    
    if removed_foods:
        logger.debug(f"  중복 제거된 음식: {removed_foods}")
    
    final_list = list(final_foods)
    logger.info(f"✅ 최종 추출된 음식: {final_list}")
    
    return final_list

def get_simple_nutrition(food_name):
    """간단한 영양 정보 추정 (영수증용) - 확장된 데이터베이스"""
    # 기본 영양 데이터베이스 (확장된 버전)
    nutrition_db = {
        # 한식
        '김밥': {'calories': 150, 'carbs': 22, 'protein': 6, 'fat': 4},
        '야채김밥': {'calories': 140, 'carbs': 20, 'protein': 5, 'fat': 4},
        '참치김밥': {'calories': 160, 'carbs': 20, 'protein': 8, 'fat': 5},
        '떡볶이': {'calories': 120, 'carbs': 25, 'protein': 3, 'fat': 2},
        '라면': {'calories': 500, 'carbs': 70, 'protein': 15, 'fat': 18},
        '비빔밥': {'calories': 160, 'carbs': 22, 'protein': 6, 'fat': 4},
        '된장찌개': {'calories': 70, 'carbs': 6, 'protein': 5, 'fat': 3},
        '김치찌개': {'calories': 85, 'carbs': 4, 'protein': 6, 'fat': 5},
        '순두부찌개': {'calories': 90, 'carbs': 5, 'protein': 8, 'fat': 4},
        '볶음밥': {'calories': 180, 'carbs': 28, 'protein': 8, 'fat': 6},
        '제육볶음': {'calories': 220, 'carbs': 12, 'protein': 18, 'fat': 12},
        '삼겹살': {'calories': 350, 'carbs': 0, 'protein': 15, 'fat': 32},
        '갈비탕': {'calories': 120, 'carbs': 6, 'protein': 12, 'fat': 5},
        '냉면': {'calories': 130, 'carbs': 25, 'protein': 6, 'fat': 1},
        '삼계탕': {'calories': 200, 'carbs': 8, 'protein': 18, 'fat': 10},
        '갈비구이': {'calories': 300, 'carbs': 8, 'protein': 20, 'fat': 22},
        '불고기': {'calories': 210, 'carbs': 10, 'protein': 20, 'fat': 10},
        '돈까스': {'calories': 280, 'carbs': 25, 'protein': 15, 'fat': 15},
        '우동': {'calories': 140, 'carbs': 28, 'protein': 5, 'fat': 2},
        '칼국수': {'calories': 120, 'carbs': 22, 'protein': 6, 'fat': 2},
        
        # 양식
        '스파게티': {'calories': 180, 'carbs': 35, 'protein': 8, 'fat': 3},
        '파스타': {'calories': 180, 'carbs': 35, 'protein': 8, 'fat': 3},
        '피자': {'calories': 266, 'carbs': 33, 'protein': 11, 'fat': 10},
        '햄버거': {'calories': 295, 'carbs': 23, 'protein': 17, 'fat': 16},
        '치즈버거': {'calories': 320, 'carbs': 25, 'protein': 18, 'fat': 18},
        '샌드위치': {'calories': 250, 'carbs': 30, 'protein': 12, 'fat': 10},
        '토스트': {'calories': 200, 'carbs': 25, 'protein': 8, 'fat': 8},
        '샐러드': {'calories': 80, 'carbs': 10, 'protein': 5, 'fat': 3},
        '스테이크': {'calories': 280, 'carbs': 0, 'protein': 26, 'fat': 18},
        
        # 패스트푸드
        '빅맥': {'calories': 550, 'carbs': 45, 'protein': 25, 'fat': 30},
        '후렌치후라이': {'calories': 365, 'carbs': 63, 'protein': 4, 'fat': 17},
        '치킨너겟': {'calories': 250, 'carbs': 15, 'protein': 20, 'fat': 12},
        '맥너겟': {'calories': 250, 'carbs': 15, 'protein': 20, 'fat': 12},
        '치킨텐더': {'calories': 220, 'carbs': 12, 'protein': 22, 'fat': 10},
        '양념치킨': {'calories': 280, 'carbs': 15, 'protein': 20, 'fat': 18},
        '후라이드치킨': {'calories': 250, 'carbs': 8, 'protein': 20, 'fat': 15},
        
        # 일식/중식
        '짜장면': {'calories': 180, 'carbs': 32, 'protein': 8, 'fat': 3},
        '짬뽕': {'calories': 160, 'carbs': 20, 'protein': 12, 'fat': 5},
        '탕수육': {'calories': 320, 'carbs': 35, 'protein': 15, 'fat': 15},
        '초밥': {'calories': 150, 'carbs': 20, 'protein': 8, 'fat': 3},
        '라멘': {'calories': 450, 'carbs': 60, 'protein': 20, 'fat': 15},
        '가츠동': {'calories': 350, 'carbs': 45, 'protein': 18, 'fat': 12},
        '규동': {'calories': 320, 'carbs': 40, 'protein': 15, 'fat': 10},
        
        # 음료
        '콜라': {'calories': 140, 'carbs': 35, 'protein': 0, 'fat': 0},
        '사이다': {'calories': 130, 'carbs': 33, 'protein': 0, 'fat': 0},
        '커피': {'calories': 5, 'carbs': 1, 'protein': 0, 'fat': 0},
        '아메리카노': {'calories': 15, 'carbs': 2, 'protein': 1, 'fat': 1},
        '카페라떼': {'calories': 120, 'carbs': 10, 'protein': 6, 'fat': 6},
        '카푸치노': {'calories': 80, 'carbs': 8, 'protein': 4, 'fat': 4},
        '녹차': {'calories': 2, 'carbs': 0, 'protein': 0, 'fat': 0},
        '홍차': {'calories': 2, 'carbs': 0, 'protein': 0, 'fat': 0},
        '우유': {'calories': 150, 'carbs': 12, 'protein': 8, 'fat': 8},
        '오렌지주스': {'calories': 110, 'carbs': 26, 'protein': 2, 'fat': 0},
        '사과주스': {'calories': 115, 'carbs': 29, 'protein': 0, 'fat': 0}
    }
    
    nutrition = nutrition_db.get(food_name, {
        'calories': 200,  # 기본값
        'carbs': 25,
        'protein': 10,
        'fat': 8
    })
    
    return {
        'food': food_name,
        'calories': nutrition['calories'],
        'carbs': nutrition['carbs'],
        'protein': nutrition['protein'],
        'fat': nutrition['fat']
    }

@app.route('/api/nutrition_api_status')
def nutrition_api_status():
    """영양 API 상태 확인"""
    try:
        # Analyzer API 상태 확인
        response = requests.get(f"{Config.ANALYZER_API_URL}/health", timeout=5)
        api_available = response.status_code == 200
        
        return jsonify({
            'success': True,
            'data': {
                'api_available': api_available,
                'analyzer_url': Config.ANALYZER_API_URL,
                'status': 'online' if api_available else 'offline'
            }
        })
    except:
        return jsonify({
            'success': True,
            'data': {
                'api_available': False,
                'analyzer_url': Config.ANALYZER_API_URL,
                'status': 'offline'
            }
        })

@app.route('/api/analyzer_status')
def analyzer_status():
    """Analyzer API 상태 확인 (대시보드용)"""
    try:
        response = requests.get(f"{Config.ANALYZER_API_URL}/health", timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            return jsonify({
                'success': True,
                'data': {
                    'status': 'online',
                    'url': Config.ANALYZER_API_URL,
                    'model_loaded': health_data.get('model_loaded', False),
                    'response_time': 'normal'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': f'HTTP {response.status_code}'
            })
            
    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'error': 'Connection timeout'
        })
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'error': 'Connection failed - analyzer server may be offline'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/api/search_food')
def search_food():
    """음식 검색 API"""
    query = request.args.get('q', '').strip()
    limit = int(request.args.get('limit', 10))
    
    if len(query) < 2:
        return jsonify({
            'success': True,
            'data': []
        })
    
    # 간단한 음식 데이터베이스 (향후 실제 DB로 교체)
    foods_db = [
        {'food_name': '치킨텐더', 'calories_per_100g': 250},
        {'food_name': '불고기덕밥', 'calories_per_100g': 180},
        {'food_name': '비빔밥', 'calories_per_100g': 160},
        {'food_name': '제육볶음밥', 'calories_per_100g': 170},
        {'food_name': '김치찌개', 'calories_per_100g': 85},
        {'food_name': '된장찌개', 'calories_per_100g': 70},
        {'food_name': '삼계탕', 'calories_per_100g': 200},
        {'food_name': '떡볶이', 'calories_per_100g': 120},
        {'food_name': '자장면', 'calories_per_100g': 180},
        {'food_name': '하하하', 'calories_per_100g': 250}
    ]
    
    # 검색 수행
    results = []
    for food in foods_db:
        if query.lower() in food['food_name'].lower():
            results.append(food)
        if len(results) >= limit:
            break
    
    return jsonify({
        'success': True,
        'data': results
    })

@app.route('/receipt')
def receipt_upload():
    """영수증 업로드 페이지"""
    # Google Vision API 사용 가능 여부 확인 (개선된 버전)
    google_key_path = Path(__file__).parent / 'receipt-ocr-key.json'
    api_available = (
        GOOGLE_VISION_AVAILABLE and 
        google_key_path.exists() and 
        os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
    )
    
    # 디버깅 정보 로깅
    logger.info(f"🔍 Google Vision API 상태 체크:")
    logger.info(f"   - GOOGLE_VISION_AVAILABLE: {GOOGLE_VISION_AVAILABLE}")
    logger.info(f"   - 키 파일 존재: {google_key_path.exists()}")
    logger.info(f"   - 환경변수 설정: {bool(os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))}")
    logger.info(f"   - 최종 사용 가능: {api_available}")
    
    return render_template('receipt_upload.html', 
                          google_vision_available=api_available,
                          supported_foods=RECEIPT_FOODS[:20])  # 지원되는 음식 일부 표시

@app.route('/upload_receipt', methods=['POST'])
def upload_receipt():
    """영수증 이미지 분석 및 음식 추출"""
    try:
        if 'receipt_image' not in request.files:
            return jsonify({
                'success': False,
                'error': '이미지 파일이 업로드되지 않았습니다.'
            }), 400
        
        file = request.files['receipt_image']
        restaurant_name = request.form.get('restaurant_name', '영수증 분석')
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': '파일이 선택되지 않았습니다.'
            }), 400
        
        # 파일 저장
        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"receipt_{timestamp}_{filename}"
            
            upload_folder = Config.UPLOAD_FOLDER
            os.makedirs(upload_folder, exist_ok=True)
            
            file_path = os.path.join(upload_folder, filename)
            file.save(file_path)
            
            logger.info(f"📷 영수증 이미지 저장: {file_path}")
            
            # OCR 텍스트 추출
            logger.info(f"🔍 영수증 OCR 처리 시작: {file_path}")
            ocr_text = google_ocr_text_from_image(file_path)
            
            # OCR 실패 시 처리
            if ocr_text.startswith("❌"):
                return jsonify({
                    'success': False,
                    'error': 'OCR 처리에 실패했습니다. 이미지가 선명한지 확인해주세요.',
                    'ocr_error': ocr_text
                }), 400
            
            # 음식명 추출
            food_list = extract_food_names_from_receipt(ocr_text)
            
            if not food_list:
                return jsonify({
                    'success': False,
                    'error': '영수증에서 인식 가능한 음식을 찾지 못했습니다.',
                    'ocr_text': ocr_text,
                    'detected_foods': [],
                    'supported_foods_sample': RECEIPT_FOODS[:10]
                }), 400
            
            # 영양 정보 가져오기
            food_infos = []
            total_calories = 0
            
            for food_name in food_list:
                nutrition_info = get_simple_nutrition(food_name)
                if nutrition_info:
                    food_infos.append(nutrition_info)
                    total_calories += nutrition_info['calories']
                    
            logger.info(f"✅ 영수증 분석 완료: {len(food_infos)}개 음식, {total_calories} kcal")
            
            return jsonify({
                'success': True,
                'ocr_text': ocr_text,
                'detected_foods': food_list,
                'food_infos': food_infos,
                'total_calories': total_calories,
                'restaurant_name': restaurant_name,
                'image_path': file_path,
                'ocr_method': 'google_vision',
                'supported_foods_count': len(RECEIPT_FOODS),
                'processing_info': {
                    'total_text_lines': len(ocr_text.split('\n')),
                    'foods_found': len(food_list),
                    'total_foods_in_db': len(RECEIPT_FOODS)
                }
            })
        
        else:
            return jsonify({
                'success': False,
                'error': '지원되지 않는 파일 형식입니다. (JPG, PNG, GIF, WebP 지원)'
            }), 400
            
    except Exception as e:
        logger.error(f"❌ 영수증 분석 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'서버 오류가 발생했습니다: {str(e)}'
        }), 500

@app.route('/save_receipt_foods', methods=['POST'])
def save_receipt_foods():
    """영수증에서 추출한 음식들을 데이터베이스에 저장"""
    try:
        data = request.get_json()
        
        if not data or 'food_infos' not in data:
            return jsonify({
                'success': False,
                'error': '음식 정보가 필요합니다.'
            }), 400
        
        restaurant_name = data.get('restaurant_name', '영수증 분석')
        food_infos = data.get('food_infos', [])
        image_path = data.get('image_path')
        
        saved_records = []
        
        # 각 음식을 개별적으로 저장
        for food_info in food_infos:
            food_record = {
                'restaurant_name': restaurant_name,
                'food_name': food_info['food'],  # 표시용 이름
                'food_name_ko': food_info['food'],  # 한글 이름
                'food_name_en': None,  # 영문 이름 (영수증에서는 대부분 한글)
                'calories': food_info['calories'],
                'protein': food_info['protein'],
                'carbs': food_info['carbs'],
                'fat': food_info['fat'],
                'weight_grams': 100,  # 기본값 (1인분 기준)
                'nutrition_source': 'receipt_ocr',
                'confidence': 0.85,  # 영수증 기반이므로 높은 신뢰도
                'timestamp': datetime.now().isoformat(),
                'image_path': image_path,
                'method': 'receipt_ocr',
                'ocr_source': 'google_vision'
            }
            
            record_id = save_food_record(food_record)
            saved_records.append({
                'id': record_id,
                'food_name': food_info['food'],
                'calories': food_info['calories']
            })
        
        logger.info(f"✅ 영수증 음식 {len(saved_records)}개 저장 완료")
        
        # 🏃‍♂️ 운동 알림 자동 체크
        try:
            # 운동 알림 시스템이 필요한 경우 여기에 구현
            pass
        except Exception as ex:
            logger.warning(f"⚠️ 운동 알림 체크 실패: {ex}")
        
        return jsonify({
            'success': True,
            'message': f'영수증에서 {len(saved_records)}개 음식이 성공적으로 등록되었습니다!',
            'saved_records': saved_records,
            'total_foods': len(saved_records),
            'total_calories': sum(record.get('calories', 0) for record in saved_records),
            'method': 'receipt_ocr',
            'redirect': '/dashboard'
        })
        
    except Exception as e:
        logger.error(f"❌ 영수증 음식 저장 오류: {e}")
        return jsonify({
            'success': False,
            'error': f'저장 중 오류가 발생했습니다: {str(e)}'
        }), 500

@app.route('/dashboard')
def dashboard():
    """대시보드 페이지 - 안전한 처리"""
    try:
        # 음식 기록 불러오기
        records = load_food_records()
        
        # 날짜별 분석
        today = datetime.now().date()
        today_records = [r for r in records if datetime.fromisoformat(r['timestamp']).date() == today]
        
        # 오늘 요약 (안전한 숫자 변환 적용)
        today_summary = {
            'date': today.strftime('%Y-%m-%d'),
            'total_calories': sum(safe_float_conversion(r.get('calories', 0)) for r in today_records),
            'total_protein': sum(safe_float_conversion(r.get('protein', 0)) for r in today_records),
            'total_carbs': sum(safe_float_conversion(r.get('carbs', 0)) for r in today_records),
            'total_fat': sum(safe_float_conversion(r.get('fat', 0)) for r in today_records),
            'meal_count': len(today_records)
        }
        
        # 주간 데이터 (최근 7일)
        weekly_data = []
        total_weekly_calories = 0
        total_weekly_protein = 0
        total_weekly_meals = 0
        
        for i in range(7):
            day_date = today - timedelta(days=6-i)
            day_records = [r for r in records if datetime.fromisoformat(r['timestamp']).date() == day_date]
            day_calories = sum(safe_float_conversion(r.get('calories', 0)) for r in day_records)
            day_protein = sum(safe_float_conversion(r.get('protein', 0)) for r in day_records)
            
            weekly_data.append({
                'date': day_date.strftime('%Y-%m-%d'),
                'total_calories': day_calories,
                'total_protein': day_protein,
                'meal_count': len(day_records)
            })
            
            total_weekly_calories += day_calories
            total_weekly_protein += day_protein
            total_weekly_meals += len(day_records)
        
        weekly_summary = {
            'weekly_data': weekly_data,
            'total_calories': total_weekly_calories,
            'total_protein': total_weekly_protein,
            'total_meals': total_weekly_meals,
            'avg_daily_calories': total_weekly_calories / 7 if total_weekly_calories > 0 else 0
        }
        
        # 최근 음식 기록 (7일간)
        recent_records = [r for r in records if 
                         (datetime.now() - datetime.fromisoformat(r['timestamp'])).days <= 7]
        recent_records.sort(key=lambda x: x['timestamp'], reverse=True)
        
        # 프로필 불러오기 (안전한 처리)
        profile = load_user_profile()
        
        # 프로필이 없는 경우 기본값 사용
        if not profile:
            logger.warning("⚠️ 프로필이 설정되지 않음 - 기본값 사용")
            profile = {
                'current_weight': 70,
                'target_weight': 65,
                'daily_calorie_goal': 2000,
                'days_remaining': 30,
                'recent_avg_calories': today_summary['total_calories'],
                'on_track': True,
                'height': 170,
                'age': 25,
                'gender': 'male',
                'activity_level': 'moderate',
                'bmr': 1500,
                'tdee': 2000
            }
        
        return render_template('dashboard.html', 
                             today=today_summary,
                             weekly=weekly_summary,
                             recent_foods=recent_records[:20],
                             progress=profile)
                             
    except Exception as e:
        logger.error(f"대시보드 로드 오류: {e}")
        # 오류 발생 시 기본 데이터로 페이지 로드
        today = datetime.now().date()
        default_data = {
            'today': {
                'date': today.strftime('%Y-%m-%d'),
                'total_calories': 0,
                'total_protein': 0,
                'total_carbs': 0,
                'total_fat': 0,
                'meal_count': 0
            },
            'weekly': {
                'weekly_data': [],
                'total_calories': 0,
                'total_protein': 0,
                'total_meals': 0,
                'avg_daily_calories': 0
            },
            'recent_foods': [],
            'progress': None
        }
        
        return render_template('dashboard.html', 
                             today=default_data['today'],
                             weekly=default_data['weekly'],
                             recent_foods=default_data['recent_foods'],
                             progress=default_data['progress'])

@app.route('/profile', methods=['GET'])
def profile():
    """프로필 설정 페이지"""
    return render_template('profile.html')

@app.route('/api/profile', methods=['GET', 'POST'])
def api_profile():
    """프로필 API - 조회 및 저장"""
    if request.method == 'GET':
        # 프로필 조회
        try:
            profile = load_user_profile()
            return jsonify({
                'success': True,
                'data': profile
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    elif request.method == 'POST':
        # 프로필 저장
        try:
            data = request.get_json()
            
            # 데이터 유효성 검사
            required_fields = ['current_weight', 'height', 'age', 'gender', 'target_weight', 'target_date', 'activity_level']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({
                        'success': False,
                        'error': f'{field} 필드가 필요합니다.'
                    }), 400
            
            # 프로필 저장
            profile = save_user_profile(data)
            
            return jsonify({
                'success': True,
                'message': '프로필이 성공적으로 저장되었습니다.',
                'data': profile
            })
            
        except Exception as e:
            logger.error(f"❌ 프로필 저장 오류: {e}")
            return jsonify({
                'success': False,
                'error': f'프로필 저장 중 오류가 발생했습니다: {str(e)}'
            }), 500

# === 🏃‍♂️ 운동 추천 시스템 ===

@app.route('/exercise')
@app.route('/exercise_recommendations')
def exercise_recommendations():
    """운동 추천 페이지 - 안전한 처리"""
    try:
        # 사용자 프로필 로드
        profile = load_user_profile()
        if not profile:
            # 프로필이 없으면 설정 페이지로 리다이렉트
            return redirect('/profile?message=프로필을 먼저 설정해주세요')
        
        # 오늘 음식 기록 로드
        today = datetime.now().date()
        records = load_food_records()
        today_records = [r for r in records if datetime.fromisoformat(r['timestamp']).date() == today]
        
        # 오늘 총 칼로리 계산 (안전한 변환)
        daily_calories = sum(safe_float_conversion(r.get('calories', 0)) for r in today_records)
        
        # 운동 추천 계산
        exercise_data = get_exercise_recommendations(profile, daily_calories)
        
        # 주간 영양소 분석 (최근 7일)
        recent_records = [r for r in records if 
                         (datetime.now() - datetime.fromisoformat(r['timestamp'])).days <= 7]
        nutrition_analysis = analyze_nutrition(recent_records)
        
        # 운동 알림 체크
        reminder_status = check_exercise_reminder(today_records)
        
        return render_template('exercise_recommendations.html',
                             profile=profile,
                             exercise_data=exercise_data,
                             nutrition_analysis=nutrition_analysis,
                             today_records=today_records,
                             daily_calories=daily_calories,
                             reminder_status=reminder_status)
        
    except Exception as e:
        logger.error(f"운동 추천 페이지 오류: {e}")
        # 오류 발생 시 기본 데이터로 사용
        default_profile = {
            'current_weight': 70,
            'height': 170,
            'age': 25,
            'gender': 'male',
            'activity_level': 'moderate'
        }
        
        default_exercise_data = {
            'success': True,
            'bmr': 1500,
            'tdee': 2000,
            'consumed_calories': 0,
            'excess_calories': 0,
            'recommendations': {
                'message': '오류로 인해 추천을 생성할 수 없습니다.',
                'recommendations': {}
            }
        }
        
        default_nutrition = {
            'carb_percent': 50,
            'protein_percent': 20,
            'fat_percent': 30,
            'avg_daily_calories': 0,
            'alerts': ['데이터가 없어 분석할 수 없습니다.']
        }
        
        default_reminder = {
            'should_recommend': False,
            'meal_count': 0,
            'current_hour': datetime.now().hour,
            'trigger_reason': []
        }
        
        return render_template('exercise_recommendations.html',
                             profile=default_profile,
                             exercise_data=default_exercise_data,
                             nutrition_analysis=default_nutrition,
                             today_records=[],
                             daily_calories=0,
                             reminder_status=default_reminder)

@app.route('/test')
def test():
    """테스트 페이지"""
    return """
    <h1>🎯 실시간 장소 감지 테스트</h1>
    <p>✅ Kakao API 연동 완료</p>
    <p>⚙️ 감지 반경: {radius}m</p>
    <p>⏱️ 체류 시간: {time}초</p>
    <p><a href="/">메인 페이지로 이동</a></p>
    <p><a href="/test_vision">🕰 Google Vision API 테스트</a></p>
    """.format(radius=Config.DETECTION_RADIUS, time=Config.REQUIRED_STAY_TIME)

@app.route('/test_vision')
def test_vision_api():
    """구글 비전 API 테스트"""
    try:
        # 라이브러리 체크
        from google.cloud import vision
        library_ok = True
        library_error = None
    except ImportError as e:
        library_ok = False
        library_error = str(e)
    
    # 키 파일 체크
    google_key_path = Path(__file__).parent / 'receipt-ocr-key.json'
    key_file_exists = google_key_path.exists()
    
    # 환경변수 체크
    env_var_set = bool(os.getenv('GOOGLE_APPLICATION_CREDENTIALS'))
    env_var_value = os.getenv('GOOGLE_APPLICATION_CREDENTIALS', 'NOT_SET')
    
    # API 연결 테스트
    api_test_result = None
    api_test_error = None
    
    if library_ok and key_file_exists and env_var_set:
        try:
            client = vision.ImageAnnotatorClient()
            # 빈 이미지로 간단한 테스트
            image = vision.Image()
            response = client.text_detection(image=image)
            
            if response.error.message:
                api_test_result = False
                api_test_error = response.error.message
            else:
                api_test_result = True
                
        except Exception as e:
            api_test_result = False
            api_test_error = str(e)
    
    # 결과 HTML 생성
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Google Vision API 테스트</title>
        <style>
            body {{ font-family: Arial, sans-serif; padding: 20px; }}
            .ok {{ color: green; }}
            .error {{ color: red; }}
            .warning {{ color: orange; }}
            .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        </style>
    </head>
    <body>
        <h1>🔍 Google Vision API 테스트 결과</h1>
        
        <div class="section">
            <h3>1. 라이브러리 설치 체크</h3>
            <p class="{'ok' if library_ok else 'error'}">
                {'✅ google-cloud-vision 라이브러리 설치됨' if library_ok else f'❌ 라이브러리 미설치: {library_error}'}
            </p>
            {'<p><small>해결: pip install google-cloud-vision</small></p>' if not library_ok else ''}
        </div>
        
        <div class="section">
            <h3>2. 키 파일 체크</h3>
            <p class="{'ok' if key_file_exists else 'error'}">
                {'✅ receipt-ocr-key.json 파일 존재' if key_file_exists else '❌ 키 파일 없음'}
            </p>
            <p><small>키 파일 경로: {google_key_path}</small></p>
        </div>
        
        <div class="section">
            <h3>3. 환경변수 체크</h3>
            <p class="{'ok' if env_var_set else 'error'}">
                {'✅ GOOGLE_APPLICATION_CREDENTIALS 환경변수 설정됨' if env_var_set else '❌ 환경변수 미설정'}
            </p>
            <p><small>설정값: {env_var_value}</small></p>
        </div>
        
        <div class="section">
            <h3>4. API 연결 테스트</h3>
    """
    
    if api_test_result is None:
        html += '<p class="warning">⚠️ 이전 단계 실패로 테스트 불가</p>'
    elif api_test_result:
        html += '<p class="ok">✅ Google Vision API 연결 성공!</p>'
    else:
        html += f'<p class="error">❌ API 연결 실패: {api_test_error}</p>'
    
    html += f"""
        </div>
        
        <div class="section">
            <h3>5. 종합 상태</h3>
            <p class="{'ok' if (library_ok and key_file_exists and env_var_set and api_test_result) else 'error'}">
                {(
                    '🎉 모든 테스트 통과! 영수증 OCR 기능을 사용할 수 있습니다.' 
                    if (library_ok and key_file_exists and env_var_set and api_test_result) 
                    else '❌ 설정이 완료되지 않았습니다. 위의 오류를 해결해주세요.'
                )}
            </p>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="/receipt">📷 영수증 업로드 페이지</a> | 
            <a href="/test">🎯 테스트 페이지</a> | 
            <a href="/">🟢 메인 페이지</a>
        </div>
    </body>
    </html>
    """
    
    return html


@app.route('/api/add_sample_data', methods=['POST'])
def add_sample_data_api():
    """운동 기능 테스트를 위한 고칼로리 샘플 데이터 추가"""
    try:
        # 고칼로리 음식 데이터 (총 4150 kcal)
        sample_foods = [
            {
                'id': 1,
                'restaurant_name': '맥도널드',
                'food_name': '빅맥 세트',
                'food_name_ko': '빅맥 세트',
                'food_name_en': 'Big Mac Set',
                'calories': 1100,
                'protein': 27,
                'carbs': 70,
                'fat': 43,
                'weight_grams': 400,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=12, minute=30).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 2,
                'restaurant_name': '피자햷',
                'food_name': '콤비네이션 피자 (라지)',
                'food_name_ko': '콤비네이션 피자',
                'food_name_en': 'Combination Pizza Large',
                'calories': 800,
                'protein': 35,
                'carbs': 85,
                'fat': 35,
                'weight_grams': 300,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=18, minute=0).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 3,
                'restaurant_name': '스타벅스',
                'food_name': '화이트 초콜릿 모카 + 스콘',
                'food_name_ko': '화이트 초콜릿 모카 + 스콘',
                'food_name_en': 'White Chocolate Mocha + Scone',
                'calories': 650,
                'protein': 12,
                'carbs': 78,
                'fat': 28,
                'weight_grams': 250,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=15, minute=30).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 4,
                'restaurant_name': '한정식집',
                'food_name': '한정식 (대형) + 디저트',
                'food_name_ko': '한정식 + 디저트',
                'food_name_en': 'Korean Course Meal + Dessert',
                'calories': 900,
                'protein': 45,
                'carbs': 95,
                'fat': 38,
                'weight_grams': 500,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=19, minute=30).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 5,
                'restaurant_name': '던킨도너츠',
                'food_name': '도너츠 6개 + 라떼',
                'food_name_ko': '도너츠 6개 + 라떼',
                'food_name_en': '6 Donuts + Latte',
                'calories': 700,
                'protein': 15,
                'carbs': 120,
                'fat': 35,
                'weight_grams': 350,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=10, minute=0).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            }
        ]
        
        # 샘플 프로필
        sample_profile = {
            'current_weight': 75,
            'height': 175,
            'age': 28,
            'gender': 'male',
            'activity_level': 'moderate',
            'target_weight': 70,
            'target_date': (datetime.now() + timedelta(days=60)).strftime('%Y-%m-%d'),
            'bmr': 1681.0,
            'tdee': 2605.5,
            'daily_calorie_goal': 2105.5,
            'days_remaining': 60,
            'weight_change_needed': -5.0,
            'recent_avg_calories': 4150,
            'on_track': False,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 파일 저장
        records_file = Path('food_records.json')
        with open(records_file, 'w', encoding='utf-8') as f:
            json.dump(sample_foods, f, ensure_ascii=False, indent=2)
        
        profile_file = Path('user_profile.json')
        with open(profile_file, 'w', encoding='utf-8') as f:
            json.dump(sample_profile, f, ensure_ascii=False, indent=2)
        
        total_calories = sum(food['calories'] for food in sample_foods)
        excess_calories = total_calories - sample_profile['tdee']
        
        logger.info(f"✅ 샘플 데이터 추가 완료: {total_calories} kcal, 초과: {excess_calories:.0f} kcal")
        
        return jsonify({
            'success': True,
            'message': f'운동 기능 테스트용 샘플 데이터가 추가되었습니다!',
            'data': {
                'food_count': len(sample_foods),
                'total_calories': total_calories,
                'tdee': sample_profile['tdee'],
                'excess_calories': excess_calories,
                'profile_created': True
            },
            'instructions': [
                '대시보드에서 오늘 칼로리가 4150+ kcal로 표시되는지 확인',
                '운동 추천 페이지에서 초과 칼로리에 따른 운동 시간 계산 확인',
                '운동 시간을 클릭해서 운동 선택 기능 테스트'
            ]
        })
        
    except Exception as e:
        logger.error(f"❌ 샘플 데이터 추가 실패: {e}")
        return jsonify({
            'success': False,
            'error': f'샘플 데이터 추가 실패: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🚀 AI Restaurant Visit and Calorie Management System Starting")
    print("=" * 70)
    print("📍 Real-time Place Detection Features:")
    print("   - ✅ Kakao Place Search API integration")
    print("   - 🔍 Real-time restaurant/cafe detection")
    print("   - 🎯 Dynamic location-based notifications")
    print("   - 📱 Enhanced mobile GPS tracking")
    print("   - 🏃‍♂️ Smart Exercise Recommendations")
    print("=" * 70)
    print("🌐 Main Server: http://localhost:5000")
    print("🧪 Test Page: http://localhost:5000/test")
    print("🏃‍♂️ Exercise: http://localhost:5000/exercise")
    print("=" * 70)
    print("🎯 위치 추적 설정:")
    print(f"   - 감지 반경: {Config.DETECTION_RADIUS}m")
    print(f"   - 체류 시간: {Config.REQUIRED_STAY_TIME}초")
    print(f"   - Kakao API: {'✅ 연동완료' if KAKAO_REST_API_KEY != 'YOUR_KAKAO_REST_API_KEY' else '❌ 미설정'}")
    print("=" * 70)
    
    print("\n🧪 실시간 위치 감지 테스트 방법:")
    print("   1. 브라우저에서 http://localhost:5000 접속")
    print("   2. 위치 권한 허용")
    print("   3. 실제 음식점/카페에 이동 (또는 마커 드래그)")
    print(f"   4. {Config.REQUIRED_STAY_TIME}초 대기 후 실시간 알림 확인")
    print("\n🚀 Starting real-time location detection server...")
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)

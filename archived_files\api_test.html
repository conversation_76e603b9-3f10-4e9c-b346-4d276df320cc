<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>식품의약품안전처 API 테스트</title>
    <style>
        body { 
            font-family: 'Malgun Gothic', sans-serif; 
            max-width: 800px; 
            margin: 0 auto; 
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { 
            color: #2c5aa0; 
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        button {
            background-color: #2c5aa0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #1e4080;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            background-color: white;
            border-radius: 5px;
            border-left: 4px solid #2c5aa0;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success { border-left-color: #28a745; }
        .error { border-left-color: #dc3545; }
        .info { border-left-color: #17a2b8; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 식품의약품안전처 API 테스트</h1>
        
        <div class="test-section">
            <h3>📡 API 설정 정보</h3>
            <p><strong>엔드포인트:</strong> https://apis.data.go.kr/1471000/FoodNtrCpntDbInfo02</p>
            <p><strong>서비스명:</strong> getFoodNtrCpntList</p>
            <p><strong>파라미터:</strong> desc_kor (음식명)</p>
            <button onclick="testApiCall()">🧪 김치찌개 테스트</button>
            <div id="apiResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 음식 검색</h3>
            <input type="text" id="searchFood" placeholder="음식명 입력 (예: 김치, 된장찌개)" value="김치">
            <button onclick="searchFood()">검색</button>
            <div id="searchResult" class="result"></div>
        </div>
        
        <div class="test-section">
            <h3>🔑 API 키 변경 테스트</h3>
            <p>현재 키가 안되면 다른 키로 테스트:</p>
            <button onclick="testWithEncodingKey()">Encoding 키로 테스트</button>
            <button onclick="testWithDecodingKey()">Decoding 키로 테스트</button>
            <div id="keyTestResult" class="result"></div>
        </div>
    </div>

    <script>
        const ENCODING_KEY = "etI0mN6xN9OPH9gYuyZEspVti%2Bm8UwO4uOrr%2Bdscc%2BnL1gc47dqdn3vyA7kvADLuURaRKu4OVikQ4xmmFRnfUQ%3D%3D";
        const DECODING_KEY = "etI0mN6xN9OPH9gYuyZEspVti+m8UwO4uOrr+dscc+nL1gc47dqdn3vyA7kvADLuURaRKu4OVikQ4xmmFRnfUQ==";
        const BASE_URL = "https://apis.data.go.kr/1471000/FoodNtrCpntDbInfo02";

        async function makeApiCall(serviceKey, foodName) {
            const params = new URLSearchParams({
                serviceKey: serviceKey,
                desc_kor: foodName,
                pageNo: '1',
                numOfRows: '5',
                type: 'json'
            });

            const url = `${BASE_URL}/getFoodNtrCpntList?${params}`;
            
            try {
                console.log('API 호출 URL:', url);
                const response = await fetch(url);
                
                const result = {
                    status: response.status,
                    statusText: response.statusText,
                    headers: Object.fromEntries(response.headers.entries()),
                    body: null,
                    error: null
                };

                if (response.ok) {
                    try {
                        result.body = await response.json();
                    } catch (e) {
                        result.body = await response.text();
                        result.error = 'JSON 파싱 실패: ' + e.message;
                    }
                } else {
                    result.body = await response.text();
                    result.error = `HTTP ${response.status}: ${response.statusText}`;
                }

                return result;
            } catch (error) {
                return {
                    status: 0,
                    error: 'Network error: ' + error.message,
                    body: null
                };
            }
        }

        async function testApiCall() {
            const resultDiv = document.getElementById('apiResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = '⏳ API 호출 중...';

            const result = await makeApiCall(ENCODING_KEY, '김치찌개');
            
            resultDiv.className = result.status === 200 ? 'result success' : 'result error';
            resultDiv.textContent = `
🔍 API 응답 결과:
• 상태 코드: ${result.status}
• 상태 텍스트: ${result.statusText}
• 에러: ${result.error || '없음'}

📋 응답 내용:
${JSON.stringify(result.body, null, 2)}
            `.trim();
        }

        async function searchFood() {
            const foodName = document.getElementById('searchFood').value;
            const resultDiv = document.getElementById('searchResult');
            
            if (!foodName.trim()) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 음식명을 입력해주세요.';
                return;
            }

            resultDiv.className = 'result info';
            resultDiv.textContent = `⏳ "${foodName}" 검색 중...`;

            const result = await makeApiCall(ENCODING_KEY, foodName);
            
            resultDiv.className = result.status === 200 ? 'result success' : 'result error';
            
            let message = `🔍 "${foodName}" 검색 결과:\n`;
            message += `• 상태: ${result.status} ${result.statusText}\n`;
            
            if (result.body && typeof result.body === 'object') {
                if (result.body.response) {
                    const header = result.body.response.header;
                    message += `• API 결과: ${header.resultCode} - ${header.resultMsg}\n`;
                    
                    const body = result.body.response.body;
                    if (body && body.items) {
                        message += `• 검색된 항목: ${body.items.length}개\n\n`;
                        body.items.forEach((item, i) => {
                            message += `${i+1}. ${item.DESC_KOR || item.FOOD_NM_KR || '이름없음'}\n`;
                            message += `   에너지: ${item.NUTR_CONT1 || item.ENERC_KCAL || 0}kcal\n`;
                        });
                    }
                }
            }
            
            if (result.error) {
                message += `\n❌ 에러: ${result.error}`;
            }
            
            resultDiv.textContent = message;
        }

        async function testWithEncodingKey() {
            await testKeyType(ENCODING_KEY, 'Encoding');
        }

        async function testWithDecodingKey() {
            await testKeyType(DECODING_KEY, 'Decoding');
        }

        async function testKeyType(key, keyType) {
            const resultDiv = document.getElementById('keyTestResult');
            resultDiv.className = 'result info';
            resultDiv.textContent = `⏳ ${keyType} 키로 테스트 중...`;

            const result = await makeApiCall(key, '김치');
            
            resultDiv.className = result.status === 200 ? 'result success' : 'result error';
            resultDiv.textContent = `
🔑 ${keyType} 키 테스트 결과:
• 키: ${key.substring(0, 30)}...
• 상태: ${result.status} ${result.statusText}
• 에러: ${result.error || '없음'}

📋 응답:
${JSON.stringify(result.body, null, 2)}
            `.trim();
        }

        // 페이지 로드시 자동 테스트
        window.onload = function() {
            console.log('🧪 식품의약품안전처 API 테스트 페이지 로드됨');
        };
    </script>
</body>
</html>

# -*- coding: utf-8 -*-
"""
API Routes
API 엔드포인트 및 상태 확인 관련 라우트
"""

from flask import Blueprint, request, jsonify
import requests
import logging

from modules.config import config
from modules.utils import load_json_file

logger = logging.getLogger(__name__)

# Blueprint 생성
api_bp = Blueprint('api', __name__, url_prefix='/api')


@api_bp.route('/nutrition_api_status')
def nutrition_api_status():
    """영양 API 상태 확인"""
    try:
        # Analyzer API 상태 확인
        response = requests.get(f"{config.ANALYZER_API_URL}/health", timeout=5)
        api_available = response.status_code == 200
        
        return jsonify({
            'success': True,
            'data': {
                'api_available': api_available,
                'analyzer_url': config.ANALYZER_API_URL,
                'status': 'online' if api_available else 'offline'
            }
        })
    except:
        return jsonify({
            'success': True,
            'data': {
                'api_available': False,
                'analyzer_url': config.ANALYZER_API_URL,
                'status': 'offline'
            }
        })


@api_bp.route('/analyzer_status')
def analyzer_status():
    """Analyzer API 상태 확인 (대시보드용)"""
    try:
        response = requests.get(f"{config.ANALYZER_API_URL}/health", timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            return jsonify({
                'success': True,
                'data': {
                    'status': 'online',
                    'url': config.ANALYZER_API_URL,
                    'model_loaded': health_data.get('model_loaded', False),
                    'response_time': 'normal'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': f'HTTP {response.status_code}'
            })
            
    except requests.exceptions.Timeout:
        return jsonify({
            'success': False,
            'error': 'Connection timeout'
        })
    except requests.exceptions.ConnectionError:
        return jsonify({
            'success': False,
            'error': 'Connection failed - analyzer server may be offline'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })


@api_bp.route('/search_food')
def search_food():
    """음식 검색 API"""
    query = request.args.get('q', '').strip()
    limit = int(request.args.get('limit', 10))
    
    if len(query) < 2:
        return jsonify({
            'success': True,
            'data': []
        })
    
    # 간단한 음식 데이터베이스 (향후 실제 DB로 교체)
    foods_db = [
        {'food_name': '치킨텐더', 'calories_per_100g': 250},
        {'food_name': '불고기덮밥', 'calories_per_100g': 180},
        {'food_name': '비빔밥', 'calories_per_100g': 160},
        {'food_name': '제육볶음밥', 'calories_per_100g': 170},
        {'food_name': '김치찌개', 'calories_per_100g': 85},
        {'food_name': '된장찌개', 'calories_per_100g': 70},
        {'food_name': '삼계탕', 'calories_per_100g': 200},
        {'food_name': '떡볶이', 'calories_per_100g': 120},
        {'food_name': '짜장면', 'calories_per_100g': 180},
        {'food_name': '햄버거', 'calories_per_100g': 250}
    ]
    
    # 검색 수행
    results = []
    for food in foods_db:
        if query.lower() in food['food_name'].lower():
            results.append(food)
        if len(results) >= limit:
            break
    
    return jsonify({
        'success': True,
        'data': results
    })


@api_bp.route('/system_status')
def system_status():
    """전체 시스템 상태 확인"""
    try:
        status = {
            'timestamp': '2024-06-08T12:00:00Z',
            'services': {},
            'config': {},
            'data': {}
        }
        
        # Analyzer API 상태
        try:
            response = requests.get(f"{config.ANALYZER_API_URL}/health", timeout=3)
            status['services']['analyzer_api'] = {
                'status': 'online' if response.status_code == 200 else 'offline',
                'url': config.ANALYZER_API_URL,
                'response_code': response.status_code
            }
        except:
            status['services']['analyzer_api'] = {
                'status': 'offline',
                'url': config.ANALYZER_API_URL,
                'error': 'Connection failed'
            }
        
        # Kakao API 상태
        status['services']['kakao_api'] = {
            'configured': config.is_kakao_configured(),
            'rest_api_key': '설정됨' if config.is_kakao_configured() else '미설정'
        }
        
        # Google Vision API 상태
        status['services']['google_vision'] = {
            'key_file_exists': config.GOOGLE_CREDENTIALS_PATH.exists(),
            'library_available': 'google.cloud.vision' in str(globals())
        }
        
        # 설정 상태
        status['config'] = {
            'detection_radius': config.DETECTION_RADIUS,
            'required_stay_time': config.REQUIRED_STAY_TIME,
            'upload_folder': config.UPLOAD_FOLDER,
            'debug_mode': config.DEBUG
        }
        
        # 데이터 상태
        try:
            records = load_json_file('food_records.json', [])
            profile = load_json_file('user_profile.json')
            restaurants = load_json_file('restaurants.json', [])
            
            status['data'] = {
                'food_records_count': len(records),
                'profile_configured': profile is not None,
                'restaurants_loaded': len(restaurants)
            }
        except:
            status['data'] = {
                'food_records_count': 0,
                'profile_configured': False,
                'restaurants_loaded': 0,
                'error': 'Data loading failed'
            }
        
        return jsonify({
            'success': True,
            'data': status
        })
        
    except Exception as e:
        logger.error(f"시스템 상태 확인 오류: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@api_bp.route('/config')
def get_config():
    """프론트엔드용 설정 정보 반환"""
    try:
        return jsonify({
            'success': True,
            'data': config.get_template_config()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@api_bp.route('/health')
def health_check():
    """간단한 헬스체크"""
    return jsonify({
        'status': 'healthy',
        'timestamp': '2024-06-08T12:00:00Z',
        'version': '2.0.0'
    })

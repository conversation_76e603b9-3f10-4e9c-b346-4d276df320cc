# === AI Restaurant Visit & Calorie Management System - Enhanced Configuration ===
# 이 파일을 .env로 복사하고 설정을 수정하세요

# === 위치 추적 설정 (Enhanced) ===
# 감지 반경 (미터) - 테스트를 위해 10m로 증가
DETECTION_RADIUS=10.0

# 체류 시간 (초) - 테스트를 위해 5초로 단축
REQUIRED_STAY_TIME=5

# 위치 업데이트 간격 (밀리초)
LOCATION_UPDATE_INTERVAL=1000

# === 자동 리디렉션 설정 ===
# 알림 후 자동으로 음식 등록 페이지로 이동할지 여부
AUTO_REDIRECT_ENABLED=true

# 자동 리디렉션 지연 시간 (초)
AUTO_REDIRECT_DELAY=3

# === API 설정 ===
# 식품의약품안전처 API 키 (실제 키로 교체하세요)
MFDS_API_KEY=YOUR_API_KEY_HERE

# Food Analyzer API 서버 주소
ANALYZER_API_URL=http://localhost:5001

# API 타임아웃 (초)
API_TIMEOUT=30

# === 파일 업로드 설정 ===
# 업로드 폴더
UPLOAD_FOLDER=uploads

# 허용되는 파일 확장자 (콤마로 구분)
ALLOWED_EXTENSIONS=png,jpg,jpeg,gif,webp

# 최대 파일 크기 (바이트) - 10MB
MAX_FILE_SIZE=10485760

# === 보안 설정 ===
# Flask 시크릿 키 (운영 환경에서는 반드시 변경하세요)
SECRET_KEY=dev-secret-key-change-in-production

# === 데이터베이스 설정 ===
# 데이터베이스 파일 경로
DATABASE_PATH=food_data.db

# === 로깅 설정 ===
# 로그 레벨 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=DEBUG

# 로그 파일 경로
LOG_FILE=app.log

# === 디버깅 설정 ===
# 디버그 모드 활성화 (개발 시에만 true)
DEBUG_MODE=true

# 상세 로깅 활성화
VERBOSE_LOGGING=true

# === 테스트 설정 ===
# 테스트 모드에서 사용할 기본 위치 (대구 IT대학)
DEFAULT_TEST_LAT=35.830569788
DEFAULT_TEST_LNG=128.75399385

# 테스트용 빠른 감지 모드 (감지 시간을 더 짧게)
FAST_TEST_MODE=true

# === 성능 설정 ===
# 캐시 만료 시간 (시간)
CACHE_EXPIRY_HOURS=24

# 최대 동시 연결 수
MAX_CONNECTIONS=100

# === 알림 설정 ===
# 브라우저 알림 활성화
BROWSER_NOTIFICATIONS=true

# 알림 자동 닫기 시간 (초, 자동 리디렉션이 비활성화된 경우)
NOTIFICATION_AUTO_CLOSE=15

# === 음식점 데이터 설정 ===
# 음식점 데이터 파일
RESTAURANTS_FILE=restaurants.json

# 음식점 데이터 자동 새로고침 간격 (분)
RESTAURANTS_REFRESH_INTERVAL=60

# === UI/UX 설정 ===
# 지도 기본 줌 레벨
DEFAULT_MAP_ZOOM=3

# 상태 메시지 최대 개수
MAX_STATUS_MESSAGES=10

# 실시간 거리 표시 활성화
SHOW_REAL_TIME_DISTANCE=true

# === 개발자 설정 ===
# 개발자 도구 활성화
DEVELOPER_TOOLS=true

# API 응답 시간 로깅
LOG_API_RESPONSE_TIME=true

# 위치 추적 상세 로그
DETAILED_LOCATION_LOGGING=true

# === 주의사항 ===
# 1. 실제 운영 환경에서는 SECRET_KEY를 반드시 변경하세요
# 2. MFDS_API_KEY는 실제 발급받은 키로 교체하세요
# 3. DETECTION_RADIUS와 REQUIRED_STAY_TIME은 테스트 환경에 맞게 조정하세요
# 4. 디버그 모드는 운영 환경에서 비활성화하세요
# 5. 로그 파일 크기가 커질 수 있으니 주기적으로 정리하세요

# === 테스트용 추천 설정 ===
# 빠른 테스트를 위한 설정:
# DETECTION_RADIUS=5.0
# REQUIRED_STAY_TIME=3
# AUTO_REDIRECT_DELAY=2
# FAST_TEST_MODE=true

# 실제 운영을 위한 설정:
# DETECTION_RADIUS=7.0
# REQUIRED_STAY_TIME=10
# AUTO_REDIRECT_DELAY=5
# FAST_TEST_MODE=false

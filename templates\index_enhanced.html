<!DOCTYPE html>
<html>
<head>
    <title>AI 음식점 방문 및 칼로리 관리 - Enhanced</title>
    <script type="text/javascript" src="//dapi.kakao.com/v2/maps/sdk.js?appkey=e86f572903492915e7f56248232b5fcc"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
        }
        .header p {
            margin: 0;
            opacity: 0.9;
        }
        .nav-bar {
            background: white;
            padding: 15px 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        .nav-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            display: inline-block;
        }
        .nav-btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        .nav-btn.secondary {
            background: #2196f3;
        }
        .nav-btn.secondary:hover {
            background: #1976d2;
        }
        .nav-btn.location {
            background: #ff9800;
        }
        .nav-btn.location:hover {
            background: #f57c00;
        }

        /* === Enhanced Configuration Panel === */
        .config-panel {
            margin: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .config-item {
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }
        .config-value {
            font-size: 24px;
            font-weight: 600;
            color: #4CAF50;
            margin-bottom: 5px;
        }
        .config-label {
            font-size: 14px;
            color: #666;
        }

        #map {
            height: 500px;
            width: 100%;
            border-radius: 0;
        }
        .map-container {
            position: relative;
            margin: 20px;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        #customControl {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        .control-btn {
            background: white;
            border: 1px solid #ccc;
            border-radius: 6px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 6px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .control-btn:hover {
            background: #f5f5f5;
            transform: scale(1.05);
        }
        .status-panel {
            margin: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .status-panel h2 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 20px;
        }
        .status-item {
            padding: 12px;
            margin: 8px 0;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #4CAF50;
            font-size: 14px;
        }
        .status-item.entered {
            border-left-color: #4CAF50;
        }
        .status-item.left {
            border-left-color: #ff9800;
        }
        .status-item.notification {
            border-left-color: #e91e63;
            background: #fce4ec;
        }
        .status-item.debug {
            border-left-color: #9c27b0;
            background: #f3e5f5;
            font-size: 12px;
            opacity: 0.8;
        }

        /* === Enhanced Notification with Auto-redirect === */
        .notification {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px 30px;
            margin: 0;
            border-radius: 12px;
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
            min-width: 350px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .notification h3 {
            margin: 0 0 15px 0;
            font-size: 20px;
        }
        .notification p {
            margin: 0 0 20px 0;
            opacity: 0.9;
        }
        .notification-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 15px;
        }
        .notification button {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 15px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .notification #register-btn {
            background: #4CAF50;
            color: white;
        }
        .notification #register-btn:hover {
            background: #45a049;
            transform: translateY(-1px);
        }
        .notification #cancel-btn {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }
        .notification #cancel-btn:hover {
            background: rgba(255,255,255,0.3);
        }
        .auto-redirect {
            font-size: 14px;
            opacity: 0.8;
            margin-top: 10px;
        }
        .countdown {
            display: inline-block;
            background: rgba(255,255,255,0.2);
            padding: 2px 8px;
            border-radius: 4px;
            font-weight: 600;
        }

        .restaurant-info {
            background: white;
            border-radius: 8px;
            padding: 15px;
            min-width: 200px;
            max-width: 300px;
            box-sizing: border-box;
            font-size: 15px;
            color: #333;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .restaurant-info h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 16px;
        }
        .restaurant-info p {
            margin: 4px 0;
            font-size: 14px;
            color: #666;
        }
        .info-panel {
            margin: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        .feature-item {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }
        .feature-icon {
            font-size: 40px;
            margin-bottom: 10px;
        }
        .feature-title {
            font-weight: 600;
            margin-bottom: 8px;
            color: #2c3e50;
        }
        .feature-desc {
            font-size: 14px;
            color: #666;
        }
        
        /* === Enhanced Location Control Panel === */
        .location-panel {
            margin: 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        .location-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .location-method {
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        .location-method:hover {
            border-color: #4CAF50;
            background: #f8fff8;
        }
        .location-method.active {
            border-color: #4CAF50;
            background: #e8f5e8;
        }
        .location-method .icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .location-method .title {
            font-weight: 600;
            margin-bottom: 5px;
        }
        .location-method .desc {
            font-size: 12px;
            color: #666;
        }
        .coord-input {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            align-items: center;
        }
        .coord-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .coord-input button {
            padding: 8px 16px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .coord-input button:hover {
            background: #45a049;
        }
        .current-location {
            margin-top: 15px;
            padding: 10px;
            background: #f0f8ff;
            border-radius: 4px;
            font-size: 14px;
        }
        
        @media (max-width: 768px) {
            .nav-bar {
                padding: 10px;
            }
            .nav-btn {
                padding: 8px 16px;
                font-size: 14px;
            }
            .map-container {
                margin: 10px;
            }
            #map {
                height: 400px;
            }
            .notification {
                min-width: 300px;
                margin: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🍽️ AI 음식점 방문 및 칼로리 관리 (Enhanced)</h1>
        <p>실시간 위치 추적 • 자동 음식 인식 • 스마트 칼로리 계산 • 자동 리디렉션</p>
    </div>

    <div class="nav-bar">
        <a href="/dashboard" class="nav-btn secondary">📊 대시보드</a>
        <a href="/upload_food" class="nav-btn">📸 음식 등록</a>
        <a href="/profile" class="nav-btn secondary">👤 프로필</a>
        <button onclick="refreshLocation()" class="nav-btn">🔄 위치 새로고침</button>
        <button onclick="toggleLocationPanel()" class="nav-btn location" id="location-toggle-btn">🎯 위치 설정</button>
        <button onclick="toggleDebugMode()" class="nav-btn" id="debug-toggle-btn">🔍 디버그 모드</button>
    </div>

    <!-- === Enhanced Configuration Display === -->
    <div class="config-panel">
        <h2>⚙️ 현재 설정</h2>
        <div class="config-grid">
            <div class="config-item">
                <div class="config-value" id="detection-radius-display">{{config.detection_radius}}m</div>
                <div class="config-label">감지 반경</div>
            </div>
            <div class="config-item">
                <div class="config-value" id="stay-time-display">{{config.required_stay_time}}초</div>
                <div class="config-label">체류 시간</div>
            </div>
            <div class="config-item">
                <div class="config-value" id="update-interval-display">{{config.location_update_interval}}ms</div>
                <div class="config-label">업데이트 간격</div>
            </div>
            <div class="config-item">
                <div class="config-value" id="auto-redirect-display">
                    {% if config.auto_redirect_enabled %}✅{% else %}❌{% endif %}
                </div>
                <div class="config-label">자동 리디렉션</div>
            </div>
        </div>
    </div>

    <!-- === Enhanced Location Control Panel === -->
    <div class="location-panel" id="location-panel" style="display: none;">
        <h2>📍 위치 설정</h2>
        <p>테스트를 위해 현재 위치를 변경할 수 있습니다. 식당 근처로 이동하여 알림 기능을 체험해보세요!</p>
        
        <div class="location-controls">
            <div class="location-method active" onclick="setLocationMethod('drag')">
                <div class="icon">🖱️</div>
                <div class="title">지도에서 드래그</div>
                <div class="desc">지도의 파란 마커를 드래그하여 위치 변경</div>
            </div>
            <div class="location-method" onclick="setLocationMethod('click')">
                <div class="icon">👆</div>
                <div class="title">지도 클릭</div>
                <div class="desc">지도를 클릭한 곳으로 즉시 이동</div>
            </div>
            <div class="location-method" onclick="setLocationMethod('gps')">
                <div class="icon">🛰️</div>
                <div class="title">실제 GPS</div>
                <div class="desc">브라우저의 위치 정보 사용</div>
            </div>
            <div class="location-method" onclick="setLocationMethod('manual')">
                <div class="icon">⌨️</div>
                <div class="title">직접 입력</div>
                <div class="desc">위도, 경도를 직접 입력</div>
            </div>
        </div>
        
        <div class="coord-input" id="manual-input" style="display: none;">
            <input type="number" id="lat-input" placeholder="위도 (예: 35.830569)" step="0.000001">
            <input type="number" id="lng-input" placeholder="경도 (예: 128.753993)" step="0.000001">
            <button onclick="setManualLocation()">위치 설정</button>
        </div>
        
        <div class="current-location" id="current-location">
            📍 현재 위치: 위치를 설정해주세요
        </div>
    </div>

    <div class="map-container">
        <div id="customControl">
            <button class="control-btn" id="find-location-btn" title="현재 위치로 이동">
                <img src="https://cdn-icons-png.flaticon.com/512/5055/5055654.png" alt="현 위치" 
                style="width:24px;height:24px;filter:brightness(0) opacity(0.7);"/>
            </button>
        </div>
        <div id="map"></div>
    </div>

    <div class="status-panel">
        <h2>📍 실시간 상태</h2>
        <div id="status">
            <div class="status-item">시스템 대기 중... 위치를 설정하고 테스트해보세요!</div>
        </div>
    </div>

    <div class="info-panel">
        <h2>🚀 주요 기능 (Enhanced)</h2>
        <div class="feature-grid">
            <div class="feature-item">
                <div class="feature-icon">📍</div>
                <div class="feature-title">스마트 위치 추적</div>
                <div class="feature-desc">설정 가능한 반경 및 체류시간으로 정확한 감지</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">🔔</div>
                <div class="feature-title">자동 알림 & 리디렉션</div>
                <div class="feature-desc">알림 후 자동으로 음식 등록 페이지로 이동</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">🔍</div>
                <div class="feature-title">상세 디버깅</div>
                <div class="feature-desc">실시간 로그와 상세한 상태 정보 제공</div>
            </div>
            <div class="feature-item">
                <div class="feature-icon">⚙️</div>
                <div class="feature-title">설정 최적화</div>
                <div class="feature-desc">테스트 환경에 맞는 감지 설정</div>
            </div>
        </div>
    </div>

    <!-- === Enhanced Notification with Auto-redirect === -->
    <div id="notification" class="notification"></div>

    <script>
        const socket = io();
        const restaurants = JSON.parse('{{restaurants|tojson|safe}}');
        const config = JSON.parse('{{config|tojson|safe}}');
        
        let map = null;
        let userMarker = null;
        let userCircle = null;
        let restaurantMarkers = {};
        let currentLocationMethod = 'drag';
        let clickListener = null;
        let currentRestaurant = null;
        let debugMode = false;
        let autoRedirectTimer = null;

        // Location state
        let currentPosition = {
            lat: 35.830569788,  // Default: IT관
            lng: 128.75399385
        };

        function initMap() {
            const container = document.getElementById('map');
            const options = {
                center: new kakao.maps.LatLng(currentPosition.lat, currentPosition.lng),
                level: 3
            };
            map = new kakao.maps.Map(container, options);

            // 지도 컨트롤 추가
            var mapTypeControl = new kakao.maps.MapTypeControl();
            map.addControl(mapTypeControl, kakao.maps.ControlPosition.TOPRIGHT);

            var zoomControl = new kakao.maps.ZoomControl();
            map.addControl(zoomControl, kakao.maps.ControlPosition.BOTTOMRIGHT);

            // 음식점 마커 생성
            createRestaurantMarkers();

            // 사용자 마커 생성
            createUserMarker();

            // 초기 위치 전송
            sendLocationUpdate();
            updateLocationDisplay();
        }

        function createRestaurantMarkers() {
            for (const restaurant of restaurants) {
                const markerImage = new kakao.maps.MarkerImage(
                    'https://cdn-icons-png.flaticon.com/512/3514/3514491.png',
                    new kakao.maps.Size(30, 30),
                    {offset: new kakao.maps.Point(15, 15)}
                );
                
                const marker = new kakao.maps.Marker({
                    position: new kakao.maps.LatLng(restaurant.lat, restaurant.lon),
                    map: map,
                    title: restaurant.name,
                    image: markerImage
                });

                const infowindow = new kakao.maps.InfoWindow({
                    content: `
                        <div class="restaurant-info">
                            <h4>🍽️ ${restaurant.name}</h4>
                            <p>📂 ${restaurant.category}</p>
                            <p>📏 반경 ${config.detection_radius}m 이내에서 ${config.required_stay_time}초 체류시 알림</p>
                        </div>
                    `
                });

                let isOpen = false;
                kakao.maps.event.addListener(marker, 'click', function() {
                    if (isOpen) {
                        infowindow.close();
                        isOpen = false;
                    } else {
                        // 다른 정보창들 닫기
                        Object.values(restaurantMarkers).forEach(rm => {
                            if (rm.infowindow) rm.infowindow.close();
                            rm.isOpen = false;
                        });
                        
                        infowindow.open(map, marker);
                        isOpen = true;
                    }
                });

                restaurantMarkers[restaurant.name] = {
                    marker: marker,
                    infowindow: infowindow,
                    isOpen: isOpen
                };
            }
        }

        function createUserMarker() {
            const userMarkerImage = new kakao.maps.MarkerImage(
                'https://t1.daumcdn.net/localimg/localimages/07/mapapidoc/markerStar.png',
                new kakao.maps.Size(24, 35)
            );

            const position = new kakao.maps.LatLng(currentPosition.lat, currentPosition.lng);

            userMarker = new kakao.maps.Marker({
                position: position,
                map: map,
                image: userMarkerImage,
                draggable: true
            });

            // 사용자 반경 표시 (설정 가능한 반경)
            userCircle = new kakao.maps.Circle({
                center: position,
                radius: config.detection_radius,
                strokeWeight: 2,
                strokeColor: '#667eea',
                strokeOpacity: 0.6,
                fillColor: '#667eea',
                fillOpacity: 0.2,
                map: map
            });

            // 마커 드래그 이벤트
            kakao.maps.event.addListener(userMarker, 'dragend', function() {
                if (currentLocationMethod === 'drag') {
                    const pos = userMarker.getPosition();
                    updateUserPosition(pos.getLat(), pos.getLng());
                }
            });

            // 현 위치 찾기 버튼
            const findLocationBtn = document.getElementById('find-location-btn');
            findLocationBtn.addEventListener('click', function() {
                const pos = userMarker.getPosition();
                map.setCenter(pos);
                map.setLevel(3);
            });
        }

        function updateUserPosition(lat, lng) {
            currentPosition = { lat, lng };
            
            const position = new kakao.maps.LatLng(lat, lng);
            userMarker.setPosition(position);
            userCircle.setPosition(position);
            
            sendLocationUpdate();
            updateLocationDisplay();
        }

        function sendLocationUpdate() {
            socket.emit('update_location', {
                lat: currentPosition.lat,
                lon: currentPosition.lng
            });
            
            if (debugMode) {
                addStatusMessage(`📡 위치 전송: ${currentPosition.lat.toFixed(6)}, ${currentPosition.lng.toFixed(6)}`, 'debug');
            }
        }

        function updateLocationDisplay() {
            const display = document.getElementById('current-location');
            display.innerHTML = `📍 현재 위치: ${currentPosition.lat.toFixed(6)}, ${currentPosition.lng.toFixed(6)}`;
        }

        // === Enhanced Location Control Methods ===

        function toggleLocationPanel() {
            const panel = document.getElementById('location-panel');
            const btn = document.getElementById('location-toggle-btn');
            
            if (panel.style.display === 'none' || !panel.style.display) {
                panel.style.display = 'block';
                btn.textContent = '🎯 위치 설정 닫기';
            } else {
                panel.style.display = 'none';
                btn.textContent = '🎯 위치 설정';
            }
        }

        function toggleDebugMode() {
            debugMode = !debugMode;
            const btn = document.getElementById('debug-toggle-btn');
            
            if (debugMode) {
                btn.textContent = '🔍 디버그 OFF';
                btn.style.background = '#e91e63';
                addStatusMessage('🔍 디버그 모드 활성화', 'debug');
            } else {
                btn.textContent = '🔍 디버그 모드';
                btn.style.background = '#4CAF50';
                addStatusMessage('🔍 디버그 모드 비활성화', 'debug');
            }
        }

        function setLocationMethod(method) {
            currentLocationMethod = method;
            
            // UI 업데이트
            document.querySelectorAll('.location-method').forEach(el => {
                el.classList.remove('active');
            });
            event.target.closest('.location-method').classList.add('active');
            
            // 입력 필드 표시/숨김
            const manualInput = document.getElementById('manual-input');
            manualInput.style.display = method === 'manual' ? 'flex' : 'none';
            
            // 기존 클릭 리스너 제거
            if (clickListener) {
                kakao.maps.event.removeListener(map, 'click', clickListener);
                clickListener = null;
            }
            
            // 방법별 설정
            switch (method) {
                case 'drag':
                    userMarker.setDraggable(true);
                    addStatusMessage('🖱️ 드래그 모드: 파란 마커를 드래그하여 위치를 변경하세요');
                    break;
                    
                case 'click':
                    userMarker.setDraggable(false);
                    clickListener = kakao.maps.event.addListener(map, 'click', function(mouseEvent) {
                        const latlng = mouseEvent.latLng;
                        updateUserPosition(latlng.getLat(), latlng.getLng());
                        addStatusMessage(`👆 클릭 위치로 이동: ${latlng.getLat().toFixed(6)}, ${latlng.getLng().toFixed(6)}`);
                    });
                    addStatusMessage('👆 클릭 모드: 지도를 클릭하여 위치를 변경하세요');
                    break;
                    
                case 'gps':
                    userMarker.setDraggable(false);
                    if (navigator.geolocation) {
                        addStatusMessage('🛰️ GPS 위치를 가져오는 중...');
                        navigator.geolocation.getCurrentPosition(
                            function(position) {
                                const lat = position.coords.latitude;
                                const lng = position.coords.longitude;
                                updateUserPosition(lat, lng);
                                map.setCenter(new kakao.maps.LatLng(lat, lng));
                                addStatusMessage(`🛰️ GPS 위치로 설정: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
                            },
                            function(error) {
                                addStatusMessage('❌ GPS 위치를 가져올 수 없습니다. 다른 방법을 사용해주세요.');
                                setLocationMethod('drag');
                            }
                        );
                    } else {
                        addStatusMessage('❌ 이 브라우저는 GPS를 지원하지 않습니다. 다른 방법을 사용해주세요.');
                        setLocationMethod('drag');
                    }
                    break;
                    
                case 'manual':
                    userMarker.setDraggable(false);
                    addStatusMessage('⌨️ 수동 입력 모드: 위도와 경도를 직접 입력하세요');
                    break;
            }
        }

        function setManualLocation() {
            const latInput = document.getElementById('lat-input');
            const lngInput = document.getElementById('lng-input');
            
            const lat = parseFloat(latInput.value);
            const lng = parseFloat(lngInput.value);
            
            if (isNaN(lat) || isNaN(lng)) {
                alert('올바른 위도와 경도를 입력해주세요.');
                return;
            }
            
            if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
                alert('위도는 -90~90, 경도는 -180~180 범위 내에서 입력해주세요.');
                return;
            }
            
            updateUserPosition(lat, lng);
            map.setCenter(new kakao.maps.LatLng(lat, lng));
            addStatusMessage(`⌨️ 수동 위치 설정: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
        }

        function refreshLocation() {
            sendLocationUpdate();
            addStatusMessage(`🔄 위치 새로고침: ${new Date().toLocaleTimeString()}`);
        }

        function addStatusMessage(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            const status = document.createElement('div');
            status.className = `status-item ${type}`;
            status.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            statusDiv.appendChild(status);
            
            // 최근 10개만 유지
            while (statusDiv.children.length > 10) {
                statusDiv.removeChild(statusDiv.firstChild);
            }
            
            // 자동 스크롤
            statusDiv.scrollTop = statusDiv.scrollHeight;
        }

        // === Enhanced Notification with Auto-redirect ===
        function showNotification(data) {
            currentRestaurant = data.restaurant;
            
            const notification = document.getElementById('notification');
            
            let autoRedirectHtml = '';
            if (config.auto_redirect_enabled) {
                autoRedirectHtml = `
                    <div class="auto-redirect">
                        <span class="countdown" id="countdown">${config.auto_redirect_delay}</span>초 후 자동으로 음식 등록 페이지로 이동합니다
                    </div>
                `;
            }
            
            notification.innerHTML = `
                <h3>🍽️ 음식 등록 알림</h3>
                <p>${data.message}</p>
                <p>📏 거리: ${data.distance}m | ⏱️ 체류: ${data.stay_duration}초</p>
                <div class="notification-buttons">
                    <button id="register-btn">📸 지금 등록하기</button>
                    <button id="cancel-btn">❌ 취소</button>
                </div>
                ${autoRedirectHtml}
            `;
            notification.style.display = 'block';

            // 버튼 이벤트 리스너
            document.getElementById('register-btn').onclick = function() {
                clearAutoRedirectTimer();
                notification.style.display = 'none';
                window.location.href = data.upload_url;
            };

            document.getElementById('cancel-btn').onclick = function() {
                clearAutoRedirectTimer();
                notification.style.display = 'none';
            };

            // 자동 리디렉션 타이머
            if (config.auto_redirect_enabled) {
                startAutoRedirectTimer(data.upload_url, config.auto_redirect_delay);
            }

            // 브라우저 알림
            if (window.Notification && Notification.permission === "granted") {
                new Notification('🍽️ 음식 등록 알림', {
                    body: data.message,
                    icon: 'https://cdn-icons-png.flaticon.com/512/3514/3514491.png'
                });
            }

            // 15초 후 자동 닫기 (자동 리디렉션이 없는 경우)
            if (!config.auto_redirect_enabled) {
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 15000);
            }
        }

        function startAutoRedirectTimer(url, seconds) {
            let timeLeft = seconds;
            const countdownElement = document.getElementById('countdown');
            
            autoRedirectTimer = setInterval(() => {
                timeLeft--;
                if (countdownElement) {
                    countdownElement.textContent = timeLeft;
                }
                
                if (timeLeft <= 0) {
                    clearAutoRedirectTimer();
                    window.location.href = url;
                }
            }, 1000);
        }

        function clearAutoRedirectTimer() {
            if (autoRedirectTimer) {
                clearInterval(autoRedirectTimer);
                autoRedirectTimer = null;
            }
        }

        // 브라우저 알림 권한 요청
        if (window.Notification && Notification.permission !== "granted") {
            Notification.requestPermission();
        }

        // === Enhanced SocketIO Event Handlers ===
        socket.on('status_update', (data) => {
            const statusText = data.status === 'entered' ? '🚶‍♂️ 입장' : '👋 퇴장';
            const type = data.status === 'entered' ? 'entered' : 'left';
            
            let message = `${statusText} ${data.restaurant} (거리: ${data.distance}m)`;
            if (data.stay_duration) {
                message += ` - 체류: ${data.stay_duration}초`;
            }
            
            addStatusMessage(message, type);
        });

        socket.on('notification', (data) => {
            addStatusMessage(`🔔 알림 발송: ${data.restaurant} (거리: ${data.distance}m, 체류: ${data.stay_duration}초)`, 'notification');
            showNotification(data);
        });

        socket.on('config_update', (data) => {
            // 서버에서 설정 업데이트를 받았을 때
            Object.assign(config, data);
            
            // UI 업데이트
            document.getElementById('detection-radius-display').textContent = `${config.detection_radius}m`;
            document.getElementById('stay-time-display').textContent = `${config.required_stay_time}초`;
            document.getElementById('update-interval-display').textContent = `${config.location_update_interval}ms`;
            
            // 사용자 반경 업데이트
            if (userCircle) {
                userCircle.setRadius(config.detection_radius);
            }
            
            addStatusMessage(`⚙️ 설정 업데이트: 반경 ${config.detection_radius}m, 체류시간 ${config.required_stay_time}초`, 'debug');
        });

        socket.on('connect', () => {
            addStatusMessage('🔌 서버 연결됨');
        });

        socket.on('disconnect', () => {
            addStatusMessage('🔌 서버 연결 해제됨');
        });

        socket.on('error', (data) => {
            addStatusMessage(`❌ 오류: ${data.message}`);
        });

        // 설정된 간격으로 위치 전송
        setInterval(sendLocationUpdate, config.location_update_interval);

        // 카카오맵 로드 후 초기화
        kakao.maps.load(() => {
            initMap();
            // 기본 드래그 모드로 설정
            setLocationMethod('drag');
            addStatusMessage('🚀 시스템 초기화 완료');
        });
    </script>
</body>
</html>
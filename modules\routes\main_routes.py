# -*- coding: utf-8 -*-
"""
Main Routes
메인 페이지, 대시보드, 프로필 관련 라우트
"""

from flask import Blueprint, render_template, request, jsonify, redirect, url_for
from datetime import datetime, date
import logging

from modules.config import config
from modules.utils import (
    load_json_file, save_json_file, filter_records_by_date,
    calculate_nutrition_summary, calculate_weekly_data, safe_float_conversion
)

logger = logging.getLogger(__name__)

# Blueprint 생성
main_bp = Blueprint('main', __name__)


@main_bp.route('/')
def index():
    """메인 페이지 - 지도 및 위치 추적 + 오늘 요약"""
    try:
        # 음식 기록 불러오기
        records = load_json_file('food_records.json', [])
        
        # 날짜별 분석
        today = datetime.now().date()
        today_records = filter_records_by_date(records, today)
        
        # 오늘 요약
        today_summary = calculate_nutrition_summary(today_records)
        today_summary['date'] = today.strftime('%Y-%m-%d')
        
        # 음식점 데이터 로드
        restaurants = load_json_file('restaurants.json', [])
        
        # 설정 정보를 템플릿에 전달
        template_config = config.get_template_config()
        
        return render_template('index.html', 
                             restaurants=restaurants, 
                             config=template_config,
                             today=today_summary)
                             
    except Exception as e:
        logger.error(f"메인 페이지 로드 오류: {e}")
        return f"페이지 로드 중 오류가 발생했습니다: {e}", 500


@main_bp.route('/dashboard')
def dashboard():
    """대시보드 페이지 - 통계 및 분석"""
    try:
        # 음식 기록 불러오기
        records = load_json_file('food_records.json', [])
        
        # 오늘 요약
        today = datetime.now().date()
        today_records = filter_records_by_date(records, today)
        today_summary = calculate_nutrition_summary(today_records)
        today_summary['date'] = today.strftime('%Y-%m-%d')
        
        # 주간 데이터
        weekly_data = calculate_weekly_data(records, today)
        
        # 주간 요약 계산
        total_weekly_calories = sum(day['total_calories'] for day in weekly_data)
        total_weekly_protein = sum(day['total_protein'] for day in weekly_data)
        total_weekly_meals = sum(day['meal_count'] for day in weekly_data)
        
        weekly_summary = {
            'weekly_data': weekly_data,
            'total_calories': total_weekly_calories,
            'total_protein': total_weekly_protein,
            'total_meals': total_weekly_meals,
            'avg_daily_calories': total_weekly_calories / 7 if total_weekly_calories > 0 else 0
        }
        
        # 최근 음식 기록 (최근 순으로 정렬)
        recent_records = sorted(records, 
                              key=lambda x: x.get('timestamp', ''), 
                              reverse=True)[:20]
        
        # 프로필 불러오기
        profile = load_json_file('user_profile.json')
        logger.info(f"🔍 프로필 로드 결과: {profile is not None}")
        if profile:
            logger.info(f"📊 프로필 데이터: 체중={profile.get('current_weight')}, 목표칼로리={profile.get('daily_calorie_goal')}")

        if not profile:
            # 프로필이 없는 경우 기본값 사용
            logger.warning("⚠️ 프로필이 없어 기본값 사용")
            profile = {
                'current_weight': 70,
                'target_weight': 65,
                'daily_calorie_goal': 2000,
                'days_remaining': 30,
                'recent_avg_calories': today_summary['total_calories'],
                'on_track': True
            }
        
        return render_template('dashboard.html', 
                             today=today_summary,
                             weekly=weekly_summary,
                             recent_foods=recent_records,
                             progress=profile)
                             
    except Exception as e:
        logger.error(f"대시보드 로드 오류: {e}")
        # 오류 발생 시 기본 데이터로 페이지 로드
        return render_template('dashboard.html', 
                             today={'date': date.today().strftime('%Y-%m-%d'), 
                                   'total_calories': 0, 'total_protein': 0, 
                                   'total_carbs': 0, 'total_fat': 0, 'meal_count': 0},
                             weekly={'weekly_data': [], 'total_calories': 0, 
                                    'total_protein': 0, 'total_meals': 0, 'avg_daily_calories': 0},
                             recent_foods=[],
                             progress=None)


@main_bp.route('/test')
def test():
    """테스트 페이지"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Kakao Map Test</title>
        <script type="text/javascript" src="https://dapi.kakao.com/v2/maps/sdk.js?appkey=b5c3aa43720c70475087f256859be76a&libraries=services"></script>
    </head>
    <body>
        <h1>Kakao Map Test</h1>
        <div id="map" style="width:100%;height:400px;"></div>

        <script>
            console.log("🚀 테스트 페이지 로드 시작");
            console.log("🗺️ Kakao 객체:", typeof kakao);

            function initTestMap() {
                console.log("🗺️ 테스트 지도 초기화 시작");

                if (typeof kakao === 'undefined') {
                    console.error("❌ Kakao 객체가 없습니다");
                    return;
                }

                if (typeof kakao.maps === 'undefined') {
                    console.error("❌ Kakao.maps 객체가 없습니다");
                    return;
                }

                var container = document.getElementById('map');
                var options = {
                    center: new kakao.maps.LatLng(35.830569788, 128.75399385),
                    level: 3
                };

                var map = new kakao.maps.Map(container, options);
                console.log("✅ 테스트 지도 생성 완료");
            }

            // Kakao Maps 로드 대기
            function waitForKakao() {
                if (typeof kakao !== 'undefined' && kakao.maps) {
                    console.log("✅ Kakao Maps API 로드 완료");
                    kakao.maps.load(initTestMap);
                } else {
                    console.log("⏳ Kakao Maps API 로드 대기 중...");
                    setTimeout(waitForKakao, 100);
                }
            }

            document.addEventListener('DOMContentLoaded', function() {
                console.log("📄 DOM 로드 완료");
                waitForKakao();
            });
        </script>
    </body>
    </html>
    '''

@main_bp.route('/simple')
def simple_map():
    """간단한 지도 페이지 - 하드코딩 버전"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>간단 지도 테스트</title>
        <script type="text/javascript" src="https://dapi.kakao.com/v2/maps/sdk.js?appkey=65093059a1b8c894bd576e566c080e3d&libraries=services"></script>
    </head>
    <body>
        <h1>🗺️ 간단 지도 테스트</h1>
        <div id="map" style="width:100%;height:400px;border:2px solid red;"></div>
        <div id="debug" style="margin-top:20px;padding:10px;background:#f0f0f0;"></div>

        <script>
            console.log("🚀 하드코딩 지도 테스트 시작");

            function log(msg) {
                console.log(msg);
                document.getElementById('debug').innerHTML += '<p>' + msg + '</p>';
            }

            log("📊 Kakao 객체: " + typeof kakao);

            function initMap() {
                log("🗺️ 지도 초기화 시작");

                try {
                    if (typeof kakao === 'undefined') {
                        throw new Error('Kakao 객체가 없습니다');
                    }

                    const container = document.getElementById('map');
                    const options = {
                        center: new kakao.maps.LatLng(35.830569788, 128.75399385),
                        level: 3
                    };

                    const map = new kakao.maps.Map(container, options);
                    log("✅ 지도 생성 완료!");

                    // 테스트 마커 추가
                    const marker = new kakao.maps.Marker({
                        position: new kakao.maps.LatLng(35.830569788, 128.75399385),
                        map: map
                    });
                    log("📍 마커 추가 완료!");

                } catch (error) {
                    log("❌ 오류: " + error.message);
                }
            }

            function waitForKakao() {
                if (typeof kakao !== 'undefined' && kakao.maps) {
                    log("✅ Kakao Maps API 로드 완료");
                    kakao.maps.load(initMap);
                } else {
                    log("⏳ Kakao Maps API 로드 대기 중...");
                    setTimeout(waitForKakao, 100);
                }
            }

            document.addEventListener('DOMContentLoaded', function() {
                log("📄 DOM 로드 완료");
                waitForKakao();
            });
        </script>
    </body>
    </html>
    '''

@main_bp.route('/profile', methods=['GET'])
def profile():
    """프로필 설정 페이지"""
    return render_template('profile.html')


@main_bp.route('/api/profile', methods=['GET', 'POST'])
def api_profile():
    """프로필 API - 조회 및 저장"""
    if request.method == 'GET':
        # 프로필 조회
        try:
            profile = load_json_file('user_profile.json')
            return jsonify({
                'success': True,
                'data': profile
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'error': str(e)
            }), 500
    
    elif request.method == 'POST':
        # 프로필 저장
        try:
            data = request.get_json()
            
            # 데이터 유효성 검사
            required_fields = ['current_weight', 'height', 'age', 'gender', 'target_weight', 'target_date', 'activity_level']
            for field in required_fields:
                if field not in data or not data[field]:
                    return jsonify({
                        'success': False,
                        'error': f'{field} 필드가 필요합니다.'
                    }), 400
            
            # 프로필 계산 및 저장
            profile = calculate_profile_metrics(data)
            
            if save_json_file(profile, 'user_profile.json'):
                return jsonify({
                    'success': True,
                    'message': '프로필이 성공적으로 저장되었습니다.',
                    'data': profile
                })
            else:
                return jsonify({
                    'success': False,
                    'error': '프로필 저장에 실패했습니다.'
                }), 500
            
        except Exception as e:
            logger.error(f"프로필 저장 오류: {e}")
            return jsonify({
                'success': False,
                'error': f'프로필 저장 중 오류가 발생했습니다: {str(e)}'
            }), 500


def calculate_profile_metrics(profile_data: dict) -> dict:
    """프로필 데이터에 기반한 BMR, TDEE 등 계산"""
    try:
        # 안전한 숫자 변환
        weight = safe_float_conversion(profile_data.get('current_weight', 70))
        height = safe_float_conversion(profile_data.get('height', 170))
        age = int(safe_float_conversion(profile_data.get('age', 30)))
        gender = profile_data.get('gender', 'male')
        activity_level = profile_data.get('activity_level', 'moderate')
        target_weight = safe_float_conversion(profile_data.get('target_weight', 65))
        target_date = datetime.strptime(profile_data['target_date'], '%Y-%m-%d').date()
        
        # BMR 계산 (Harris-Benedict equation)
        if gender == 'male':
            bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age)
        else:
            bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age)
        
        # TDEE 계산
        activity_multipliers = {
            'sedentary': 1.2,      # 운동 안함
            'light': 1.375,        # 가벼운 운동 (주 1-3회)
            'moderate': 1.55,      # 보통 운동 (주 3-5회)
            'active': 1.725,       # 활발한 운동 (주 6-7회)
            'very_active': 1.9     # 매우 활발 (하루 2회 운동 또는 육체 노동)
        }
        tdee = bmr * activity_multipliers.get(activity_level, 1.2)
        
        # 목표 칼로리 계산
        today = datetime.now().date()
        days_to_target = (target_date - today).days
        weight_change = target_weight - weight
        
        # 1kg = 7700kcal (지방)
        total_calorie_change = weight_change * 7700
        daily_calorie_adjustment = total_calorie_change / days_to_target if days_to_target > 0 else 0
        daily_calorie_goal = tdee + daily_calorie_adjustment
        
        # 안전 범위 체크
        min_safe = bmr * 0.8  # BMR의 80% 이하로는 내려가지 않음
        max_safe = tdee * 1.2  # TDEE의 120% 이상으로는 올라가지 않음
        daily_calorie_goal = max(min_safe, min(max_safe, daily_calorie_goal))
        
        # 최근 평균 칼로리 계산
        records = load_json_file('food_records.json', [])
        recent_records = [r for r in records if 
                         (datetime.now() - datetime.fromisoformat(r['timestamp'])).days <= 7]
        recent_avg_calories = sum(safe_float_conversion(r.get('calories', 0)) for r in recent_records) / 7 if recent_records else 0
        
        # 목표 달성 여부
        on_track = abs(recent_avg_calories - daily_calorie_goal) <= daily_calorie_goal * 0.1  # 10% 오차 허용
        
        # 프로필 데이터 준비
        profile = {
            **profile_data,
            'bmr': round(bmr, 1),
            'tdee': round(tdee, 1),
            'daily_calorie_goal': round(daily_calorie_goal, 1),
            'days_remaining': days_to_target,
            'weight_change_needed': round(weight_change, 1),
            'recent_avg_calories': round(recent_avg_calories, 1),
            'on_track': on_track,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        logger.info(f"프로필 계산 완료: {daily_calorie_goal:.0f} kcal/일")
        
        return profile
        
    except Exception as e:
        logger.error(f"프로필 계산 오류: {e}")
        raise e


@main_bp.route('/api/add_sample_data', methods=['POST'])
def add_sample_data_api():
    """운동 기능 테스트를 위한 고칼로리 샘플 데이터 추가"""
    try:
        from datetime import timedelta
        
        # 고칼로리 음식 데이터 (총 4150 kcal)
        sample_foods = [
            {
                'id': 1,
                'restaurant_name': '맥도널드',
                'food_name': '빅맥 세트',
                'food_name_ko': '빅맥 세트',
                'food_name_en': 'Big Mac Set',
                'calories': 1100,
                'protein': 27,
                'carbs': 70,
                'fat': 43,
                'weight_grams': 400,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=12, minute=30).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 2,
                'restaurant_name': '피자헛',
                'food_name': '콤비네이션 피자 (라지)',
                'food_name_ko': '콤비네이션 피자',
                'food_name_en': 'Combination Pizza Large',
                'calories': 800,
                'protein': 35,
                'carbs': 85,
                'fat': 35,
                'weight_grams': 300,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=18, minute=0).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 3,
                'restaurant_name': '스타벅스',
                'food_name': '화이트 초콜릿 모카 + 스콘',
                'food_name_ko': '화이트 초콜릿 모카 + 스콘',
                'food_name_en': 'White Chocolate Mocha + Scone',
                'calories': 650,
                'protein': 12,
                'carbs': 78,
                'fat': 28,
                'weight_grams': 250,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=15, minute=30).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 4,
                'restaurant_name': '한정식집',
                'food_name': '한정식 (대형) + 디저트',
                'food_name_ko': '한정식 + 디저트',
                'food_name_en': 'Korean Course Meal + Dessert',
                'calories': 900,
                'protein': 45,
                'carbs': 95,
                'fat': 38,
                'weight_grams': 500,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=19, minute=30).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            },
            {
                'id': 5,
                'restaurant_name': '던킨도너츠',
                'food_name': '도너츠 6개 + 라떼',
                'food_name_ko': '도너츠 6개 + 라떼',
                'food_name_en': '6 Donuts + Latte',
                'calories': 700,
                'protein': 15,
                'carbs': 120,
                'fat': 35,
                'weight_grams': 350,
                'nutrition_source': 'sample_data',
                'confidence': 1.0,
                'timestamp': datetime.now().replace(hour=10, minute=0).isoformat(),
                'image_path': None,
                'method': 'sample_data'
            }
        ]
        
        # 샘플 프로필
        sample_profile = {
            'current_weight': 75,
            'height': 175,
            'age': 28,
            'gender': 'male',
            'activity_level': 'moderate',
            'target_weight': 70,
            'target_date': (datetime.now() + timedelta(days=60)).strftime('%Y-%m-%d'),
            'bmr': 1681.0,
            'tdee': 2605.5,
            'daily_calorie_goal': 2105.5,
            'days_remaining': 60,
            'weight_change_needed': -5.0,
            'recent_avg_calories': 4150,
            'on_track': False,
            'created_at': datetime.now().isoformat(),
            'updated_at': datetime.now().isoformat()
        }
        
        # 파일 저장
        save_json_file(sample_foods, 'food_records.json')
        save_json_file(sample_profile, 'user_profile.json')
        
        total_calories = sum(food['calories'] for food in sample_foods)
        excess_calories = total_calories - sample_profile['tdee']
        
        logger.info(f"샘플 데이터 추가 완료: {total_calories} kcal, 초과: {excess_calories:.0f} kcal")
        
        return jsonify({
            'success': True,
            'message': f'운동 기능 테스트용 샘플 데이터가 추가되었습니다!',
            'data': {
                'food_count': len(sample_foods),
                'total_calories': total_calories,
                'tdee': sample_profile['tdee'],
                'excess_calories': excess_calories,
                'profile_created': True
            }
        })
        
    except Exception as e:
        logger.error(f"샘플 데이터 추가 실패: {e}")
        return jsonify({
            'success': False,
            'error': f'샘플 데이터 추가 실패: {str(e)}'
        }), 500

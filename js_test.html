<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Test</title>
</head>
<body>
    <h1>JavaScript 작동 테스트</h1>
    <div id="result">JavaScript가 실행되지 않았습니다</div>
    <button onclick="testClick()">클릭 테스트</button>
    
    <script>
        // 즉시 실행
        console.log("JavaScript 실행됨!");
        document.getElementById('result').innerHTML = "✅ JavaScript가 정상 작동합니다!";
        
        // 클릭 테스트 함수
        function testClick() {
            alert("JavaScript 클릭 이벤트 작동!");
            console.log("클릭 이벤트 실행됨");
        }
        
        // 외부 스크립트 로드 테스트
        function loadExternalScript() {
            console.log("외부 스크립트 로드 시도...");
            
            const script = document.createElement('script');
            script.src = 'https://code.jquery.com/jquery-3.6.0.min.js';
            
            script.onload = function() {
                console.log("✅ 외부 스크립트 로드 성공");
                document.getElementById('result').innerHTML += "<br>✅ 외부 스크립트 로드 성공";
            };
            
            script.onerror = function() {
                console.log("❌ 외부 스크립트 로드 실패");
                document.getElementById('result').innerHTML += "<br>❌ 외부 스크립트 로드 실패";
            };
            
            document.head.appendChild(script);
        }
        
        // 3초 후 외부 스크립트 테스트
        setTimeout(loadExternalScript, 3000);
    </script>
</body>
</html>

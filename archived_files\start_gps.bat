@echo off
title AI Project GPS Starter
color 0A

echo ========================================
echo    AI Project GPS Quick Starter
echo ========================================
echo.

:: 현재 디렉토리 확인
echo [1/4] 현재 디렉토리 확인 중...
cd /d %~dp0
echo 현재 위치: %CD%
echo.

:: Python 확인
echo [2/4] Python 확인 중...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [오류] Python이 설치되지 않았거나 PATH에 없습니다.
    echo Python 3.8 이상을 설치하세요.
    pause
    exit /b 1
)
python --version
echo.

:: 가상환경 확인
echo [3/4] 가상환경 확인 중...
if exist "venv\Scripts\activate.bat" (
    echo 가상환경 발견! 활성화 중...
    call venv\Scripts\activate
) else (
    echo 가상환경이 없습니다. 전역 Python을 사용합니다.
)
echo.

:: 환경 검증
echo [4/4] 환경 설정 검증 중...
if exist "verify_install.py" (
    python verify_install.py
    echo.
    echo ========================================
    
    :: 사용자 입력 대기
    echo.
    set /p continue="계속하시겠습니까? (Y/N): "
    if /i "%continue%" neq "Y" (
        echo 종료합니다.
        pause
        exit /b 0
    )
) else (
    echo verify_install.py 파일이 없어 검증을 건너뜁니다.
)

echo.
echo ========================================
echo    서버 시작
echo ========================================
echo.

:: 서버 시작
echo AI Project GPS 서버를 시작합니다...
echo.
echo 주요 기능:
echo - 실시간 위치 추적 (감지 반경: 15m)
echo - 음식점 체류 감지 (3초 이상)
echo - AI 음식 인식 및 칼로리 분석
echo - 한국 식품의약품안전처 영양정보 DB 연동
echo.
echo 브라우저에서 http://localhost:5000 접속
echo 종료하려면 Ctrl+C를 누르세요.
echo.
echo ========================================
echo.

:: app.py 실행
python app.py

:: 오류 발생 시
if %errorlevel% neq 0 (
    echo.
    echo [오류] 서버 시작 실패!
    echo 위의 오류 메시지를 확인하세요.
    echo.
    pause
)

:: 정상 종료
echo.
echo 서버가 종료되었습니다.
pause
